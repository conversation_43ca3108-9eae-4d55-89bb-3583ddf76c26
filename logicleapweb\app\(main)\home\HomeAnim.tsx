'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useRef } from 'react';
import EnergyModal from './components/EnergyModal';
import { Modal } from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import PackageModal, { PackageInfo } from './components/PackageModal';
import { WS_URL } from '@/config/config';
import { getDefaultWebSocket, WebSocketClient } from 'logic-common/dist/api/web_socket/webSocket';
import { createTab } from 'logic-common/dist/utils/tab/Tab';
import { getNetworkDetector } from '@/lib/utils/NetworkDetector';


interface HomeAnimContextType {
  points?: number;
  webSocket?: WebSocketClient | null;
  isOnline: boolean;
}

// 声明自定义事件类型，确保TypeScript能识别这些事件
declare global {
  interface WindowEventMap {
    pointsUpdate: CustomEvent<{ points: number, change: number, fromCurrentTab?: boolean }>;
    packageAssigned: CustomEvent<{ packageInfo: PackageInfo }>;
    networkStatusChange: CustomEvent<{ isOnline: boolean }>;
  }
}

const HomeAnimContext = createContext<HomeAnimContextType>({
  isOnline: true
});

/**
 * 首页动画系统提供者组件
 * 1. 管理WebSocket连接，接收服务器推送的能量消息
 * 2. 显示能量增加动画和通知
 */
export function HomeAnimProvider({ children }: { children: ReactNode }) {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentPoints, setCurrentPoints] = useState(0);
  const [pointsChange, setPointsChange] = useState(0);
  const [modalKey, setModalKey] = useState(0);
  // 添加WebSocket状态变量
  const [webSocketInstance, setWebSocketInstance] = useState<WebSocketClient | null>(null);

  // 新增套餐弹窗状态
  const [packageModalVisible, setPackageModalVisible] = useState(false);
  const [packageInfo, setPackageInfo] = useState<PackageInfo | null>(null);
  const [packageModalKey, setPackageModalKey] = useState(0);

  // 新增网络状态
  const [isOnline, setIsOnline] = useState(true);

  const webSocketRef = useRef<WebSocketClient | null>(null);
  const networkDetectorRef = useRef<ReturnType<typeof getNetworkDetector> | null>(null);

  const getTabId = (): string => {
    const tempTabId = createTab('home');
    if (!localStorage.getItem('tabId')) {
      localStorage.setItem('tabId', tempTabId);
    }

    return localStorage.getItem('tabId') || tempTabId;
  }

  const reConnectWebSocket = (webSocket: WebSocketClient) => {

    webSocket.addTab(getTabId());
    console.log('WebSocketInfo', webSocket.getConnectionInfo());
    const userId = JSON.parse(localStorage.getItem('user') || '{}').id;
    //发送查询离线消息
    setTimeout(() => {
      webSocket.emit('queryOfflinePoints', {
        userId: userId,
      });
    }, 2000);

  }

  const initWebSocket = () => {
    let token = localStorage.getItem('token');


    if (!token) {
      // console.log('未找到token，无法连接上Websocket');
    }
    token = token || '';

    if (webSocketRef.current) {

      if (!localStorage.getItem('token')) {
        // console.log('未找到token，无法连接上Websocket');
        return;
      }

      if (!webSocketRef.current.isConnected()) {
        const webSocket = getDefaultWebSocket({
          url: WS_URL,
          token,
        });
        webSocketRef.current = webSocket;
        // 更新状态变量以触发Context更新
        setWebSocketInstance(webSocket);

        // 更新网络检测器中的WebSocket对象
        if (networkDetectorRef.current) {
          networkDetectorRef.current.setWebSocket(webSocket);
        }

        reConnectWebSocket(webSocket);
      }
    } else {
      const webSocket = getDefaultWebSocket({
        url: WS_URL,
        token,
      });

      webSocketRef.current = webSocket;
      // 更新状态变量以触发Context更新
      setWebSocketInstance(webSocket);

      // 初始化网络检测器
      networkDetectorRef.current = getNetworkDetector({
        webSocket,
        pingUrl: '/api/web/user/info/ping',
        pingIntervalMs: 30000, // 每30秒检测一次
      });


      // 添加事件
      webSocket.on("assignPoints", (data: any) => {
        console.log('收到能量消息', data);

        // 显示积分变更弹窗
        setModalVisible(false);
        setTimeout(() => {
          setCurrentPoints(Number(data.points));
          setPointsChange(Number(data.change));
          setModalKey(prev => prev + 1);
          setModalVisible(true);
        }, 100);

        // 保存积分到本地存储并触发事件
        localStorage.setItem('userPoints', String(data.points));
        window.dispatchEvent(new CustomEvent('pointsUpdate', {
          detail: {
            points: data.points,
            change: data.change
          }
        }));
      });

      webSocket.on("packageAssigned", (data: any) => {
        // 处理套餐分配消息
        console.log('收到套餐分配消息', data);

        // 显示套餐分配弹窗
        setPackageModalVisible(false);
        setTimeout(() => {
          setPackageInfo(data.packageInfo);
          setPackageModalKey(prev => prev + 1);
          setPackageModalVisible(true);
        }, 100);

        // 触发包分配自定义事件
        window.dispatchEvent(new CustomEvent('packageAssigned', {
          detail: {
            packageInfo: data.packageInfo
          }
        }));
      });

      webSocket.on("error", (error: any) => {
        console.error('WebSocket 错误:', error);
      });
    }

  }

  useEffect(() => {
    const timer = setInterval(() => {
      initWebSocket();
    }, 1000);

    return () => {
      clearInterval(timer);
      // 清理网络检测器资源
      if (networkDetectorRef.current) {
        networkDetectorRef.current.destroy();
      }
    };
  }, []);

  // 监听网络状态变化
  useEffect(() => {
    if (networkDetectorRef.current) {
      const handleNetworkChange = (status: boolean) => {
        setIsOnline(status);

        // 触发全局网络状态变化事件
        window.dispatchEvent(new CustomEvent('networkStatusChange', {
          detail: { isOnline: status }
        }));

        // 如果网络恢复，尝试重新连接WebSocket
        if (status && webSocketRef.current && !webSocketRef.current.isConnected()) {
          initWebSocket();
        }

        console.log('网络状态变化:', status ? '在线' : '离线');
      };

      networkDetectorRef.current.addListener(handleNetworkChange);

      // 返回清理函数
      return () => {
        if (networkDetectorRef.current) {
          networkDetectorRef.current.removeListener(handleNetworkChange);
        }
      };
    }
  }, []);

  const handleModalClose = () => {
    setModalVisible(false);
    setModalKey(prev => prev + 1);
  };

  // 添加能量更新处理函数
  const handleEnergyUpdated = (newPoints: number) => {
    // 更新本地能量值
    setCurrentPoints(newPoints);

    // 更新本地存储
    localStorage.setItem('userPoints', String(newPoints));

    // 触发全局事件，通知其他组件更新能量显示
    window.dispatchEvent(new CustomEvent('pointsUpdate', {
      detail: {
        points: newPoints,
        change: pointsChange
      }
    }));
  };

  const handlePackageModalClose = () => {
    setPackageModalVisible(false);
    setPackageModalKey(prev => prev + 1);
  };

  return (
    <HomeAnimContext.Provider value={{
      points: currentPoints,
      webSocket: webSocketInstance, // 使用状态变量而非ref.current
      isOnline
    }}>
      {children}
      <EnergyModal
        key={modalKey + `energeyUniqueKey${new Date()}`}
        visible={modalVisible}
        points={currentPoints}
        change={pointsChange}
        onClose={handleModalClose}
        onEnergyUpdated={handleEnergyUpdated}
      />
      <PackageModal
        key={packageModalKey}
        visible={packageModalVisible}
        packageInfo={packageInfo}
        onClose={handlePackageModalClose}
      />
    </HomeAnimContext.Provider>
  );
}

export const useHomeAnim = () => useContext(HomeAnimContext);

export default HomeAnimProvider; 