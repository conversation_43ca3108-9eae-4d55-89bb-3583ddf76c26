# 微信公众号配置
# appid: wx2b5d05a3dfebae30
# appsecret: 06ce7054489695e4b49caa0b715e78d1
# token: b3337731ab0711ef8c1fe79208535f88
# encodingAESKey: Hha4Su7QiNUEdj1jow5oQZtcIXVshfUXSClXRCpD1am

# 公众号测试号
# appid: wxeac644b6acef0405
# appsecret: 22681f0abf2490d0853b5675905b557b
# token: b3337731ab0711ef8c1fe79208535f88
# encodingAESKey: Hha4Su7QiNUEdj1jow5oQZtcIXVshfUXSClXRCpD1am
# 公众号测试号2
appid: wxeac644b6acef0405
appsecret: 22681f0abf2490d0853b5675905b557b
token: b3337731ab0711ef8c1fe79208535f88
encodingAESKey: Hha4Su7QiNUEdj1jow5oQZtcIXVshfUXSClXRCpD1am

# 扫码登录配置
scan_login:
  qrcode_expire: 300 # 二维码过期时间（秒）
  auth_expire: 120 # 授权有效期（秒）程

# 微信回调URL配置
callback_url:
  # 暂时注释
  #  base: https://logicleapai.cn
  # base: http://7uz4301cb969.vicp.fun
  base: http://10xh9vd648325.vicp.fun
  scan: /weixin-scan/callback
  message: /weixin/message
# # 前端URL配置
# frontend:
#   login_success_url: http://localhost:3000/login-success
#   bind_success_url: http://localhost:3000/bind-success
#   bind_url: http://localhost:3000/weixin-bind
