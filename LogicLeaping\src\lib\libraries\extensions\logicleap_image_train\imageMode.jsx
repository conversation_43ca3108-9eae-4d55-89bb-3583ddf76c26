import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle, useCallback } from 'react';

import notification from '../utils/trainNotification';

import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
import TrainMain from '../utils/trainUtil/trainMain/trainMain.jsx';
import ClassCardCss from '../utils/trainUtil/ClassCard/ClassCardCss.css';
import ClassCardStyle from '../utils/trainUtil/ClassCard/ClassCardStyle';
import CameraCardStyle from '../utils/trainUtil/ClassCard/CameraCardStyle';

// 导入新的 ossApi 和 request
import ossApi from '@api/OSS_Upload.ts';
// 移除 request 导入
// import request from '@api/request.ts'; // 如果需要 request，也用别名
// 导入新的 imageModelApi
// 导入 workApi 代替 imageModelApi
import workApi from '@api/work_api.ts';

const OSS = require('ali-oss'); // 这个可能不再需要，除非还有其他地方用
// 移除 API_URL require
// const { API_URL } = require('../../../../config/config.js');

// 根据传入的标签初始化类别节点
const getInitialClassNodes = (labels) => {
    if (labels && Array.isArray(labels) && labels.length > 0) {
        return labels.map((label, index) => ({
            id: index,
            name: label || `类别 ${index + 1}`, // 如果标签为空，则使用默认名称
            samples: [],
            expanded: false
        }));
    }
    // 默认类别
    return [
        { id: 0, name: '类别 1', samples: [], expanded: false },
        { id: 1, name: '类别 2', samples: [], expanded: false }
    ];
};

const ImageMode = ({modeRef, modeString, initialClassLabels}) => {
    //ref元素
    const containerRef = useRef(null);
    const classesPanelContentRef = useRef(null);
    const trainMainFunctionRef = useRef(null);


   //类别参数
   const [classNodes, setClassNodes] = useState(() => getInitialClassNodes(initialClassLabels));
   const currentRecordIndexRef=useRef(-1);
   const currentClassRef=useRef(-1);
   // 添加 classNodesRef 来实时跟踪 classNodes 的变化
   const classNodesRef = useRef(getInitialClassNodes(initialClassLabels));
   
    // 使用 useEffect 同步 classNodes 状态到 ref
    useEffect(() => {
        classNodesRef.current = classNodes;
    }, [classNodes]);

    //训练参数
    //是否训练
    const [isTraining, setIsTraining] = useState(false);
    //训练进度
    const [training, setTraining] = useState(-1);
    //高级选项展开/缩放
    const [advancedExpanded, setAdvancedExpanded] = useState(false);
    //训练参数
    const [DefaultTrainParams] = useState({
        epochs: 5,
        batchSize: 8,
        learningRate: 0.005
    });
    const [trainParams, setTrainParams] = useState(DefaultTrainParams);
    const canTrainRef = useRef(false);
    //训练参数
    const [showDataChart, setShowDataChart] = useState(false);

    // 添加训练历史记录状态
    const [trainingHistory, setTrainingHistory] = useState({
        epochs: [],
        trainAccuracy: [],
        trainLoss: [],
        valAccuracy: [],
        valLoss: []
    });
    const [hasData, setHasData] = useState(false);
    
    // 添加图表引用
    const accChartRef = useRef(null);
    const lossChartRef = useRef(null);

    //预览参数
    const [predicting, setPredicting] = useState(false);
    const [confidences, setConfidences] = useState([]);
    const [modelTrained, setModelTrained] = useState(false);
    // 预览相关状态
    const [previewInputSource, setPreviewInputSource] = useState('webcam'); // 默认输入源为摄像头
    const videoStreamRef = useRef(null);
    const predictionIntervalRef = useRef(null);

    // 摄像头和录制设置相关
    const facingModeRef = useRef('user'); // 前置摄像头默认值
    const recordSettingsRef = useRef({
        holdToRecord: true, // 默认为按住录制模式
        canSpaceRecord: true, // 是否可以按空格键录制
        delayTime: 0, // 默认无延迟开始
        duration: 0, // 默认不限制录制时长
        fps: 10 // 默认FPS
    });
    // 添加录制设置状态
    const [recordSettings, setRecordSettings] = useState({
        canSpaceRecord: true, // 是否可以按空格键录制
        holdToRecord: true, 
        delayTime: 0,
        duration: 3,
        fps: 10
    });
    // 设置面板显示状态
    const [showSettingsPanel, setShowSettingsPanel] = useState(false);
    
    // 初始化时加载设置
    useEffect(() => {
        try {
            const savedSettings = localStorage.getItem('imageRecordSettings');
            if (savedSettings) {
                const parsedSettings = JSON.parse(savedSettings);
                setRecordSettings(parsedSettings);
                recordSettingsRef.current = parsedSettings;
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }, []);
    
    // 监听设置变化，同步到ref
    useEffect(() => {
        recordSettingsRef.current = recordSettings;
    }, [recordSettings]);

    //knn相关的ref
    const knnRef = useRef(null);
    const mobilenetRef = useRef(null);
    const videoRef = useRef(null);
    const timerRef = useRef(null);
    const videoPlayingRef = useRef(false);
    
    // 用于KNN分类器的常量
    const TOPK = 3; // 预测时返回的top k个结果
    const IMAGE_SIZE = 224; // 图像大小，用于MobileNet模型

    // 添加一个引用来存储上次采集的时间戳
    const lastCaptureTimeRef = useRef(0);

    // 定时器相关的ref
    const recordingTimerRef = useRef(null);
    const startDelayTimerRef = useRef(null);

    // 暴露方法给父组件
    useImperativeHandle(modeRef, () => ({
        handleImportModel: () => {
        handleImportModel();
        },
        // 暴露下载模型方法
        handleExportToLocal: () => {
        handleExportToLocal();
        },
        // 暴露保存到云端方法
        handleExportToCloud: () => {
        handleExportToCloud();
        }
    }));

    // 暴露导入模型方法
    const handleImportModel = () => {
        try {
            // 创建文件输入框
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.json';
            fileInput.style.display = 'none';
            
            // 添加文件选择事件处理
            fileInput.onchange = async (event) => {
                if (!event.target.files || !event.target.files[0]) return;
                
                const file = event.target.files[0];
                
                try {
                    // 读取文件内容
                    const reader = new FileReader();
                    
                    // 设置加载完成后的处理函数
                    reader.onload = async (e) => {
                        try {
                            console.log('🐱 Claude 4.0 sonnet: 文件选择器导入 - 开始解析模型数据...');
                            console.log('🐱 原始数据长度:', e.target.result?.length);
                            console.log('🐱 原始数据类型:', typeof e.target.result);

                            // 解析JSON数据
                            const modelData = JSON.parse(e.target.result);
                            console.log('🐱 JSON解析成功，模型数据:', modelData);
                            
                            // 检查模型类型
                            if (!modelData.modelType || modelData.modelType !== 'knn-classifier') {
                                notification.error('不支持的模型类型，请导入KNN分类器模型');
                                return;
                            }
                            
                            // 检查模型数据
                            if (!modelData.dataset || !modelData.labels) {
                                notification.error('模型数据无效，缺少分类器数据集或标签');
                                return;
                            }
                            
                            // 编辑式导入：只导入图片数据，不导入KNN分类器数据
                            // KNN数据将在用户训练时重新生成
                            console.log('编辑式导入：跳过KNN分类器数据，只导入缩略图数据');
                            console.log(`模型标签:`, modelData.labels);
                            
                            // 根据模型标签重建类别节点
                            const newClassNodes = modelData.labels.map((label, index) => {
                                // 检查是否有对应的缩略图数据
                                let samples = [];
                                if (modelData.thumbnails && modelData.thumbnails[index]) {
                                    samples = modelData.thumbnails[index].map(thumbnail => ({
                                        id: thumbnail.id || Date.now() + Math.random(),
                                        imageUrl: thumbnail.imageUrl
                                    }));
                                }

                                return {
                                    id: index,
                                    name: label,
                                    samples: samples,
                                    expanded: false
                                };
                            });

                            // 计算总的样本数量
                            const totalSamples = newClassNodes.reduce((total, node) => total + node.samples.length, 0);

                            // 检查图片数量，如果超过30张则显示警告
                            if (totalSamples > 30) {
                                const confirmed = window.confirm(
                                    `该模型包含${totalSamples}个样本图片（超过30张），模型较大可能会导致机器卡顿。\n\n是否继续导入？`
                                );

                                if (!confirmed) {
                                    // 用户取消导入
                                    return;
                                }
                            }

                            // 用户确认导入，更新状态
                            setClassNodes(newClassNodes);

                            // 恢复训练参数（如果存在）
                            if (modelData.trainParams) {
                                setTrainParams(modelData.trainParams);
                                console.log('已恢复训练参数:', modelData.trainParams);
                            } else {
                                // 向后兼容：旧模型文件没有训练参数时使用默认值
                                setTrainParams(DefaultTrainParams);
                                console.log('使用默认训练参数（旧版本模型）');
                            }

                            // 注意：不设置 modelTrained = true，让用户手动训练
                            // 这样导入后不会自动展开预览节点

                            // 显示成功消息 - 强调这是数据导入，需要重新训练
                            const hasTrainParams = modelData.trainParams ? '训练参数已恢复，' : '';
                            if (totalSamples > 0) {
                                notification.success(`训练数据已成功导入！包含${newClassNodes.length}个类别，共${totalSamples}个样本图片。${hasTrainParams}请点击"开始训练"来训练模型。`);
                            } else {
                                notification.success(`模型结构已导入！包含${newClassNodes.length}个类别（旧版本模型，无样本缩略图）。${hasTrainParams}请添加样本后重新训练。`);
                            }
                            
                        } catch (error) {
                            console.error('解析模型数据失败:', error);
                            notification.error('解析模型数据失败: ' + error.message);
                        }
                    };
                    
                    // 读取文件
                    reader.readAsText(file);
                    
                } catch (error) {
                    console.error('读取文件失败:', error);
                    notification.error('读取文件失败: ' + error.message);
                }
            };
            
            // 添加到文档并触发点击
            document.body.appendChild(fileInput);
            fileInput.click();
            
            // 使用完成后移除
            setTimeout(() => {
                document.body.removeChild(fileInput);
            }, 100);
            
        } catch (error) {
            console.error('导入模型失败:', error);
            notification.error('导入模型失败: ' + error.message);
        }
    }

    // 暴露下载模型方法
    const handleExportToLocal = () => {
        // 检查模型是否已训练
        if (!modelTrained) {
            notification.error('请先训练模型');
            return;
        }
        
        try {
            // 检查KNN分类器是否已初始化
            if (!knnRef.current) {
                notification.error('模型未初始化，请重新训练');
                return;
            }
            
            // 获取训练好的分类器数据集
            const dataset = knnRef.current.getClassifierDataset();
            
            // 获取所有类别的标签
            const labels = classNodes.map(node => node.name || `类别 ${node.id + 1}`);

            // 收集每个类别的所有样本图片（不限制数量）
            const thumbnails = {};
            classNodes.forEach(node => {
                if (node.samples && node.samples.length > 0) {
                    // 导出所有样本图片
                    const sampleThumbnails = node.samples.map(sample => ({
                        id: sample.id,
                        imageUrl: sample.imageUrl
                    }));
                    thumbnails[node.id] = sampleThumbnails;
                }
            });

            // 将张量转换为可序列化的对象 - 添加量化处理以减小文件大小
            const tf = require('@tensorflow/tfjs');
            const serializedDataset = {};
            
            Object.keys(dataset).forEach((key) => {
                const tensor = dataset[key];
                // 找出数据范围以便进行更好的量化
                const values = tensor.dataSync();
                
                // 找出最大和最小值，用于量化
                let minVal = Number.POSITIVE_INFINITY;
                let maxVal = Number.NEGATIVE_INFINITY;
                for (let i = 0; i < values.length; i++) {
                    if (values[i] < minVal) minVal = values[i];
                    if (values[i] > maxVal) maxVal = values[i];
                }
                
                // 将32位浮点数量化为16位整数
                const range = maxVal - minVal;
                const quantizedValues = new Int16Array(values.length);
                for (let i = 0; i < values.length; i++) {
                    // 将值映射到0-65535范围
                    const normalizedVal = (values[i] - minVal) / range;
                    // 量化为16位整数
                    quantizedValues[i] = Math.round(normalizedVal * 32767);
                }
                
                // 保存量化后的数据和还原所需的信息
                serializedDataset[key] = {
                    quantizedValues: Array.from(quantizedValues),
                    shape: tensor.shape,
                    minVal: minVal,
                    maxVal: maxVal,
                    quantized: true
                };
            });
            
            // 构建完整的模型数据
            const modelData = {
                modelType: 'knn-classifier',
                dataset: serializedDataset,
                labels: labels,
                thumbnails: thumbnails, // 添加缩略图数据
                trainParams: trainParams, // 添加训练参数
                modelVersion: '1.4', // 升级版本号
                exportDate: new Date().toISOString(),
                compression: 'int16',
            };
            
            // 直接创建下载链接并下载
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(modelData));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "model.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            notification.success('模型已成功导出到本地');
        } catch (error) {
            console.error('导出模型失败:', error);
            notification.error(`导出模型失败: ${error.message}`);
        }
    }

    // 暴露保存到云端方法
    const handleExportToCloud = () => {
        // 检查模型是否已训练
        if (!modelTrained) {
            notification.error('请先训练模型');
            return;
        }

        try {
            // 检查KNN分类器是否已初始化
            if (!knnRef.current) {
                notification.error('模型未初始化，请重新训练');
            return;
        }
            
            // 获取训练好的分类器数据集
            const dataset = knnRef.current.getClassifierDataset();
            
            // 获取所有类别的标签
            const labels = classNodes.map(node => node.name || `类别 ${node.id + 1}`);

            // 收集每个类别的所有样本图片（不限制数量）
            const thumbnails = {};
            classNodes.forEach(node => {
                if (node.samples && node.samples.length > 0) {
                    // 导出所有样本图片
                    const sampleThumbnails = node.samples.map(sample => ({
                        id: sample.id,
                        imageUrl: sample.imageUrl
                    }));
                    thumbnails[node.id] = sampleThumbnails;
                }
            });

            // 将张量转换为可序列化的对象 - 添加量化处理以减小文件大小
            const tf = require('@tensorflow/tfjs');
            const serializedDataset = {};
            
            Object.keys(dataset).forEach((key) => {
                const tensor = dataset[key];
                // 找出数据范围以便进行更好的量化
                const values = tensor.dataSync();
                
                // 找出最大和最小值，用于量化
                let minVal = Number.POSITIVE_INFINITY;
                let maxVal = Number.NEGATIVE_INFINITY;
                for (let i = 0; i < values.length; i++) {
                    if (values[i] < minVal) minVal = values[i];
                    if (values[i] > maxVal) maxVal = values[i];
                }
                
                // 将32位浮点数量化为16位整数
                const range = maxVal - minVal;
                const quantizedValues = new Int16Array(values.length);
                for (let i = 0; i < values.length; i++) {
                    // 将值映射到0-65535范围
                    const normalizedVal = (values[i] - minVal) / range;
                    // 量化为16位整数
                    quantizedValues[i] = Math.round(normalizedVal * 32767);
                }
                
                // 保存量化后的数据和还原所需的信息
                serializedDataset[key] = {
                    quantizedValues: Array.from(quantizedValues),
                    shape: tensor.shape,
                    minVal: minVal,
                    maxVal: maxVal,
                    quantized: true
                };
            });
            
            // 构建完整的模型数据
            const modelData = {
                modelType: 'knn-classifier',
                dataset: serializedDataset,
                labels: labels,
                thumbnails: thumbnails, // 添加缩略图数据
                trainParams: trainParams, // 添加训练参数
                modelVersion: '1.4', // 升级版本号
                exportDate: new Date().toISOString(),
                compression: 'int16',
            };
            
            // 创建弹窗让用户输入模型名称和描述
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;
            
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 24px;
                width: 400px;
                max-width: 90%;
            `;
            
            const modalTitle = document.createElement('h3');
            modalTitle.textContent = '保存模型到云端';
            modalTitle.style.cssText = `
                margin-top: 0;
                margin-bottom: 16px;
                font-size: 18px;
                font-weight: 500;
            `;
            
            const nameLabel = document.createElement('label');
            nameLabel.textContent = '模型名称';
            nameLabel.style.cssText = `
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #333;
            `;
            
            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '请输入模型名称';
            nameInput.style.cssText = `
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                margin-bottom: 16px;
                box-sizing: border-box;
            `;
            
            const descLabel = document.createElement('label');
            descLabel.textContent = '模型描述';
            descLabel.style.cssText = `
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #333;
            `;
            
            const descInput = document.createElement('textarea');
            descInput.placeholder = '请输入模型描述（可选）';
            descInput.style.cssText = `
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                margin-bottom: 16px;
                box-sizing: border-box;
                min-height: 80px;
                resize: vertical;
            `;
            
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                justify-content: flex-end;
                gap: 12px;
            `;
            
            const cancelButton = document.createElement('button');
            cancelButton.textContent = '取消';
            cancelButton.style.cssText = `
                padding: 8px 16px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                background: white;
                cursor: pointer;
            `;
            
            const saveButton = document.createElement('button');
            saveButton.textContent = '保存';
            saveButton.style.cssText = `
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                background: #4766C2;
                color: white;
                cursor: pointer;
            `;
            
            // 添加取消按钮点击事件
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
            
            // 添加保存按钮点击事件
            saveButton.addEventListener('click', async () => {
                const modelName = nameInput.value.trim();
                const modelDesc = descInput.value.trim();
                
                if (!modelName) {
                    alert('请输入模型名称');
                    return;
                }
                
                // 禁用保存按钮
                saveButton.disabled = true;
                saveButton.textContent = '保存中...';
                
                try {
                    // 生成唯一的文件名
                    const timestamp = Date.now();
                    const randomStr = Math.random().toString(36).substring(2, 8);
                    const fileName = `image-models/${modelName}_${timestamp}_${randomStr}.json`;

                    // 将模型数据转换为Blob
                    const modelBlob = new Blob([JSON.stringify(modelData)], { type: 'application/json' });

                    // ---- 使用 ossApi 上传文件 ----
                    const formData = new FormData();
                    formData.append('file', modelBlob, fileName);
                    const fileUrl = await ossApi.uploadFile(formData);
                    // ----------------------------------

                    // 准备传递给 imageModelApi 的数据
                    const modelMetaData = {
                        name: modelName,
                        description: modelDesc,
                        // modelType: 'image', // imageModelApi 会自动设置
                        fileUrl: fileUrl,
                        labels: modelData.labels,
                        isPublic: true
                    };

                    // ---- 使用 workApi.saveImageModel ----
                    const result = await workApi.saveImageModel(modelMetaData);
                    // -----------------------------------

                    if (result.success) {
                        // 移除弹窗
                        document.body.removeChild(modal);
                        // 显示成功消息
                        notification.success('模型已成功保存到云端');
                    } else {
                        // 处理后端返回的错误
                        throw new Error(result.message || '保存模型元数据失败');
                    }

                } catch (error) {
                    console.error('保存模型失败:', error);
                    // 提取更详细的错误信息
                    const errorMessage = error.message || (error.response ? JSON.stringify(error.response) : '未知错误');
                    notification.error(`保存模型失败: ${errorMessage}`);

                    // 恢复保存按钮状态
                    saveButton.disabled = false;
                    saveButton.textContent = '保存';
                }
            });
            
            // 组装DOM
            buttonContainer.appendChild(cancelButton);
            buttonContainer.appendChild(saveButton);
            
            modalContent.appendChild(modalTitle);
            modalContent.appendChild(nameLabel);
            modalContent.appendChild(nameInput);
            modalContent.appendChild(descLabel);
            modalContent.appendChild(descInput);
            modalContent.appendChild(buttonContainer);
            
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // 自动聚焦到名称输入框
            nameInput.focus();
        } catch (error) {
            console.error('保存模型到云端准备失败:', error);
            notification.error('保存模型到云端失败: ' + error.message);
        }
    }

    const exportModelToBlocks = () => {
        // 检查模型是否已训练
        if (!modelTrained) {
            notification.error('请先训练模型');
            return false;
        }
        
        try {
            // 检查KNN分类器是否已初始化
            if (!knnRef.current) {
                notification.error('模型未初始化，请重新训练');
                return false;
            }
            
            // 获取训练好的分类器数据集
            const dataset = knnRef.current.getClassifierDataset();
            
            // 获取所有类别的标签
            const labels = classNodes.map(node => node.name || `类别 ${node.id + 1}`);
            
            // 将张量转换为可序列化的对象 - 添加量化处理以减小文件大小
            const tf = require('@tensorflow/tfjs');
            const serializedDataset = {};
            
            Object.keys(dataset).forEach((key) => {
                const tensor = dataset[key];
                // 找出数据范围以便进行更好的量化
                const values = tensor.dataSync();
                
                // 找出最大和最小值，用于量化
                let minVal = Number.POSITIVE_INFINITY;
                let maxVal = Number.NEGATIVE_INFINITY;
                for (let i = 0; i < values.length; i++) {
                    if (values[i] < minVal) minVal = values[i];
                    if (values[i] > maxVal) maxVal = values[i];
                }
                
                // 将32位浮点数量化为16位整数
                const range = maxVal - minVal;
                const quantizedValues = new Int16Array(values.length);
                for (let i = 0; i < values.length; i++) {
                    // 将值映射到0-65535范围
                    const normalizedVal = (values[i] - minVal) / range;
                    // 量化为16位整数
                    quantizedValues[i] = Math.round(normalizedVal * 32767);
                }
                
                // 保存量化后的数据和还原所需的信息
                serializedDataset[key] = {
                    quantizedValues: Array.from(quantizedValues),
                    shape: tensor.shape,
                    minVal: minVal,
                    maxVal: maxVal,
                    quantized: true
                };
            });
            
            // 构建完整的模型数据
            const modelData = {
                modelType: 'knn-classifier',
                dataset: serializedDataset,
                labels: labels,
                trainParams: trainParams, // 添加训练参数
                modelVersion: '1.4', // 升级版本号保持一致
                exportDate: new Date().toISOString(),
                compression: 'int16',
            };
            
            // 获取全局实例
            const parentInstance = window._logicleapImageTrainInstance;
            if (!parentInstance) {
                notification.error('无法获取全局实例');
                return false;
            }
            
            // 更新模型状态
            parentInstance.modelImported = true;
            
            // 保存模型数据到全局实例
            parentInstance.modelCount = (parentInstance.modelCount || 0) + 1;
            const modelNumber = String(parentInstance.modelCount);
            
            // 更新模型缓存
            parentInstance.modelCache = parentInstance.modelCache || {};
            parentInstance.modelDataCache = parentInstance.modelDataCache || {};
            
            // 获取模型标签
            const modelName = `包含${labels.length}个类别的图像模型`;
            
            parentInstance.modelCache[modelNumber] = {
                modelName: modelName,
                importTime: new Date().toISOString(),
                labels: labels
            };
            
            // 在内存中保存模型数据
            const modelDataString = JSON.stringify(modelData);
            parentInstance.modelDataCache[modelNumber] = modelDataString;
            
            // 将模型数据保存到浏览器缓存
            try {
                localStorage.setItem('logicleap_model_cache_index', JSON.stringify(parentInstance.modelCache));
                localStorage.setItem(`logicleap_model_data_${modelNumber}`, modelDataString);
            } catch (storageError) {
                console.warn('保存到本地存储失败:', storageError);
                // 即使本地存储失败，也继续执行，因为模型已经在内存中
            }
            
            // 触发模型缓存更新事件
            if (typeof parentInstance.dispatchModelCacheUpdatedEvent === 'function') {
                parentInstance.dispatchModelCacheUpdatedEvent();
            }
            
            // 显示成功消息
            notification.success(`模型已成功保存（编号：${modelNumber}），可以使用"预测图像"积木块来使用此模型。`);
            
            // 自动关闭训练窗口
            setTimeout(() => {
                if (parentInstance && typeof parentInstance.closeTrainDialog === 'function') {
                    parentInstance.closeTrainDialog();
                }
            }, 1500); // 添加延时，让用户看到成功消息
            
            return true;
        } catch (error) {
            console.error('导出模型失败:', error);
            notification.error(`导出模型失败: ${error.message}`);
            return false;
        }
    };

    //默认初始化方法
    useEffect(() => {
        init();
        startCapture();

        // 保存实例引用到全局变量，便于外部访问
        window._logicleapImageTrainInstance = {
            handleImportModel,
            handleExportToLocal,
            handleExportToCloud,
            exportModelToBlocks
        };

        // 检查是否有待导入的模型数据（来自编辑模型功能）
        console.log('检查待导入的模型数据:', window._pendingModelDataForEdit);
        if (window._pendingModelDataForEdit) {
            console.log('发现待导入的模型数据，开始处理...');
            try {
                // 延迟执行导入，确保组件完全初始化
                setTimeout(() => {
                    // 创建一个模拟的文件对象
                    const blob = new Blob([window._pendingModelDataForEdit], { type: 'application/json' });
                    const file = new File([blob], 'model.json', { type: 'application/json' });

                    // 创建文件读取器
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                        try {
                            console.log('🐱 Claude 4.0 sonnet: 开始解析模型数据...');
                            console.log('🐱 原始数据长度:', e.target.result?.length);

                            // 检查数据是否为空
                            if (!e.target.result) {
                                console.error('文件内容为空');
                                notification.error('文件内容为空，请选择有效的模型文件');
                                return;
                            }

                            // 解析JSON数据
                            let modelData;
                            try {
                                modelData = JSON.parse(e.target.result);
                                console.log('JSON解析成功，模型数据:', modelData);
                            } catch (parseError) {
                                console.error('JSON解析失败:', parseError);
                                notification.error('模型文件格式错误，无法解析JSON数据');
                                return;
                            }

                            // 检查模型数据是否为空
                            console.log('模型数据类型:', typeof modelData);
                            console.log('模型数据是否为null:', modelData === null);
                            console.log('模型数据是否为undefined:', modelData === undefined);
                            console.log('模型数据布尔值:', !!modelData);

                            if (!modelData || typeof modelData !== 'object' || modelData === null) {
                                console.error('解析后的模型数据为空或无效');
                                notification.error('模型数据为空，请选择有效的模型文件');
                                return;
                            }

                            // 检查模型类型
                            console.log('检查模型类型:', modelData.modelType);
                            if (!modelData.modelType || modelData.modelType !== 'knn-classifier') {
                                console.error('不支持的模型类型:', modelData.modelType);
                                notification.error('不支持的模型类型，请导入KNN分类器模型');
                                return;
                            }

                            // 执行导入逻辑（复制handleImportModel中的核心逻辑）
                            // 这里直接调用我们之前实现的导入代码

                            // 检查是否有缩略图数据
                            if (modelData.thumbnails) {
                                // 处理缩略图数据，恢复到类别节点
                                const newClassNodes = [];

                                // 遍历标签创建类别节点
                                modelData.labels.forEach((label, index) => {
                                    const classId = index;
                                    const samples = [];

                                    // 如果有对应的缩略图数据，添加到samples中
                                    if (modelData.thumbnails[classId]) {
                                        modelData.thumbnails[classId].forEach(thumbnail => {
                                            samples.push({
                                                id: thumbnail.id,
                                                imageUrl: thumbnail.imageUrl
                                            });
                                        });
                                    }

                                    newClassNodes.push({
                                        id: classId,
                                        name: label,
                                        samples: samples,
                                        expanded: false
                                    });
                                });

                                // 计算总的样本数量
                                const totalSamples = newClassNodes.reduce((total, node) => total + node.samples.length, 0);

                                // 检查图片数量，如果超过30张则显示警告
                                if (totalSamples > 30) {
                                    const confirmed = window.confirm(
                                        `该模型包含${totalSamples}个样本图片（超过30张），模型较大可能会导致机器卡顿。\n\n是否继续导入？`
                                    );

                                    if (!confirmed) {
                                        // 用户取消导入
                                        window._pendingModelDataForEdit = null;
                                        return;
                                    }
                                }

                                // 用户确认导入，更新状态
                                setClassNodes(newClassNodes);

                                // 恢复训练参数（如果存在）
                                if (modelData.trainParams) {
                                    setTrainParams(modelData.trainParams);
                                    console.log('已恢复训练参数:', modelData.trainParams);
                                } else {
                                    // 向后兼容：旧模型文件没有训练参数时使用默认值
                                    setTrainParams(DefaultTrainParams);
                                    console.log('使用默认训练参数（旧版本模型）');
                                }

                                // 显示成功消息
                                const hasTrainParams = modelData.trainParams ? '训练参数已恢复，' : '';
                                if (totalSamples > 0) {
                                    notification.success(`训练数据已成功导入！包含${newClassNodes.length}个类别，共${totalSamples}个样本图片。${hasTrainParams}请点击"开始训练"来训练模型。`);
                                } else {
                                    notification.success(`模型结构已导入！包含${newClassNodes.length}个类别（旧版本模型，无样本缩略图）。${hasTrainParams}请添加样本后重新训练。`);
                                }
                            } else {
                                notification.error('模型文件中没有找到缩略图数据，无法编辑');
                            }

                        } catch (error) {
                            console.error('自动导入模型失败:', error);
                            notification.error('自动导入模型失败: ' + error.message);
                        }
                    };

                    reader.readAsText(file);
                }, 1000); // 等待1秒确保组件完全初始化

                // 清理全局变量
                window._pendingModelDataForEdit = null;

            } catch (error) {
                console.error('处理待导入模型数据失败:', error);
                notification.error('处理模型数据失败');
                window._pendingModelDataForEdit = null;
            }
        }

        // 组件卸载时清理
        return () => {
            window._logicleapImageTrainInstance = null;
        };

    }, []);

    // 添加绘制图表的useEffect
    useEffect(() => {
        if (showDataChart) {
            const drawAccuracyChart = () => {
                const canvas = accChartRef.current;
                if (!canvas) return;
                
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 设置画布尺寸
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                
                // 增加顶部填充，为图例留出更多空间
                const padding = { top: 30, right: 20, bottom: 30, left: 40 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 绘制网格背景
                ctx.fillStyle = '#f9f9f9';
                ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
                
                // 绘制网格线
                ctx.beginPath();
                ctx.strokeStyle = '#e0e0e0';
                ctx.lineWidth = 1;
                
                // 水平网格线
                for (let i = 0; i <= 10; i++) {
                    const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(canvas.width - padding.right, y);
                }
                
                // 垂直网格线 - 根据数据点数量决定
                const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
                if (trainingHistory.epochs.length > 0) {
                    for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
                        if (i <= trainingHistory.epochs.length) {
                            const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                            ctx.moveTo(x, padding.top);
                            ctx.lineTo(x, canvas.height - padding.bottom);
                        }
                    }
                }
                
                ctx.stroke();
                
                // 绘制坐标轴
                ctx.beginPath();
                ctx.strokeStyle = '#999';
                ctx.lineWidth = 1.5;
                
                // X轴
                ctx.moveTo(padding.left, canvas.height - padding.bottom);
                ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
                
                // Y轴
                ctx.moveTo(padding.left, padding.top);
                ctx.lineTo(padding.left, canvas.height - padding.bottom);
                ctx.stroke();
                
                // 如果没有数据，则不绘制
                if (trainingHistory.epochs.length === 0) return;
                
                // 计算比例尺
                const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
                
                // 绘制图例 - 移到顶部标题位置上方，避免挡住数据
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.textBaseline = 'top';
                
                // 训练准确率图例
                ctx.fillStyle = '#4f7df9';
                ctx.fillRect(padding.left, padding.top - 20, 15, 6);
                ctx.fillText('准确率', padding.left + 20, padding.top - 20);
                
                // 验证准确率图例
                if (trainingHistory.valAccuracy.some(v => v > 0)) {
                    ctx.fillStyle = '#f97d4f';
                    ctx.fillRect(padding.left + 120, padding.top - 20, 15, 6);
                    ctx.fillText('验证准确率', padding.left + 140, padding.top - 20);
                }
                
                // 绘制训练准确率
                if (trainingHistory.trainAccuracy.length > 0) {
                    ctx.beginPath();
                    ctx.strokeStyle = '#4f7df9';
                    ctx.lineWidth = 2;
                    
                    trainingHistory.trainAccuracy.forEach((acc, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (acc * chartHeight);
                        
                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });
                    
                    ctx.stroke();
                    
                    // 绘制数据点
                    trainingHistory.trainAccuracy.forEach((acc, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (acc * chartHeight);
                        
                        ctx.beginPath();
                        ctx.fillStyle = '#4f7df9';
                        ctx.arc(x, y, 3, 0, Math.PI * 2);
                        ctx.fill();
                    });
                }
                
                // 绘制验证准确率
                if (trainingHistory.valAccuracy.length > 0 && trainingHistory.valAccuracy.some(v => v > 0)) {
                    ctx.beginPath();
                    ctx.strokeStyle = '#f97d4f';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 3]);
                    
                    trainingHistory.valAccuracy.forEach((acc, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (acc * chartHeight);
                        
                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });
                    
                    ctx.stroke();
                    ctx.setLineDash([]);
                    
                    // 绘制数据点
                    trainingHistory.valAccuracy.forEach((acc, i) => {
                        if (acc > 0) {
                            const x = padding.left + i * xScale;
                            const y = canvas.height - padding.bottom - (acc * chartHeight);
                            
                            ctx.beginPath();
                            ctx.fillStyle = '#f97d4f';
                            ctx.arc(x, y, 3, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    });
                }
                
                // 绘制Y轴刻度
                ctx.font = '10px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'right';
                ctx.textBaseline = 'middle';
                
                for (let i = 0; i <= 10; i++) {
                    const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
                    ctx.fillText(`${(i * 10)}%`, padding.left - 5, y);
                }
                
                // 绘制X轴刻度
                ctx.textAlign = 'center';
                ctx.textBaseline = 'top';
                
                const totalEpochs = trainingHistory.epochs.length;
                
                // 显示第一个周期
                if (totalEpochs > 0) {
                    ctx.fillText(
                        trainingHistory.epochs[0],
                        padding.left,
                        canvas.height - padding.bottom + 5
                    );
                    
                    // 显示中间周期
                    for (let i = gridStep; i < totalEpochs; i += gridStep) {
                        const x = padding.left + (i / totalEpochs) * chartWidth;
                        ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
                    }
                    
                    // 显示最后一个周期
                    ctx.fillText(
                        trainingHistory.epochs[totalEpochs - 1],
                        padding.left + chartWidth,
                        canvas.height - padding.bottom + 5
                    );
                }
            };
            
            const drawLossChart = () => {
                const canvas = lossChartRef.current;
                if (!canvas) return;
                
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 设置画布尺寸
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
                
                // 增加顶部填充，为图例留出更多空间
                const padding = { top: 30, right: 20, bottom: 30, left: 40 };
                const chartWidth = canvas.width - padding.left - padding.right;
                const chartHeight = canvas.height - padding.top - padding.bottom;
                
                // 绘制网格背景
                ctx.fillStyle = '#f9f9f9';
                ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
                
                // 绘制网格线
                ctx.beginPath();
                ctx.strokeStyle = '#e0e0e0';
                ctx.lineWidth = 1;
                
                // 水平网格线
                for (let i = 0; i <= 10; i++) {
                    const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
                    ctx.moveTo(padding.left, y);
                    ctx.lineTo(canvas.width - padding.right, y);
                }
                
                // 垂直网格线
                const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
                if (trainingHistory.epochs.length > 0) {
                    for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
                        if (i <= trainingHistory.epochs.length) {
                            const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                            ctx.moveTo(x, padding.top);
                            ctx.lineTo(x, canvas.height - padding.bottom);
                        }
                    }
                }
                
                ctx.stroke();
                
                // 绘制坐标轴
                ctx.beginPath();
                ctx.strokeStyle = '#999';
                ctx.lineWidth = 1.5;
                
                // X轴
                ctx.moveTo(padding.left, canvas.height - padding.bottom);
                ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
                
                // Y轴
                ctx.moveTo(padding.left, padding.top);
                ctx.lineTo(padding.left, canvas.height - padding.bottom);
                ctx.stroke();
                
                // 如果没有数据，则不绘制
                if (trainingHistory.epochs.length === 0) return;
                
                // 计算比例尺
                const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
                
                // 获取损失值范围
                const allLoss = [
                    ...trainingHistory.trainLoss,
                    ...trainingHistory.valLoss.filter(l => l > 0)
                ].filter(l => !isNaN(l) && isFinite(l));
                
                // 如果没有有效的损失值，则不绘制
                if (allLoss.length === 0) return;
                
                const maxLoss = Math.min(Math.max(...allLoss), 2); // 限制最大值为2，以避免异常值影响图表比例
                
                // 绘制图例
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.textBaseline = 'top';
                
                // 训练损失图例
                ctx.fillStyle = '#4fb14f';
                ctx.fillRect(padding.left, padding.top - 20, 15, 6);
                ctx.fillText('损失值', padding.left + 20, padding.top - 20);
                
                // 验证损失图例
                if (trainingHistory.valLoss.some(v => v > 0)) {
                    ctx.fillStyle = '#b14f4f';
                    ctx.fillRect(padding.left + 120, padding.top - 20, 15, 6);
                    ctx.fillText('验证损失', padding.left + 140, padding.top - 20);
                }
                
                // 绘制训练损失
                if (trainingHistory.trainLoss.length > 0) {
                    ctx.beginPath();
                    ctx.strokeStyle = '#4fb14f';
                    ctx.lineWidth = 2;
                    
                    trainingHistory.trainLoss.forEach((loss, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                        
                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });
                    
                    ctx.stroke();
                    
                    // 绘制数据点
                    trainingHistory.trainLoss.forEach((loss, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                        
                        ctx.beginPath();
                        ctx.fillStyle = '#4fb14f';
                        ctx.arc(x, y, 3, 0, Math.PI * 2);
                        ctx.fill();
                    });
                }
                
                // 绘制验证损失
                if (trainingHistory.valLoss.length > 0 && trainingHistory.valLoss.some(v => v > 0)) {
                    ctx.beginPath();
                    ctx.strokeStyle = '#b14f4f';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 3]);
                    
                    trainingHistory.valLoss.forEach((loss, i) => {
                        const x = padding.left + i * xScale;
                        const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                        
                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    });
                    
                    ctx.stroke();
                    ctx.setLineDash([]);
                    
                    // 绘制数据点
                    trainingHistory.valLoss.forEach((loss, i) => {
                        if (loss > 0) {
                            const x = padding.left + i * xScale;
                            const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                            
                            ctx.beginPath();
                            ctx.fillStyle = '#b14f4f';
                            ctx.arc(x, y, 3, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    });
                }
                
                // 绘制Y轴刻度
                ctx.font = '10px Arial';
                ctx.fillStyle = '#666';
                ctx.textAlign = 'right';
                ctx.textBaseline = 'middle';
                
                for (let i = 0; i <= 10; i++) {
                    const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
                    ctx.fillText((maxLoss * i / 10).toFixed(1), padding.left - 5, y);
                }
                
                // 绘制X轴刻度
                ctx.textAlign = 'center';
                ctx.textBaseline = 'top';
                
                const totalEpochs = trainingHistory.epochs.length;
                
                // 显示第一个周期
                if (totalEpochs > 0) {
                    ctx.fillText(
                        trainingHistory.epochs[0],
                        padding.left,
                        canvas.height - padding.bottom + 5
                    );
                    
                    // 显示中间周期
                    for (let i = gridStep; i < totalEpochs; i += gridStep) {
                        const x = padding.left + (i / totalEpochs) * chartWidth;
                        ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
                    }
                    
                    // 显示最后一个周期
                    ctx.fillText(
                        trainingHistory.epochs[totalEpochs - 1],
                        padding.left + chartWidth,
                        canvas.height - padding.bottom + 5
                    );
                }
            };
            
            // 设置一个更新定时器，定期重绘图表以反映新数据
            const updateTimer = setInterval(() => {
                if (trainingHistory.epochs.length > 0) {
                    drawAccuracyChart();
                    drawLossChart();
                }
            }, 100); // 每100ms更新一次图表
            
            // 首次调用绘制
            if (trainingHistory.epochs.length > 0) {
                setTimeout(() => {
                    drawAccuracyChart();
                    drawLossChart();
                }, 0);
            }
            
            // 清理函数
            return () => {
                clearInterval(updateTimer);
            };
        }
    }, [showDataChart, trainingHistory, hasData]);


    // 添加一个useEffect来监听样本数量和类别节点的变化，并更新canTrainRef的值
    useEffect(() => {
        const updateCanTrain = () => {
        // 检查是否有至少两个类别节点
        const hasEnoughClasses = classNodes.length >= 2;
        
        // 检查每个类别是否都有足够的样本（至少5个）
        const allClassesHaveSamples = classNodes.every(node => {
            return node.samples && node.samples.length >= 10;
        });
        
        // 更新canTrainRef的值
        canTrainRef.current = hasEnoughClasses && allClassesHaveSamples;
        
        // 输出当前训练条件状态到控制台，帮助调试
        console.log('训练条件检查:', {
            hasEnoughClasses,
            allClassesHaveSamples,
            canTrain: canTrainRef.current,
            classCount: classNodes.length,
            samplesState: classNodes.map(node => ({ 
            classId: node.id, 
            sampleCount: node.samples ? node.samples.length : 0 
            }))
        });
        };
        
        // 执行更新
        updateCanTrain();
        
    }, [classNodes]);

    //模型初始化
    const init = async () => {
         // 导入加载动画组件
         const { createSquareLoadingAnimation } = require('../utils/loadingAnimation');
        
         // 创建加载动画
         const loader = createSquareLoadingAnimation({
             message: '正在初始化模型...',
             backgroundColor: '#4766C2', // 使用不透明的蓝色背景
             boxColor: '#ffffff',
             textColor: '#ffffff'
         });
         try {
            // 更新加载进度
            loader.updateProgress('加载TensorFlow.js...',20);
            
            // 初始化 TensorFlow.js
            const tf = require('@tensorflow/tfjs');
            // 设置后端为 WebGL（优先）或 CPU
            await tf.setBackend('webgl');
            if (!tf.getBackend()) {
                await tf.setBackend('cpu');
            }
           
            // 更新加载进度
            loader.updateProgress('加载MobileNet模型...',40);

            // 初始化模型
            const mobilenetModule = require('@tensorflow-models/mobilenet');
            const knnClassifier = require('@tensorflow-models/knn-classifier');
            
            loader.updateProgress('初始化分类器...',60);
            knnRef.current = knnClassifier.create();
            
            loader.updateProgress('加载预训练模型...',80);
            // 使用更轻量级的MobileNet模型
            const modelUrl = `${window.location.origin}/static/utils/image_train/model.json`;
            mobilenetRef.current = await mobilenetModule.load({version: 1, alpha: 0.25, modelUrl: modelUrl});
            
            // 更新加载消息
            loader.updateMessage('模型初始化完成！');
            
            // 平滑移除加载动画
            setTimeout(() => {
                loader.remove();
            }, 500);
           
        } catch (error) {
            // 更新加载动画显示错误
            loader.updateMessage('初始化失败');
            loader.updateProgress(error.message);
            
            // 3秒后移除加载动画
            setTimeout(() => {
                loader.remove();
            }, 3000);
            
            console.error('TensorFlow 初始化失败:', error);
            throw error;
        }
    }

    // 添加开始捕获摄像头画面方法
    const startCapture = async () => {
        try {
            // 获取视频元素
            videoRef.current = document.createElement('video');
            videoRef.current.setAttribute('autoplay', '');
            videoRef.current.setAttribute('playsinline', '');
            
            // 获取摄像头权限
            const constraints = {
                video: true,
                audio: false
            };
            
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            videoRef.current.srcObject = stream;
            
            // 等待视频元素加载
            await new Promise(resolve => {
                videoRef.current.onloadedmetadata = () => {
                    resolve();
                };
            });
            
            // 开始播放视频
            await videoRef.current.play();
            videoPlayingRef.current = true;
            
            // 开始动画循环
            timerRef.current = requestAnimationFrame(animate);
            
            return stream;
        } catch (error) {
            notification.error('无法访问摄像头');
            console.error('无法访问摄像头:', error);
            return null;
        }
    }
    
    // 停止摄像头
    const stopCapture = () => {
        if (videoPlayingRef.current) {
            videoPlayingRef.current = false;
        }
        
        // 取消动画循环
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
        
        // 停止视频流
        if (videoRef.current && videoRef.current.srcObject) {
            const tracks = videoRef.current.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            videoRef.current.srcObject = null;
        }
    }
    
    // 处理类别展开时的摄像头初始化
    const setupWebcamForClass = async (nodeId) => {
        // 如果当前有正在播放的视频，确保它被停止
        stopCapture();
        
        // 获取容器元素
        const container = document.getElementById(`camera-container-${nodeId}`);
        if (!container) {
            console.error(`未找到类别${nodeId}的摄像头容器`);
            return;
        }
        
        try {
            // 清空容器
            container.innerHTML = '';
            
            // 获取摄像头流
            const stream = await startCapture();
            if (!stream) return;
            
            // 创建视频元素并添加到容器
            const videoElement = document.createElement('video');
            videoElement.setAttribute('autoplay', '');
            videoElement.setAttribute('playsinline', '');
            videoElement.style.width = '100%';
            videoElement.style.height = '100%';
            videoElement.style.objectFit = 'cover';
            
            // 设置视频源
            videoElement.srcObject = stream;
            
            // 添加到容器
            container.appendChild(videoElement);
            
            // 设置当前类别引用
            currentClassRef.current = nodeId;
            
            // 添加标题
            
            // 创建左上角翻转摄像头按钮
            const flipButton = document.createElement('button');
            flipButton.style.position = 'absolute';
            flipButton.style.top = '8px';
            flipButton.style.right = '8px';
            flipButton.style.background = 'transparent';
            flipButton.style.color = 'white';
            flipButton.style.border = 'none';
            flipButton.style.width = '32px'; // 固定宽度
            flipButton.style.height = '32px'; // 固定高度
            flipButton.style.padding = '0';
            flipButton.style.cursor = 'pointer';
            flipButton.style.display = 'flex';
            flipButton.style.alignItems = 'center';
            flipButton.style.justifyContent = 'center';
            flipButton.innerHTML = `
             <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M24 6V42" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M4 34L16 12V34H4Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
                <path d="M44 34H32V12L44 34Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
            </svg>
            `;
            
            // 翻转摄像头事件
            flipButton.onclick = () => {
              // 切换视频的水平翻转
              if (videoElement.style.transform === 'scaleX(-1)') {
                videoElement.style.transform = 'scaleX(1)';
              } else {
                videoElement.style.transform = 'scaleX(-1)';
              }
            };
            container.appendChild(flipButton);
            
        } catch (error) {
            notification.error('初始化摄像头失败');
            console.error('初始化摄像头失败:', error);
        }
    }

     // 动画循环函数，用于实时处理摄像头画面
     const animate = async () => {
        if (videoPlayingRef.current) {
            try {
                const tf = require('@tensorflow/tfjs');
                
                // 确保视频元素准备就绪
                if (!videoRef.current || !videoRef.current.videoWidth) {
                    timerRef.current = requestAnimationFrame(animate);
                    return;
                }

                // 获取视频帧
                const image = tf.browser.fromPixels(videoRef.current);
                
                let logits;
                // 使用 MobileNet 提取特征 - 使用更小的特征层
                const infer = () => mobilenetRef.current.infer(image, 'conv_pw_13_relu');

                // 如果正在录制，添加样本
                if (currentRecordIndexRef.current !== -1) {
                    try {
                        // 控制采集频率
                        const currentTime = Date.now();
                        // 使用设置中的FPS参数计算采集间隔，从ref获取最新值
                        const captureInterval = 1000 / (recordSettingsRef.current.fps || 10);
                        // 检查是否达到采集间隔
                        if (currentTime - lastCaptureTimeRef.current >= captureInterval) {
                            logits = infer();
                            // 不再每次即时添加到KNN，只保存特征和样本图像
                            // 在训练时才会批量添加到KNN
                            
                            // 获取当前视频元素的翻转状态
                            const videoContainer = document.getElementById(`camera-container-${currentRecordIndexRef.current}`);
                            const videoElement = videoContainer ? videoContainer.querySelector('video') : null;
                            const isFlipped = videoElement ? videoElement.style.transform === 'scaleX(-1)' : false;
                            
                            // 保存当前帧作为预览图片，考虑翻转状态
                            const canvas = document.createElement('canvas');
                            canvas.width = videoRef.current.videoWidth;
                            canvas.height = videoRef.current.videoHeight;
                            const ctx = canvas.getContext('2d');
                            
                            if (isFlipped) {
                                // 如果视频是翻转的，在绘制到canvas时也应用翻转
                                ctx.translate(canvas.width, 0);
                                ctx.scale(-1, 1);
                            }
                            
                            ctx.drawImage(videoRef.current, 0, 0);
                            
                            // 如果应用了翻转，恢复canvas上下文
                            if (isFlipped) {
                                ctx.setTransform(1, 0, 0, 1, 0, 0);
                            }
                            
                            const imageUrl = canvas.toDataURL('image/jpeg');

                            // 创建新样本
                            const newSample = {
                                id: Date.now() + Math.random(),
                                imageUrl: imageUrl,
                                isFlipped: isFlipped // 可选：记录此样本是否是翻转的
                            };
                            
                            // 更新类别节点，添加新样本
                            setClassNodes(prev => 
                                prev.map(n => 
                                    n.id === currentRecordIndexRef.current
                                        ? { ...n, samples: [...(n.samples || []), newSample] } 
                                        : n
                            ));

                            // 更新上次采集时间
                            lastCaptureTimeRef.current = currentTime;
                        }
                    } catch (error) {
                        console.error('Error during sample capture:', error);
                    }
                }

                // 如果有训练的类别，进行预测
                if (knnRef.current) {
                    const numClasses = knnRef.current.getNumClasses();
                    if (numClasses > 0 && modelTrained) {
                        logits = infer();
                        const res = await knnRef.current.predictClass(logits, TOPK);

                        // 更新预测结果
                        if (predicting) {
                            const newConfidences = [];
                            
                            // 使用 classNodesRef 而不是 classNodes 获取最新的类别名称
                            const classNames = classNodesRef.current.map(node => node.name || `类别 ${node.id + 1}`);
                            
                            // 构建置信度数据
                            for (let i = 0; i < numClasses; i++) {
                                const confidence = res.confidences[i] || 0;
                                newConfidences.push({
                                    id: i,
                                    name: classNames[i] || `类别 ${i + 1}`,
                                    confidence: confidence
                                });
                            }
                            
                            // 更新置信度状态
                            setConfidences(newConfidences);
                        }
                    }
                }

                // 释放内存
                image.dispose();
                if (logits) {
                    logits.dispose();
                }
            } catch (error) {
                console.error('Error during animation:', error);
            }
            
            // 下一帧动画
            timerRef.current = requestAnimationFrame(animate);
        }
    }

    // 添加键盘事件处理
    useEffect(() => {
        // 空格键按下
        const handleKeyDown = (e) => {
            if (e.code === 'Space' || e.keyCode === 32) {
                if(!canSpaceRecord()){
                    return;
                }
                // 只有当有展开的类别节点时才处理
                if (currentClassRef.current !== -1) {
                    e.preventDefault(); // 防止页面滚动
                    // 设置录制索引
                    startRecording();
                }
            }
        };
        
        // 空格键释放
        const handleKeyUp = (e) => {
            if (e.code === 'Space' || e.keyCode === 32) {
                if(!canSpaceRecord()){
                    return;
                }
                // 只有当有展开的类别节点时才处理
                if (currentClassRef.current !== -1) {
                    e.preventDefault(); // 防止页面滚动
                    
                    // 停止录制
                    stopRecording();
                    
                    // 模拟拍摄一次
                    const tf = require('@tensorflow/tfjs');
                    
                    // 确保视频元素准备就绪
                    if (videoRef.current && videoRef.current.videoWidth) {
                        // 获取视频帧
                        const image = tf.browser.fromPixels(videoRef.current);
                        
                        // 使用 MobileNet 提取特征
                        const logits = mobilenetRef.current.infer(image, 'conv_pw_13_relu');
                        
                        // 不再每次即时添加到KNN，只保存样本图像
                        // 在训练时才会批量添加到KNN
                        
                        // 保存当前帧作为预览图片
                        const canvas = document.createElement('canvas');
                        canvas.width = videoRef.current.videoWidth;
                        canvas.height = videoRef.current.videoHeight;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(videoRef.current, 0, 0);
                        const imageUrl = canvas.toDataURL('image/jpeg');

                        // 创建新样本
                        const newSample = {
                            id: Date.now() + Math.random(),
                            imageUrl: imageUrl
                        };
                        
                        // 更新类别节点，添加新样本
                        setClassNodes(prev => 
                            prev.map(n => 
                                n.id === currentClassRef.current
                                    ? { ...n, samples: [...(n.samples || []), newSample] } 
                                    : n
                            )
                        );
                        
                        // 释放内存
                        image.dispose();
                        logits.dispose();
                    }
                }
            }
        };

        
        const canSpaceRecord = ()=>{
            if(recordSettingsRef.current.canSpaceRecord){
                return true;
            }else{
                return false;
            }
        }
        
        // 添加事件监听器
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        
        // 清理函数
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
        };
    }, []);


    const startRecording = (delay = 0, duration = 0, holdToRecord = true) => {
        if (holdToRecord) {
            // 如果是按住录制模式，直接开始录制
            currentRecordIndexRef.current = currentClassRef.current;
        } else {
            // 清除可能存在的旧定时器
            if (startDelayTimerRef.current) {
                clearTimeout(startDelayTimerRef.current);
            }
            if (recordingTimerRef.current) {
                clearTimeout(recordingTimerRef.current);
            }

            // 创建倒计时显示元素
            const videoContainer = document.getElementById(`camera-container-${currentClassRef.current}`);
            if (videoContainer) {
                let countdownElement = document.createElement('div');
                countdownElement.style.position = 'absolute';
                countdownElement.style.top = '50%';
                countdownElement.style.left = '50%';
                countdownElement.style.transform = 'translate(-50%, -50%)';
                countdownElement.style.fontSize = '48px';
                countdownElement.style.fontWeight = 'bold';
                countdownElement.style.color = 'white';
                countdownElement.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
                countdownElement.style.zIndex = '1000';
                videoContainer.appendChild(countdownElement);

                // 开始倒计时
                let remainingTime = delay / 1000;
                const updateCountdown = () => {
                    if (remainingTime > 0) {
                        countdownElement.textContent = remainingTime.toFixed(1);
                        remainingTime = Math.max(0, remainingTime - 0.1);
                        if (remainingTime > 0) {
                            setTimeout(updateCountdown, 100);
                        } else {
                            countdownElement.remove();
                        }
                    } else {
                        countdownElement.remove();
                    }
                };
                updateCountdown();
            }

            // 设置延迟开始定时器
            startDelayTimerRef.current = setTimeout(() => {
                // 开始录制
                currentRecordIndexRef.current = currentClassRef.current;
                
                // 设置录制时长定时器
                if (duration > 0) {
                    recordingTimerRef.current = setTimeout(() => {
                        stopRecording();
                    }, duration);
                }
            }, delay);
        }
    }

    const stopRecording = () => {
        // 清除所有定时器
        if (startDelayTimerRef.current) {
            clearTimeout(startDelayTimerRef.current);
            startDelayTimerRef.current = null;
        }
        if (recordingTimerRef.current) {
            clearTimeout(recordingTimerRef.current);
            recordingTimerRef.current = null;
        }
        currentRecordIndexRef.current = -1;
    }
    

    // 渲染类别卡片
    const renderClassCard = (node) => {
        return (
            <div 
                key={node.id} 
                data-class-node={true}
                className={ClassCardCss.classCard}
            >
                {/* 标题部分 */}
                <div style={ClassCardStyle.Header}>
                    <div style={ClassCardStyle.HeaderLeft}>
                        <input
                            type="text"
                            value={node.name || `类别 ${node.id + 1}`}
                             onChange={(e) => {
                                // 事件对象可能被React回收，所以先persist或立即提取值
                                e.persist(); // 防止React事件池回收此事件
                                // 提取当前的值，避免后续引用已回收的事件对象
                                const newValue = e.target && e.target.value;
                                // 添加安全检查，确保e和e.target不为null
                                if (e && e.target && typeof newValue !== 'undefined') {
                                    setClassNodes(prev => 
                                        prev.map(n => 
                                            n.id === node.id ? { ...n, name: newValue } : n
                                        )
                                    );
                                }
                            }}
                            style={ClassCardStyle.HeaderLeftInput}
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        />
                        
                        {/* 编辑图标 */}
                        <button 
                            style={ClassCardStyle.HeaderLeftInputEditButton}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.opacity = '1';
                                e.currentTarget.querySelector('svg').style.stroke = '#4766C2';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.opacity = '0.6';
                                e.currentTarget.querySelector('svg').style.stroke = '#9aa0a6';
                            }}
                            onClick={(e) => {
                                e.stopPropagation();
                                const input = e.currentTarget.parentNode.querySelector('input');
                                input.focus();
                                input.select();
                            }}
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                            </svg>
                        </button>
                    </div>
                    
                    {/* 菜单按钮 */}
                    <div style={ClassCardStyle.HeaderRight}>
                        <button
                            style={ClassCardStyle.HeaderRightMenuButton}
                            onClick={(e) => {
                                e.stopPropagation();
                                // 打开/关闭菜单的逻辑
                                // 可以增加一个菜单状态控制
                                const menuElem = e.currentTarget.nextElementSibling;
                                if (menuElem) {
                                    menuElem.style.display = menuElem.style.display === 'block' ? 'none' : 'block';
                                }
                            }}
                            data-class-card-menu-button="true"
                        >
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                            </svg>
                        </button>
                        
                        {/* 下拉菜单 */}
                        <div 
                            style={ClassCardStyle.HeaderRightMenuDropdown}
                            data-class-card-menu="true"
                        >
                            <div 
                                style={ClassCardStyle.HeaderRightMenuItem}
                                onMouseOver={(e) => e.currentTarget.style.background = '#f5f5f5'}
                                onMouseOut={(e) => e.currentTarget.style.background = 'white'}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    // 移除所有样本逻辑
                                    setClassNodes(prev => 
                                        prev.map(n => 
                                            n.id === node.id ? { ...n, samples: [] } : n
                                        )
                                    );
                                    e.currentTarget.parentNode.style.display = 'none';
                                }}
                            >
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#666" strokeWidth="2">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                </svg>
                                清空样本
                            </div>
                            <div 
                                style={ClassCardStyle.HeaderRightMenuItem}
                                onMouseOver={(e) => e.currentTarget.style.background = '#f5f5f5'}
                                onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    // 下载样本逻辑
                                    downloadSamples(node.id);
                                    e.currentTarget.parentNode.style.display = 'none';
                                }}
                            >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="7 10 12 15 17 10"></polyline>
                                    <line x1="12" y1="15" x2="12" y2="3"></line>
                                </svg>
                                下载样本
                            </div>
                            <div 
                                style={{
                                    ...ClassCardStyle.HeaderRightMenuItem,
                                    color: '#d93025',
                                }}
                                onMouseOver={(e) => e.currentTarget.style.background = '#f5f5f5'}
                                onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    // 删除类别逻辑
                                    setClassNodes(prev => prev.filter(n => n.id !== node.id));
                                    e.currentTarget.parentNode.style.display = 'none';
                                }}
                            >
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                                删除类别
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* 初始内容 - 未展开状态 */}
                {!node.expanded && (
                    <div style={ClassCardStyle.ContentNotExpanded}>
                        <div style={ClassCardStyle.ContentNotExpandedSamplesSummary}>
                            {node.samples.length === 0 || node.samples.length === undefined ? 
                                '添加图片样本:（应至少添加10个）' : 
                                `${node.samples.length} 个图片样本（应至少添加10个）`
                            }
                        </div>
                        
                        <div style={ClassCardStyle.ContentNotExpandedContent}>
                            {/* 按钮容器 */}
                            <div style={ClassCardStyle.ContentNotExpandedContentLeftButtonContainer}>
                                {/* 摄像头按钮 */}
                                <button
                                    style={ClassCardStyle.ContentNotExpandedContentLeftButton}
                                    onClick={() => {
                                        // 展开当前类别
                                        setClassNodes(prev => 
                                            prev.map(n => 
                                                n.id === node.id 
                                                    ? { ...n, expanded: true } 
                                                    : { ...n, expanded: false }
                                            )
                                        );
                                        currentClassRef.current = node.id;
                                        
                                        // 使用setTimeout确保DOM已经更新
                                        setTimeout(() => {
                                            setupWebcamForClass(node.id);
                                        }, 100);
                                    }}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                        <circle cx="12" cy="13" r="4"/>
                                    </svg>
                                    <span>摄像头</span>
                                </button>
                                
                                {/* 上传按钮 */}
                                <button
                                    style={ClassCardStyle.ContentNotExpandedContentLeftButton}
                                    onClick={() => {
                                        // 触发文件上传
                                        const fileInput = document.createElement('input');
                                        fileInput.type = 'file';
                                        fileInput.accept = 'image/*';
                                        fileInput.multiple = true;
                                        fileInput.style.display = 'none';
                                        
                                        fileInput.onchange = (e) => {
                                            // 处理文件上传逻辑
                                            const files = Array.from(e.target.files);
                                            files.forEach(file => {
                                                const reader = new FileReader();
                                                reader.onload = async (e) => {
                                                    try {
                                                        const imageUrl = e.target.result;
                                                        
                                                        // 加载图片并提取特征
                                                        const img = new Image();
                                                        img.crossOrigin = 'anonymous';
                                                        
                                                        // 等待图片加载完成
                                                        await new Promise((resolve, reject) => {
                                                            img.onload = resolve;
                                                            img.onerror = () => reject(new Error('图像加载失败'));
                                                            img.src = imageUrl;
                                                        });
                                                        
                                                        // 创建canvas并绘制图像
                                                        const canvas = document.createElement('canvas');
                                                        canvas.width = 227; // MobileNet输入大小
                                                        canvas.height = 227;
                                                        const ctx = canvas.getContext('2d');
                                                        ctx.drawImage(img, 0, 0, 227, 227);
                                                        
                                                        // 使用mobilenet提取特征
                                                        const tf = require('@tensorflow/tfjs');
                                                        const tensor = tf.browser.fromPixels(canvas);
                                                        const sampleLogits = mobilenetRef.current.infer(tensor, 'conv_pw_13_relu');
                                                        
                                                        // 添加样本到KNN分类器
                                                        if (knnRef.current) {
                                                            knnRef.current.addExample(sampleLogits, node.id);
                                                            console.log(`已添加样本到类别 ${node.id}，图片URL: ${imageUrl}`);
                                                        }
                                                        
                                                        // 创建新样本
                                                        const newSample = {
                                                            id: Date.now() + Math.random(),
                                                            imageUrl: imageUrl
                                                        };
                                                        
                                                        // 更新类别节点
                                                        setClassNodes(prev => 
                                                            prev.map(n => 
                                                                n.id === node.id 
                                                                    ? { ...n, samples: [...(n.samples || []), newSample] } 
                                                                    : n
                                                            )
                                                        );
                                                        
                                                        // 设置已有数据标志
                                                        setHasData(true);
                                                        
                                                        // 释放资源
                                                        tensor.dispose();
                                                        sampleLogits.dispose();
                                                    } catch (error) {
                                                        console.error('处理上传图片失败:', error);
                                                        notification.show('处理上传图片失败: ' + error.message, 'error');
                                                    }
                                                };
                                                reader.readAsDataURL(file);
                                            });
                                        };
                                        
                                        document.body.appendChild(fileInput);
                                        fileInput.click();
                                        document.body.removeChild(fileInput);
                                    }}
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="17 8 12 3 7 8"/>
                                        <line x1="12" y1="3" x2="12" y2="15"/>
                                    </svg>
                                    <span>上传</span>
                                </button>
                            </div>
                            

                            {/* 样本预览区域 */}
                            <div className={ClassCardCss.sampleScrollContainer}>
                                {node.samples && node.samples.map(sample => (
                                    <div
                                        key={sample.id}
                                        style={{
                                            width: '48px',
                                            height: '48px',
                                            minWidth: '48px',
                                            flexShrink: 0,
                                            borderRadius: '4px',
                                            backgroundImage: `url(${sample.imageUrl})`,
                                            backgroundSize: 'cover',
                                            backgroundPosition: 'center',
                                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                                            position: 'relative'
                                        }}
                                        onMouseEnter={(e) => {
                                            const deleteBtn = e.currentTarget.querySelector('button');
                                            if (deleteBtn) deleteBtn.style.display = 'block';
                                        }}
                                        onMouseLeave={(e) => {
                                            const deleteBtn = e.currentTarget.querySelector('button');
                                            if (deleteBtn) deleteBtn.style.display = 'none';
                                        }}
                                    >
                                        <button
                                            style={{
                                                position: 'absolute',
                                                top: 0,
                                                right: 0,
                                                width: '16px',
                                                height: '16px',
                                                background: 'rgba(0, 0, 0, 0.5)',
                                                color: 'white',
                                                border: 'none',
                                                borderRadius: '0 4px 0 4px',
                                                fontSize: '10px',
                                                lineHeight: 1,
                                                padding: 0,
                                                display: 'none',
                                                cursor: 'pointer'
                                            }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                // 删除样本逻辑
                                                setClassNodes(prev => 
                                                    prev.map(n => 
                                                        n.id === node.id 
                                                            ? { ...n, samples: n.samples.filter(s => s.id !== sample.id) } 
                                                            : n
                                                    )
                                                );
                                            }}
                                        >
                                            ×
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}
                
                {/* 展开内容 */}
                {node.expanded && (
                    <div style={ClassCardStyle.ContentExpanded}>
                        {/* 两列布局容器 */}
                            {/* 左侧摄像头区域 */}
                            <div style={ClassCardStyle.ContentExpandedContainerLeft}>
                                {/* 摄像头标题和控制区域 */}
                                <div style={ClassCardStyle.ContentExpandedContainerLeftHeader}>
                                    <div style={ClassCardStyle.ContentExpandedContainerLeftHeaderTitle}>
                                        摄像头
                                    </div>
                                    <div 
                                        style={ClassCardStyle.ContentExpandedContainerLeftHeaderCloseButton}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            // 关闭展开视图
                                            setClassNodes(prev => 
                                                prev.map(n => 
                                                    n.id === node.id ? { ...n, expanded: false } : n
                                                )
                                            );
                                        }}
                                    >
                                        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 6L6 18M6 6L18 18" stroke="#1890ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                </div>
                         
                                {/* 摄像头预览区域 - 修改为4:3比例 */}
                                <div
                                    id={`camera-container-${node.id}`}
                                    style={{
                                        ...CameraCardStyle.cameraContainer,
                                    }}
                                />
                                
                                {/* 录制按钮容器 */}
                                <div style={CameraCardStyle.cameraButtonContainer}>
                                    {/* 录制按钮 */}
                                    <button
                                        style={CameraCardStyle.cameraRecordButton}
                                        onMouseDown={() => {
                                            // 获取当前的录制设置
                                            const settings = recordSettingsRef.current;
                                            
                                            // 开始录制，传入设置参数
                                            // 注意：delayTime和duration需要转换为毫秒
                                            startRecording(
                                                settings.delayTime * 1000,  // 转换为毫秒
                                                settings.duration * 1000,   // 转换为毫秒
                                                settings.holdToRecord
                                            );
                                            
                                            // 用一个动画效果显示正在录制
                                            const button = document.activeElement;
                                            if (button) {
                                                button.style.background = '#3b52a5';
                                            }
                                        }}
                                        onMouseUp={() => {
                                            const settings = recordSettingsRef.current;
                                            if(settings.holdToRecord){
                                                 // 停止录制
                                                 stopRecording();
                                            }
                                            // 恢复按钮样式
                                            const button = document.activeElement;
                                            if (button) {
                                                button.style.background = '#4766C2';
                                            }
                                           
                                        }}
                                        onMouseLeave={() => {
                                            // 鼠标离开时也停止录制
                                            const settings = recordSettingsRef.current;
                                            if(settings.holdToRecord){
                                                 // 停止录制
                                                 stopRecording();
                                            }
                                            // 恢复按钮样式
                                            const button = document.activeElement;
                                            if (button) {
                                                button.style.background = '#4766C2';
                                            }
                                        }}
                                    >
                                        <span>开始录制</span>
                                    </button>
                                    
                                    {/* 设置按钮 */}
                                    <button
                                        style={CameraCardStyle.cameraSettingButton}
                                        onClick={() => {
                                            // 直接显示高级设置面板
                                            setShowSettingsPanel(true);
                                        }}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                            <circle cx="12" cy="12" r="3"></circle>
                                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                {/* 高级设置面板 */}
                                {showSettingsPanel && (
                                    <div style={CameraCardStyle.cameraSettingContainer}>
                                        <div style={CameraCardStyle.cameraSettingContainerHeader}>
                                            <h3 style={CameraCardStyle.cameraSettingContainerHeaderTitle}>设置</h3>
                                        </div>
                                        
                                        <div style={CameraCardStyle.cameraSettingContainerItem}>
                                            <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                                                <span>FPS:</span>
                                                <input 
                                                    type="number" 
                                                    value={recordSettings.fps} 
                                                    onChange={(e) => {
                                                        e.persist(); // 防止React事件池回收此事件
                                                        // 提取当前的值，避免后续引用已回收的事件对象
                                                        const newValue = e.target && e.target.value;
                                                        setRecordSettings(prev => ({
                                                            ...prev,
                                                            fps: Math.max(1, Math.min(30, parseInt(newValue) || 10))
                                                        }));
                                                    }}
                                                    style={CameraCardStyle.cameraSettingContainerItemInput}
                                                    min="1"
                                                    max="30"
                                                />
                                            </label>
                                        </div>
                                        
                                        <div style={CameraCardStyle.cameraSettingContainerItem}>
                                            <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                                                <span>按空格键即可录制</span>
                                                <div 
                                                    onClick={() => {
                                                        // 切换按住录制模式
                                                        const newCanSpaceRecord = !recordSettings.canSpaceRecord;
                                                        setRecordSettings(prev => ({
                                                            ...prev,
                                                            canSpaceRecord: newCanSpaceRecord
                                                        }));
                                                        
                                                    }}
                                                    style={{
                                                        width: '40px',
                                                        height: '20px',
                                                        backgroundColor: recordSettings.canSpaceRecord ? '#4766C2' : '#ccc',
                                                        borderRadius: '10px',
                                                        position: 'relative',
                                                        cursor: 'pointer',
                                                        transition: 'background-color 0.3s'
                                                    }}
                                                >
                                                    <div style={{
                                                        width: '16px',
                                                        height: '16px',
                                                        backgroundColor: 'white',
                                                        borderRadius: '50%',
                                                        position: 'absolute',
                                                        top: '2px',
                                                        left: recordSettings.canSpaceRecord ? '22px' : '2px',
                                                        transition: 'left 0.3s'
                                                    }} />
                                                </div>
                                            </label>
                                        </div>

                                        <div style={CameraCardStyle.cameraSettingContainerItem}>
                                            <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                                                <span>按住即可录制</span>
                                                <div 
                                                    onClick={() => {
                                                        // 切换按住录制模式
                                                        const newHoldToRecord = !recordSettings.holdToRecord;
                                                        setRecordSettings(prev => ({
                                                            ...prev,
                                                            holdToRecord: newHoldToRecord
                                                        }));
                                                        
                                                        // 如果切换到按住录制模式，并且正在录制，则停止录制
                                                        // if (newHoldToRecord && isCollecting) {
                                                        //     stopCollecting();
                                                        // }
                                                    }}
                                                    style={{
                                                        width: '40px',
                                                        height: '20px',
                                                        backgroundColor: recordSettings.holdToRecord ? '#4766C2' : '#ccc',
                                                        borderRadius: '10px',
                                                        position: 'relative',
                                                        cursor: 'pointer',
                                                        transition: 'background-color 0.3s'
                                                    }}
                                                >
                                                    <div style={{
                                                        width: '16px',
                                                        height: '16px',
                                                        backgroundColor: 'white',
                                                        borderRadius: '50%',
                                                        position: 'absolute',
                                                        top: '2px',
                                                        left: recordSettings.holdToRecord ? '22px' : '2px',
                                                        transition: 'left 0.3s'
                                                    }} />
                                                </div>
                                            </label>
                                        </div>
                                        
                                        <div style={{ 
                                            ...CameraCardStyle.cameraSettingContainerItem,
                                            opacity: recordSettings.holdToRecord ? 0.5 : 1,
                                            pointerEvents: recordSettings.holdToRecord ? 'none' : 'auto'
                                        }}>
                                            <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                                                <span>延迟时间 (秒):</span>
                                                <input 
                                                    type="number" 
                                                    value={recordSettings.delayTime} 
                                                    onChange={(e) => {
                                                        e.persist(); // 防止React事件池回收此事件
                                                        // 提取当前的值，避免后续引用已回收的事件对象
                                                        const newValue = e.target && e.target.value;
                                                        setRecordSettings(prev => ({
                                                            ...prev,
                                                            delayTime: Math.max(0, Math.min(10, parseFloat(newValue) || 0))
                                                        }));
                                                    }}
                                                    style={CameraCardStyle.cameraSettingContainerItemInput}
                                                    min="0"
                                                    max="10"
                                                    step="0.1"
                                                    disabled={recordSettings.holdToRecord}
                                                />
                                            </label>
                                        </div>
                                        
                                        <div style={{ 
                                            ...CameraCardStyle.cameraSettingContainerItem,
                                            opacity: recordSettings.holdToRecord ? 0.5 : 1,
                                            pointerEvents: recordSettings.holdToRecord ? 'none' : 'auto'
                                        }}>
                                            <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                                                <span>时长 (秒):</span>
                                                <input 
                                                    type="number" 
                                                    value={recordSettings.duration} 
                                                    onChange={(e) => {
                                                        e.persist(); // 防止React事件池回收此事件
                                                        // 提取当前的值，避免后续引用已回收的事件对象
                                                        const newValue = e.target && e.target.value;
                                                        setRecordSettings(prev => ({
                                                            ...prev,
                                                            duration: Math.max(1, Math.min(10, parseInt(newValue) || 3))
                                                        }));
                                                    }}
                                                    style={CameraCardStyle.cameraSettingContainerItemInput}
                                                    min="1"
                                                    max="10"
                                                    disabled={recordSettings.holdToRecord}
                                                />
                                            </label>
                                        </div>
                                        
                                        <div style={CameraCardStyle.cameraSettingContainerButtonContainer}>
                                            <div style={CameraCardStyle.cameraSettingContainerButtonContainer}>
                                                <button 
                                                    onClick={() => setShowSettingsPanel(false)}
                                                    style={CameraCardStyle.cameraSettingContainerButtonReturn}
                                                >
                                                    返回
                                                </button>
                                                
                                                <button 
                                                    onClick={() => {
                                                        // 保存设置到localStorage
                                                        try {
                                                            localStorage.setItem('imageRecordSettings', JSON.stringify(recordSettings));
                                                            console.log('设置已保存:', recordSettings);
                                                        } catch (error) {
                                                            console.error('设置保存失败:', error);
                                                        }
                                                        setShowSettingsPanel(false);
                                                    }}
                                                    style={CameraCardStyle.cameraSettingContainerButtonSave}
                                                >
                                                    保存设置
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            
                            {/* 右侧样本区域 */}
                            <div style={ClassCardStyle.ContentExpandedContainerRight}>
                                {/* 样本标题 */}
                                <div style={CameraCardStyle.sampleStats}>
                                    {node.samples.length === 0 ? 
                                        '添加图片样本:（应至少添加10个）' : 
                                        `${node.samples.length} 个图片样本（应至少添加10个）`
                                    }
                                </div>
                                
                                {/* 样本预览区域 */}
                                <div style={CameraCardStyle.samplesContentContainer}>
                                    {node.samples && node.samples.map(sample => (
                                        <div
                                            key={sample.id}
                                            style={{
                                                ...CameraCardStyle.sampleThumbnail,
                                                backgroundImage: `url(${sample.imageUrl})`,
                                            }}
                                            onMouseEnter={(e) => {
                                                const deleteBtn = e.currentTarget.querySelector('button');
                                                if (deleteBtn) deleteBtn.style.display = 'block';
                                            }}
                                            onMouseLeave={(e) => {
                                                const deleteBtn = e.currentTarget.querySelector('button');
                                                if (deleteBtn) deleteBtn.style.display = 'none';
                                            }}
                                        >
                                            <button
                                                style={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    right: 0,
                                                    width: '16px',
                                                    height: '16px',
                                                    background: 'rgba(0, 0, 0, 0.5)',
                                                    color: 'white',
                                                    border: 'none',
                                                    borderRadius: '0 4px 0 4px',
                                                    fontSize: '10px',
                                                    lineHeight: 1,
                                                    padding: 0,
                                                    display: 'none',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    // 删除样本逻辑
                                                    setClassNodes(prev => 
                                                        prev.map(n => 
                                                            n.id === node.id 
                                                                ? { ...n, samples: n.samples.filter(s => s.id !== sample.id) } 
                                                                : n
                                                        )
                                                    );
                                                }}
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                )}
            </div>
        );
    };

    //添加类别节点
    const addClassNode = () => {
        // 检查是否已达到最大类别数量限制
        if (classNodes.length >= 10) {
            notification.error('最多只能创建10个类别');
            return;
        }

        // 计算新的ID，确保不与现有类别冲突
        const newId = classNodes.length > 0 
            ? Math.max(...classNodes.map(node => node.id)) + 1 
            : 0;
        
        // 创建新节点
        const newNode = {
            id: newId,
            name: `类别 ${newId + 1}`,
            samples: [],
            expanded: false
        };
        
        // 添加新节点
        setClassNodes(prev => [...prev, newNode]);
        
        // 如果已经有训练好的模型，提示用户需要重新训练
        if (modelTrained) {
            notification.info('已添加新类别，需要重新训练模型');
        }
        
        // 添加后自动滚动到容器底部
        setTimeout(() => {
            if (classesPanelContentRef.current) {
                classesPanelContentRef.current.scrollTop = classesPanelContentRef.current.scrollHeight;
            }
        }, 100);
    };

    // 停止视频流
    const stopVideoStream = () => {
        if (videoStreamRef.current) {
            console.log('停止视频流轨道...');
            const tracks = videoStreamRef.current.getTracks();
            tracks.forEach(track => track.stop());
            videoStreamRef.current = null;
        }
    };
    
    // 初始化摄像头
    const initCamera = async (classId) => {
        try {
            // 停止现有视频流
            stopVideoStream();
            
            // 使用ref中存储的当前朝向
            const currentFacingMode = facingModeRef.current;
            
            // 请求摄像头权限
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: currentFacingMode,
                    width: { ideal: 320 },
                    height: { ideal: 240 }
                },
                audio: false
            });
            
            // 保存视频流引用
            videoStreamRef.current = stream;
            
            // 获取摄像头容器
            const cameraContainer = document.getElementById(`camera-container-${classId}`);
            if (cameraContainer) {
                // 清空容器
                cameraContainer.innerHTML = '';
                
                // 创建视频元素
                const video = document.createElement('video');
                video.setAttribute('playsinline', '');
                video.style.transform = currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)';
                video.style.width = '100%';
                video.style.height = '100%';
                video.style.objectFit = 'cover';
                
                // 设置视频源
                video.srcObject = stream;
                
                // 添加视频到容器
                cameraContainer.appendChild(video);
                
                // 播放视频
                await video.play();
                
                // 设置视频引用
                videoRef.current = video;
                
                // 创建翻转摄像头按钮
                const flipButton = document.createElement('div');
                flipButton.style.cssText = `
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background-color: rgba(0, 0, 0, 0.4);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    z-index: 10;
                    transition: background-color 0.3s;
                `;
                flipButton.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 5L19 8M19 8L16 11M19 8H13M10 19L7 16M7 16L10 13M7 16H13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
                flipButton.addEventListener('mouseenter', (e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
                });
                flipButton.addEventListener('mouseleave', (e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.4)';
                });
                flipButton.addEventListener('click', () => {
                    // 切换摄像头方向
                    const newFacingMode = facingModeRef.current === 'user' ? 'environment' : 'user';
                    facingModeRef.current = newFacingMode;
                    
                    // 重新初始化摄像头
                    initCamera(classId);
                });
                cameraContainer.appendChild(flipButton);
                
                // 确保当前类别处于展开状态
                setClassNodes(prev => 
                    prev.map(node => 
                        node.id === classId 
                            ? { ...node, expanded: true } 
                            : { ...node, expanded: false }
                    )
                );
            }
            
        } catch (error) {
            console.error('摄像头初始化失败:', error);
            notification.error('摄像头初始化失败: ' + error.message);
        }
    };

    //渲染数据图表
    const renderDataChart = () => {
        if (!showDataChart) return null;
    
        // 使用trainingHistory数据
        const currentTrainingHistory = trainingHistory;
        const hasTrainingData = currentTrainingHistory.epochs.length > 0;
    
        // --- 样式常量 ---
        // 图表容器的样式
        const chartPanelStyle = {
          position: 'absolute',
          top: 0,
          right: 0,
          width: '300px',
          height: '100%', 
          backgroundColor: 'white',
          boxShadow: '-4px 0 12px rgba(0, 0, 0, 0.1)',
          zIndex: 100,
          transform: showDataChart ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderLeft: '1px solid #eee'
        };
        
        const chartHeaderStyle = {
          padding: '16px',
          borderBottom: '1px solid #eee',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        };
        
        const chartContentStyle = {
          flex: 1,
          padding: '16px',
          overflowY: 'auto'
        };
        
        const closeButtonStyle = {
          background: 'none',
          border: 'none',
          fontSize: '20px',
          cursor: 'pointer',
          color: '#666'
        };
        
        const chartStyle = {
          marginBottom: '24px',
          padding: '16px',
          border: '1px solid #eee',
          borderRadius: '8px',
          backgroundColor: '#f9f9f9'
        };
        
        // 图表图标样式
        const chartIconStyle = {
          marginRight: '8px', 
          display: 'inline-flex',
          verticalAlign: 'middle',
          width: '20px',
          height: '20px',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#4f7df9'
        };
        
        // 介绍内容块样式
        const introBlockStyle = {
          padding: '12px',
          backgroundColor: '#f0f7ff',
          borderRadius: '8px',
          fontSize: '14px',
          lineHeight: 1.5,
          color: '#4a5568',
          marginBottom: '24px',
          border: '1px solid #e3eeff'
        };
        // --- 样式常量结束 ---
        
        return (
          <>
            <div style={chartPanelStyle}>
              <div style={chartHeaderStyle}>
                <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
                  <span style={chartIconStyle}>
                    <svg viewBox="0 0 24 24" width="20" height="20" stroke="currentColor" strokeWidth="2" fill="none">
                      <path d="M2 22h20M2 2v16h20V2H2z" />
                      <path d="M7 14l3-3 2 2 3-3 3 3" />
                    </svg>
                  </span>
                  数据图表 {isTraining ? '(训练中...)' : ''}
                </h3>
                <button 
                  style={closeButtonStyle}
                  onClick={() => setShowDataChart(false)}
                >
                  ×
                </button>
              </div>
              
              <div style={chartContentStyle}>
                {/* 添加介绍内容 */}
                <div style={introBlockStyle}>
                  <strong style={{ fontSize: '15px', display: 'block', marginBottom: '4px' }}>训练图表</strong>
                  <p style={{ margin: '0' }}>这里的图表可以帮助您了解模型的训练和运行情况</p>
                </div>
                
                {hasTrainingData ? (
                  <>
                    <div style={{...chartStyle, height: '220px'}}>
                      <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                        <span style={{ color: '#4f7df9', marginRight: '6px' }}>
                          <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M23 6l-9.5 9.5-5-5L1 18" />
                          </svg>
                        </span>
                        每个周期的准确度
                      </h4>
                      <div style={{ position: 'relative', height: '180px', width: '100%' }}>
                        <canvas 
                          ref={accChartRef} 
                          width="280" 
                          height="180"
                          style={{ width: '100%', height: '100%' }}
                        />
                      </div>
                    </div>
                    
                    <div style={{...chartStyle, height: '220px'}}>
                      <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                        <span style={{ color: '#4fb14f', marginRight: '6px' }}>
                          <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M3 3v18h18" />
                            <path d="M18 12l-6-6-6 6" />
                          </svg>
                        </span>
                        每个周期的损失
                      </h4>
                      <div style={{ position: 'relative', height: '180px', width: '100%' }}>
                        <canvas 
                          ref={lossChartRef} 
                          width="280" 
                          height="180"
                          style={{ width: '100%', height: '100%' }}
                        />
                      </div>
                    </div>
                    
                  </>
                ) : (
                  <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999', backgroundColor: '#f9f9f9', borderRadius: '8px', marginTop: '20px' }}>
                    {isTraining ? (
                      <>
                        <div style={{ margin: '0 auto 16px', width: '60px', height: '60px', position: 'relative' }}>
                          <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#4f7df9" strokeWidth="1.5" style={{ animation: 'spin 2s linear infinite', position: 'absolute', top: 0, left: 0 }}>
                            <style>{`
                              @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                              }
                            `}</style>
                            <circle cx="12" cy="12" r="10" strokeDasharray="30 40" strokeLinecap="round" />
                          </svg>
                        </div>
                        <p style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '8px' }}>正在训练模型...</p>
                        <p style={{ fontSize: '14px' }}>训练数据将实时显示在图表中</p>
                      </>
                    ) : (
                      <>
                        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#ccc" strokeWidth="1.5" style={{ margin: '0 auto 16px', display: 'block' }}>
                          <path d="M2 22h20M2 2v16h20V2H2z" />
                          <path d="M7 14l3-3 2 2 3-3 3 3" strokeDasharray="4" />
                        </svg>
                        <p style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '8px' }}>尚无训练数据</p>
                        <p style={{ fontSize: '14px' }}>训练模型后将在此显示训练过程的准确率和损失</p>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          </>
        );
      };


    const train = async () => {
        // 如果已经在训练中，直接返回
        if (isTraining) {
            return;
        }
        
        console.log('训练开始，先停止预览...');
        stopPreview();
        
        // 等待预览完全停止
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('预览已停止，开始检查样本...');

        // 检查是否有足够的样本
        let hasEnoughSamples = false;
        let classesWithSamples = 0;
        
        // 检查每个类别是否有至少10个样本
        for (const node of classNodes) {
            if (node.samples && node.samples.length >= 5) {
                classesWithSamples++;
            }
        }
        
        // 至少需要2个类别有足够的样本
        hasEnoughSamples = classesWithSamples >= 2;
        
        if (!hasEnoughSamples) {
            notification.error('请确保至少有两个类别，且每个类别至少有5个样本');
            return;
        }
        
        // 开始训练过程
        setIsTraining(true);
        setTraining(0); // 初始化训练进度为0%
        
        // 重置训练历史记录
        setTrainingHistory({
            epochs: [],
            trainAccuracy: [],
            trainLoss: [],
            valAccuracy: [],
            valLoss: []
        });
        setHasData(false);
    
        
        try {
            // 获取训练参数
            const { epochs, batchSize, learningRate } = trainParams;
            
            // 确保knn分类器已初始化
            if (!knnRef.current) {
                const knnClassifier = require('@tensorflow-models/knn-classifier');
                knnRef.current = knnClassifier.create();
            } else {
                // 清空现有的KNN分类器
                knnRef.current.clearAllClasses();
            }
            
            // 重新将所有样本添加到KNN分类器
            const tf = require('@tensorflow/tfjs');
            
            // 为每个类别的每个样本重新添加到KNN
            for (const node of classNodes) {
                if (node.samples && node.samples.length > 0) {
                    console.log(`为类别 ${node.name || `类别 ${node.id + 1}`} 添加 ${node.samples.length} 个样本`);
                    
                    // 对每个样本进行处理
                    for (const sample of node.samples) {
                        try {
                            // 从样本的图像URL创建图像元素
                            const img = new Image();
                            img.src = sample.imageUrl;
                            
                            // 等待图像加载
                            await new Promise(resolve => {
                                img.onload = resolve;
                            });
                            
                            // 转换为tensor
                            const imageTensor = tf.browser.fromPixels(img);
                            
                            // 使用MobileNet提取特征
                            const sampleLogits = mobilenetRef.current.infer(imageTensor, 'conv_pw_13_relu');
                            
                            // 添加到KNN分类器
                            knnRef.current.addExample(sampleLogits, node.id);
                            
                            // 释放内存
                            imageTensor.dispose();
                            sampleLogits.dispose();
                        } catch (error) {
                            console.error(`处理样本时出错:`, error);
                        }
                    }
                }
            }
            
            // 模拟训练过程 - 在实际应用中，这里应该是真正的训练代码
            for (let epoch = 1; epoch <= epochs; epoch++) {
                // 更新训练进度 - 修复进度计算，将其除以100以显示正确的小数比例
                const progress = Math.round((epoch / epochs) * 100) / 100;
                setTraining(progress);
                
                // 输出当前训练状态，显示正确的轮数和进度百分比
                console.log(`训练中: 第 ${epoch}/${epochs} 轮，进度: ${progress * 100}%`);
                
                // 模拟训练数据
                // 生成模拟的训练准确率和损失值
                let trainAcc, trainLoss, valAcc, valLoss;
                
                // 精确度：在前4轮使用随机数，后面全用1
                if (epoch <= 4) {
                    // 在前4轮使用随机数，范围从0.5到0.9
                    trainAcc = 0.5 + Math.random() * 0.4;
                    // 验证准确率稍低一些
                    valAcc = trainAcc - 0.05 - Math.random() * 0.1;
                } else {
                    // 从第5轮开始，精确度固定为1
                    trainAcc = 1.0;
                    valAcc = 1.0;
                }
                
                // 损失度：在20轮以内持续下降趋近于0，超过20轮直接为0
                if (epoch > 20) {
                    // 超过20轮，损失为0
                    trainLoss = 0.0;
                    valLoss = 0.0;
                } else {
                    // 在20轮以内，使用指数衰减函数计算损失
                    // 初始值0.8，逐渐下降
                    const initialLoss = 0.8;
                    const decayFactor = 3.5; // 控制下降速度的参数
                    
                    // 指数衰减函数: loss = initialLoss * e^(-decayFactor * progress)
                    // progress是从0到1的进度
                    const progress = (epoch - 1) / 19; // 从0到1
                    const baseLoss = initialLoss * Math.exp(-decayFactor * progress);
                    
                    // 添加一些随机波动，但确保随着进度增加波动减小
                    const fluctuation = 0.05 * (1 - progress) * (Math.random() * 2 - 1);
                    
                    // 确保损失不会小于0.01，除非是最后一轮
                    trainLoss = Math.max(0.01, baseLoss + fluctuation);
                    if (epoch === 20) {
                        trainLoss = 0.005; // 最后一轮特别小但不为0
                    }
                    
                    // 验证损失通常略高于训练损失
                    const valFluctuation = 0.07 * (Math.random() + 0.5); // 始终是正值
                    valLoss = trainLoss + valFluctuation;
                }
                
                console.log(`轮次 ${epoch}: 训练准确率=${trainAcc.toFixed(4)}, 训练损失=${trainLoss.toFixed(4)}, 验证准确率=${valAcc.toFixed(4)}, 验证损失=${valLoss.toFixed(4)}`);
                
                // 更新训练历史记录
                setTrainingHistory(prev => {
                    const newHistory = {
                        epochs: [...prev.epochs, epoch],
                        trainAccuracy: [...prev.trainAccuracy, trainAcc],
                        trainLoss: [...prev.trainLoss, trainLoss],
                        valAccuracy: [...prev.valAccuracy, valAcc],
                        valLoss: [...prev.valLoss, valLoss]
                    };
                    
                    // 当有数据时设置标志，以便图表可以显示
                    if (newHistory.epochs.length > 0) {
                        setHasData(true);
                    }
                    
                    return newHistory;
                });
                
                // 延长模拟训练延迟，使得训练过程更慢，并让图表有更多时间更新
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            // 训练完成
            setTraining(1);
            setModelTrained(true);
        
            console.log('训练完成，设置状态...');
            setIsTraining(false);
            
            // 等待状态更新完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            console.log('准备重新启动预览...');
            // 使用try-catch包装预览启动
            try {
                await startPreview();
                console.log('预览成功启动');
            } catch (error) {
                console.error('预览启动失败:', error);
                notification.error('预览启动失败，请尝试手动点击预览按钮');
            }
            
        } catch (error) {
            console.error('训练失败:', error);
            notification.error(`训练失败: ${error.message}`);
            
            // 重置训练状态
            setIsTraining(false);
            setTraining(-1);
        }
    };



    // 开始预览函数
    const startPreview = async () => {
        
        // 确保之前的预览已完全停止
        stopPreview();
        
        // 确保预测状态是false，然后设置为true
        if (predicting) {
            setPredicting(false);
            // 等待状态更新
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        try {
            console.log('开始请求摄像头访问...');
            // 获取摄像头权限
            const stream = await navigator.mediaDevices.getUserMedia({ video: true });
            console.log('成功获取摄像头访问权限');
            videoStreamRef.current = stream;
            
            // 获取预览视频元素
            const previewVideo = document.getElementById('preview-video');
            if (previewVideo) {
                previewVideo.srcObject = stream;
                previewVideo.style.display = 'block';
                
                // 确保预览容器（视频的父元素）可见
                const previewContainer = previewVideo.parentElement;
                if (previewContainer) {
                    console.log('显示预览容器...');
                    previewContainer.style.display = 'flex';
                }
                
                // 等待视频元素加载
                await new Promise(resolve => {
                    previewVideo.onloadedmetadata = () => {
                        console.log('视频元数据已加载');
                        resolve();
                    };
                });
                
                // 开始播放视频
                await previewVideo.play().catch(err => {
                    console.error('视频播放失败:', err);
                    throw err;
                });
                console.log('视频已开始播放');
                
                // 隐藏初始提示
                const initialPrompt = document.getElementById('preview-initial-prompt');
                if (initialPrompt) {
                    initialPrompt.style.display = 'none';
                }
            }
            
            // 设置预览状态
            setPredicting(true);
            
            // 开始预测
            await startPrediction(previewVideo);
            
        } catch (error) {
            console.error('无法访问摄像头:', error);
            notification.error('无法访问摄像头');
            setPredicting(false);
        }
    };
    
    // 停止预览函数
    const stopPreview = () => {
        console.log('开始停止预览...');
        
        // 停止预测循环
        if (predictionIntervalRef.current) {
            console.log('清除预测间隔...');
            clearInterval(predictionIntervalRef.current);
            predictionIntervalRef.current = null;
        }
        
        // 停止视频流
        if (videoStreamRef.current) {
            console.log('停止视频流轨道...');
            const tracks = videoStreamRef.current.getTracks();
            tracks.forEach(track => track.stop());
            videoStreamRef.current = null;
        }
        
        // 清空视频源
        const previewVideo = document.getElementById('preview-video');
        if (previewVideo) {
            console.log('清空视频元素...');
            if (previewVideo.srcObject) {
                const stream = previewVideo.srcObject;
                if (stream) {
                    const tracks = stream.getTracks();
                    tracks.forEach(track => track.stop());
                }
            }
            previewVideo.srcObject = null;
            previewVideo.style.display = 'none';
            
            // 找到并隐藏整个预览容器的白色div（视频的父元素）
            const previewContainer = previewVideo.parentElement;
            if (previewContainer) {
                console.log('隐藏预览容器...');
                previewContainer.style.display = 'none';
            }
            
            // 显示初始提示
            const initialPrompt = document.getElementById('preview-initial-prompt');
            if (initialPrompt) {
                initialPrompt.style.display = 'flex';
            }
        }
        
        // 重置预测结果
        setConfidences([]);
        setPredicting(false);
        
        console.log('预览停止完成');
    };
    
    // 开始预测
    const startPrediction = async (video) => {
        console.log('开始预测过程...');
        
        // 检查必要的模型组件
        if (!mobilenetRef.current || !knnRef.current) {
            console.error('模型组件未完全加载');
            notification.error('预测模型未完全加载，请重新加载页面');
            return;
        }
        
        // 检查KNN分类器是否有类别
        const numClasses = knnRef.current.getNumClasses();
        console.log(`当前KNN分类器类别数: ${numClasses}`);
        
        if (numClasses === 0) {
            console.error('KNN分类器没有任何类别数据，无法开始预测');
            notification.error('模型未训练或没有类别数据，请先训练模型');
            return;
        }
        
        // 创建预测间隔，如果已经存在则先清除
        if (predictionIntervalRef.current) {
            console.log('清除之前的预测间隔');
            clearInterval(predictionIntervalRef.current);
            predictionIntervalRef.current = null;
        }
        
        console.log('设置新的预测间隔...');
        predictionIntervalRef.current = setInterval(async () => {
            // 检查视频是否准备好
            if (!video || !video.videoWidth) {
                console.warn('视频未就绪，跳过当前预测帧');
                return;
            }
            
            try {
                // 获取视频帧
                const tf = require('@tensorflow/tfjs');
                const image = tf.browser.fromPixels(video);
                
                // 使用MobileNet提取特征
                const logits = mobilenetRef.current.infer(image, 'conv_pw_13_relu');
                
                // 使用KNN进行预测
                const numClasses = knnRef.current.getNumClasses();
                if (numClasses > 0) {
                    console.log(`开始KNN预测，类别数: ${numClasses}`);
                    const res = await knnRef.current.predictClass(logits, TOPK);
                    
                    // 准备预测结果
                    const newConfidences = [];
                    
                    // 使用 classNodesRef 而不是 classNodes 获取最新的类别名称
                    const classNames = classNodesRef.current.map(node => node.name || `类别 ${node.id + 1}`);
                    
                    // 构建每个类别的置信度信息
                    Object.keys(res.confidences).forEach((i) => {
                        const confidence = res.confidences[i] || 0;
                        newConfidences.push({
                            id: i,
                            name: classNames[i] || `类别 ${i + 1}`,
                            confidence: confidence
                        });
                    });
                    
                    // 更新预测结果状态
                    setConfidences(newConfidences);
                } else {
                    console.warn('KNN分类器没有任何类别数据');
                }
                
                // 清理资源
                image.dispose();
                logits.dispose();
            } catch (error) {
                console.error('预测错误:', error);
                if (error.message && error.message.includes('must match the product of shape')) {
                    notification.error('特征形状不匹配，请重新训练模型');
                    clearInterval(predictionIntervalRef.current);
                    predictionIntervalRef.current = null;
                }
            }
        }, 100); // 每100毫秒预测一次
        
        console.log('预测循环已启动');
    };

    // 渲染预览头部函数
    const renderPreviewHeaderFunction = () => {
    return (
        <>
                {/* <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }}>
                    <div style={{
                        position: 'relative',
                        cursor: modelTrained ? 'pointer' : 'not-allowed',
                        opacity: modelTrained ? 1 : 0.5
                    }}>
                        <div 
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                background: '#f1f3f4',
                                padding: '6px 12px',
                                borderRadius: '4px',
                                fontSize: '14px',
                                color: '#202124'
                            }}
                            onClick={() => {
                                if (modelTrained) {
                                    notification.success('目前仅支持摄像头输入，未来会支持更多输入源');
                                } else {
                                    notification.error('请先训练模型');
                                }
                            }}
                        >
                            摄像头
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                        
                        <div style={{
                            position: 'absolute',
                            top: '100%',
                            left: 0,
                            right: 0,
                            marginTop: '4px',
                            background: 'white',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                            borderRadius: '4px',
                            zIndex: 10,
                            display: 'none' // 默认隐藏
                        }}>
                            <div style={{
                                padding: '8px 12px',
                                fontSize: '14px',
                                color: '#202124',
                                cursor: 'pointer'
                            }}>
                                摄像头
                            </div>
                            <div style={{
                                padding: '8px 12px',
                                fontSize: '14px',
                                color: '#9aa0a6', // 灰色表示未启用
                                cursor: 'not-allowed'
                            }}>
                                文件上传
                            </div>
                        </div>
                    </div>
                </div> */}
        </>
        );
    };

    // 渲染预览容器
    const renderPreviewContainer = () => {
        return (
            <div style={{
                width: '100%',
                flexDirection: 'column',
                height: '100%',
                display: modelTrained ? 'flex' : 'none'
            }}>
                {/* 预览区域 */}
                <div style={{
                    width: '100%',
                    aspectRatio: '4/3',
                    background: '#f8f9fa',
                    position: 'relative',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    marginBottom: '16px'
                }}>
                    {/* 视频元素 */}
                    <video 
                        id="preview-video"
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            display: 'none'
                        }}
                        autoPlay
                        muted
                        playsInline
                    />
                </div>
            </div>
        );
    };


    // 下载样本函数 - 将特定类别的样本打包下载
    const downloadSamples = (classId) => {
        // 查找指定类别
        const targetClass = classNodes.find(node => node.id === classId);
        
        // 检查是否有样本
        if (!targetClass || !targetClass.samples || targetClass.samples.length === 0) {
            notification.error('没有可下载的样本');
            return;
        }
        
        try {
            // 创建一个ZIP文件
            const JSZip = require('jszip');
            const zip = new JSZip();
            
            // 获取类别名称作为文件夹名
            const className = targetClass.name || `类别_${classId + 1}`;
            
            // 创建一个文件夹
            const folder = zip.folder(className);
            
            // 添加每个样本到ZIP文件
            targetClass.samples.forEach((sample, index) => {
                // 从Base64数据URL中提取图像数据
                const imageData = sample.imageUrl.replace(/^data:image\/(png|jpg|jpeg);base64,/, '');
                
                // 添加图像到ZIP
                folder.file(`样本_${index + 1}.jpg`, imageData, {base64: true});
            });
            
            // 生成ZIP文件
            zip.generateAsync({type: 'blob'}).then(content => {
                // 创建下载链接
                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(content);
                downloadLink.download = `${className}_样本.zip`;
                
                // 添加到文档并触发点击
                document.body.appendChild(downloadLink);
                downloadLink.click();
                
                // 清理
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(downloadLink.href);
                
                // 显示成功提示
                notification.success(`已下载 ${targetClass.samples.length} 个样本`);
            });
        } catch (error) {
            console.error('下载样本失败:', error);
            notification.error(`下载样本失败: ${error.message}`);
            
            // JSZip未安装时的错误处理
            if (error.message && error.message.includes('JSZip')) {
                notification.error('未安装JSZip模块，请先安装: npm install jszip');
            }
        }
    };





    // 训练主组件的属性
    const trainMainProps={
        //ref元素
        containerRef,
        classesPanelContentRef,

        //传入面板方法
        //渲染连接线
        //渲染预览内容面板
        
        //传入训练的属性或者方法
        //类别属性
        classNodes,
        //类别方法
        renderClassCard,
        addClassNode,

        //训练属性
        trainParams,
        setTrainParams,
        DefaultTrainParams,
        isTraining,
        training,
        advancedExpanded,
        setAdvancedExpanded,
        canTrainRef,

        //训练方法
        train,

        //传入预览的属性或者方法
        //预览属性
        modelTrained,
        predicting,
        confidences,

        //预览方法
        exportModelToBlocks,
        renderPreviewHeaderFunction,
        renderPreviewContainer,


        //ref,用于回传上面组件需要调用的方法
        trainMainFunctionRef,

        //数据图表
        showDataChart,
        setShowDataChart,
        renderDataChart,
    }



    return (
        <TrainMain {...trainMainProps} />
    )
}

export default ImageMode;
