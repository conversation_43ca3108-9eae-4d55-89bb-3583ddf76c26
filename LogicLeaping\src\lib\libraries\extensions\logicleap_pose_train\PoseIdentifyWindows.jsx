import React, { useState, useEffect, useRef, useCallback } from 'react';
import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
const tf = require('@tensorflow/tfjs');

// 添加十个固定的颜色
const classColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青绿色
    '#FFD166', // 黄色
    '#6A0572', // 紫色
    '#1A936F', // 绿色
    '#3D82AB', // 蓝色
    '#F77F00', // 橙色
    '#9D70CB', // 淡紫色
    '#5D576B', // 深紫色
    '#E63946'  // 深红色
];

// 自定义样式
const styles = {
    previewContainer: {
        width: '240px',
        height: '180px',
        position: 'relative',
        backgroundColor: '#f0f0f0',
        overflow: 'hidden',
        borderRadius: '8px',
        margin: '0 auto 16px auto'
    },
    button: {
        padding: '8px 16px',
        backgroundColor: '#4766C2',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
        margin: '8px 0',
        width: '100%'
    },
    buttonContainer: {
        display: 'flex',
        justifyContent: 'center',
        marginTop: '12px'
    },
    error: {
        color: 'red',
        textAlign: 'center',
        margin: '10px 0'
    }
};

const PoseIdentifyWindows = ({loadedModel}) => {
    // 状态管理
    const [predicting, setPredicting] = useState(false);
    const [error, setError] = useState('');
    const [confidences, setConfidences] = useState([]);
    const [currentPrediction, setCurrentPrediction] = useState(null);

    // Refs
    const previewVideoRef = useRef(null);
    const previewCanvasRef = useRef(null);
    const detectorRef = useRef(null);
    const predictionBufferRef = useRef([]);
    const isPreviewActiveRef = useRef(false);
    const requestRef = useRef(null);
    const facingModeRef = useRef('user');

    // 初始化检测器
    useEffect(() => {
        const initDetector = async () => {
            try {
                if (!window.poseDetection) {
                    console.error('姿态检测库未加载');
                    setError('姿态检测库未加载，请刷新页面重试');
                    return;
                }

                const model = await window.poseDetection.createDetector(
                    window.poseDetection.SupportedModels.MoveNet,
                    {
                        modelType: window.poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
                        modelUrl: '/static/utils/pose_train/model.json' // 指向本地模型文件
                    }
                );
                detectorRef.current = model;
                
                console.log('姿态检测器初始化完成');
                
                // 自动开始预览
                startPreview();
            } catch (error) {
                console.error('初始化检测器失败:', error);
                setError('初始化姿态检测器失败: ' + error.message);
            }
        };

        initDetector();

        startPreview();
        // 组件卸载时清理
        return () => {
            stopPreview();
        };
    }, []);

    // 开始预览
    const startPreview = async () => {
        if (!loadedModel) {
            setError('模型未加载');
            return;
        }

        try {
            // 停止现有视频流
            stopVideoStream();
            
            const currentFacingMode = facingModeRef.current;

            // 创建新的视频流
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: currentFacingMode,
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                },
                audio: false
            });
            
            const transformValue = currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)';

            // 获取预览容器
            const previewContainer = document.getElementById('preview-container');
            if (previewContainer) {
                // 清空容器
                previewContainer.innerHTML = '';
                
                // 使用现有的或创建新的视频元素
                if (!previewVideoRef.current) {
                    previewVideoRef.current = document.createElement('video');
                    previewVideoRef.current.id = 'preview-video';
                    previewVideoRef.current.setAttribute('playsinline', '');
                    previewVideoRef.current.setAttribute('autoplay', '');
                    previewVideoRef.current.setAttribute('muted', '');
                }
                
                // 使用现有的或创建新的画布
                if (!previewCanvasRef.current) {
                    previewCanvasRef.current = document.createElement('canvas');
                }
                
                // 设置视频源
                previewVideoRef.current.srcObject = stream;
                previewVideoRef.current.style.display = 'block';
                
                // 设置视频和画布样式
                previewVideoRef.current.style.cssText = `
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transform: ${transformValue};
                    display: block;
                `;
                
                previewCanvasRef.current.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    transform: ${transformValue};
                `;
                
                // 添加到容器
                previewContainer.appendChild(previewVideoRef.current);
                previewContainer.appendChild(previewCanvasRef.current);

                const flipButton = document.createElement('div');
                flipButton.style.position = 'absolute';
                flipButton.style.top = '8px';
                flipButton.style.right = '8px';
                flipButton.style.background = 'transparent';
                flipButton.style.color = 'white';
                flipButton.style.border = 'none';
                flipButton.style.width = '32px';
                flipButton.style.height = '32px';
                flipButton.style.padding = '0';
                flipButton.style.cursor = 'pointer';
                flipButton.style.display = 'flex';
                flipButton.style.alignItems = 'center';
                flipButton.style.justifyContent = 'center';
                flipButton.innerHTML = `
                  <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M24 6V42" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M4 34L16 12V34H4Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
                      <path d="M44 34H32V12L44 34Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
                  </svg>`;
                flipButton.addEventListener('click', () => toggleCameraFacing());
                previewContainer.appendChild(flipButton);

                previewContainer.style.display = 'block';
                
                // 播放视频
                await previewVideoRef.current.play();
                
                // 设置画布大小
                previewCanvasRef.current.width = previewVideoRef.current.videoWidth;
                previewCanvasRef.current.height = previewVideoRef.current.videoHeight;
            }
            
            // 重置预测缓冲区
            predictionBufferRef.current = [];
            
            // 更新状态
            isPreviewActiveRef.current = true;
            setPredicting(true);
            setError('');
            
            // 开始预测
            detectAndPredict();

        } catch (error) {
            console.error('启动预览失败:', error);
            setError('预览失败: ' + (error.message || '无法启动预览模式'));
            stopPreview();
        }
    };
    
    const toggleCameraFacing = () => {
        const newFacingMode = facingModeRef.current === 'user' ? 'environment' : 'user';
        facingModeRef.current = newFacingMode;
        startPreview();
    };

    // 停止视频流
    const stopVideoStream = () => {
        if (previewVideoRef.current && previewVideoRef.current.srcObject) {
            const tracks = previewVideoRef.current.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            previewVideoRef.current.srcObject = null;
        }
    };
    
    // 停止预览
    const stopPreview = () => {
        // 更新状态
        isPreviewActiveRef.current = false;
        setPredicting(false);
        
        // 停止视频流
        stopVideoStream();

        // 取消动画帧请求
        if (requestRef.current) {
            cancelAnimationFrame(requestRef.current);
            requestRef.current = null;
        }

        // 隐藏预览容器
        const previewContainer = document.getElementById('preview-container');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
        
        // 重置预测结果
        setCurrentPrediction(null);
        predictionBufferRef.current = [];
        setConfidences([]);
    };

    // 获取关键点名称
    const getKeypointName = (index) => {
        const keypointNames = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ];
        return keypointNames[index] || 'nose';
    };

    // 归一化姿态数据
    const normalizePose = (pose) => {
        if (!pose || !pose.keypoints || pose.keypoints.length === 0) return null;
        
        // 提取关键点
        const keypoints = pose.keypoints;
        
        // 检查关键点得分
        const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
        if (validKeypoints.length < 5) return null; // 至少需要5个有效关键点
        
        // 计算身体中心点
        let centerX = 0;
        let centerY = 0;
        let totalWeight = 0;
        
        validKeypoints.forEach(kp => {
            centerX += kp.x * kp.score;
            centerY += kp.y * kp.score;
            totalWeight += kp.score;
        });
        
        centerX /= totalWeight;
        centerY /= totalWeight;
        
        // 计算缩放因子
        let maxDist = 0;
        validKeypoints.forEach(kp => {
            const dist = Math.sqrt(
                Math.pow(kp.x - centerX, 2) + 
                Math.pow(kp.y - centerY, 2)
            );
            maxDist = Math.max(maxDist, dist);
        });
        
        // 防止除以零
        const scale = maxDist > 0 ? 1.0 / maxDist : 1.0;
        
        // 归一化所有关键点
        const normalizedData = [];
        
        // 确保所有17个关键点都有值
        for (let i = 0; i < 17; i++) {
            const keypoint = keypoints.find(kp => kp.name === getKeypointName(i));
            
            if (keypoint && keypoint.score > 0.3) {
                // 归一化坐标
                const x = (keypoint.x - centerX) * scale;
                const y = (keypoint.y - centerY) * scale;
                normalizedData.push(x, y);
            } else {
                // 对于缺失或低置信度的关键点，使用0值
                normalizedData.push(0, 0);
            }
        }
        
        return normalizedData;
    };

    // 预测姿态
    const predictPose = async (pose) => {
        if (!loadedModel || !isPreviewActiveRef.current) return;

        try {
            const videoWidth = previewVideoRef.current ? previewVideoRef.current.videoWidth : 640;
            const keypointsToPredict = facingModeRef.current === 'user' ?
                pose.keypoints.map(kp => ({ ...kp, x: videoWidth - kp.x })) :
                pose.keypoints;
            const poseToPredict = { ...pose, keypoints: keypointsToPredict };
            // 归一化姿态数据
            const normalizedPose = normalizePose(poseToPredict);
            
            if (!normalizedPose) return;
            
            // 确保TensorFlow.js已经加载
            if (!window.tf) {
                console.error('TensorFlow.js尚未加载，无法预测姿态');
                return;
            }
            
            // 使用TensorFlow预测
            const tf = window.tf;
            
            // 转换为张量
            const input = tf.tensor2d([normalizedPose], [1, 34], 'float32');
            
            // 预测
            const prediction = loadedModel.predict(input);
            const probabilities = await prediction.data();
            
            // 获取类别信息
            const classLabels = loadedModel.metadata && loadedModel.metadata.classLabels 
                ? loadedModel.metadata.classLabels 
                : Array.from({length: probabilities.length}, (_, i) => `类别${i+1}`);
            
            // 更新置信度数据
            const confidenceData = Array.from(probabilities).map((prob, index) => ({
                id: index,
                name: classLabels[index] || `类别${index+1}`,
                confidence: prob
            }));
            
            setConfidences(confidenceData);
            
            // 获取最高概率的类别
            const maxIndex = probabilities.indexOf(Math.max(...probabilities));
            const maxProbability = probabilities[maxIndex];
            
            // 设置置信度阈值
            const confidenceThreshold = 0.6;
            
            if (maxProbability >= confidenceThreshold) {
                const prediction = {
                    className: classLabels[maxIndex] || '未知',
                    confidence: maxProbability * 100
                };
                setCurrentPrediction(prediction);
            } else {
                setCurrentPrediction({
                    className: '未知姿态',
                    confidence: maxProbability * 100
                });
            }
            
            // 清理张量
            input.dispose();
            prediction.dispose();
            
        } catch (error) {
            console.error('预测错误:', error);
        }
    };

    // 检测和预测循环
    const detectAndPredict = async () => {
        if (!isPreviewActiveRef.current || !detectorRef.current || !previewVideoRef.current) {
            return;
        }

        try {
            // 检测姿态
            const poses = await detectorRef.current.estimatePoses(previewVideoRef.current);
            
            if (poses.length > 0) {
                const pose = poses[0];
                
                // 绘制骨架
                if (previewCanvasRef.current) {
                    const ctx = previewCanvasRef.current.getContext('2d');
                    ctx.clearRect(0, 0, previewCanvasRef.current.width, previewCanvasRef.current.height);
                    
                    // 绘制关键点
                    ctx.fillStyle = 'red';
                    pose.keypoints.forEach(keypoint => {
                        if (keypoint.score > 0.3) {
                            ctx.beginPath();
                            ctx.arc(keypoint.x, keypoint.y, 5, 0, 2 * Math.PI);
                            ctx.fill();
                        }
                    });
                    
                    // 绘制骨架连接线
                    const connections = [
                        ['nose', 'left_eye'], ['nose', 'right_eye'],
                        ['left_eye', 'left_ear'], ['right_eye', 'right_ear'],
                        ['nose', 'left_shoulder'], ['nose', 'right_shoulder'],
                        ['left_shoulder', 'right_shoulder'],
                        ['left_shoulder', 'left_elbow'], ['right_shoulder', 'right_elbow'],
                        ['left_elbow', 'left_wrist'], ['right_elbow', 'right_wrist'],
                        ['left_shoulder', 'left_hip'], ['right_shoulder', 'right_hip'],
                        ['left_hip', 'right_hip'],
                        ['left_hip', 'left_knee'], ['right_hip', 'right_knee'],
                        ['left_knee', 'left_ankle'], ['right_knee', 'right_ankle']
                    ];
                    
                    // 创建关键点映射
                    const keypointMap = {};
                    pose.keypoints.forEach(keypoint => {
                        keypointMap[keypoint.name] = keypoint;
                    });
                    
                    // 绘制连接线
                    ctx.strokeStyle = '#4766C2';
                    ctx.lineWidth = 4;
                    
                    connections.forEach(([p1Name, p2Name]) => {
                        const p1 = keypointMap[p1Name];
                        const p2 = keypointMap[p2Name];
                        
                        if (p1 && p2 && p1.score > 0.3 && p2.score > 0.3) {
                            ctx.beginPath();
                            ctx.moveTo(p1.x, p1.y);
                            ctx.lineTo(p2.x, p2.y);
                            ctx.stroke();
                        }
                    });
                }
                
                // 预测姿态
                await predictPose(pose);
            }
        } catch (error) {
            console.error('检测错误:', error);
        }

        // 如果仍在预览，继续检测
        if (isPreviewActiveRef.current) {
            requestRef.current = requestAnimationFrame(detectAndPredict);
        }
    };

    // 渲染预览面板
    const renderPreviewPanel = () => {
        return (
            <div style={trainMainStyle.panel}>
                <div style={trainMainStyle.panelContent}>
                    {renderPreviewContainer()}
                    {renderProgressContainer()}
                </div>
            </div>
        );
    };
    
    // 渲染预览容器
    const renderPreviewContainer = () => {
        return (
            <div style={{
                width: '100%',
                flexDirection: 'column',
                height: '100%',
                display: predicting ? 'block' : 'none'
            }}>
                {/* 预览区域 */}
                <div
                    id="preview-container"
                    style={{
                        width: '100%',
                        aspectRatio: '4/3',
                        background: '#f8f9fa',
                        position: 'relative',
                        overflow: 'hidden',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '8px',
                        marginBottom: '16px'
                    }}
                />
            </div>
        );
    };

    // 渲染类别置信度展示区域
    const renderProgressContainer = () => {
        return (
            <div style={{
                ...trainMainStyle.progressContainer,
                display: confidences.length > 0 ? 'block' : 'none'
            }}>
                <h4 style={trainMainStyle.progressHeaderText}>输出</h4>
                {/* 按原始顺序展示类别，不进行排序 */}
                {confidences.map((item, index) => (
                    <div key={`conf-${item.id}`} style={trainMainStyle.progressItem}>
                        {/* 类别名称 */}
                        <div style={{
                            ...trainMainStyle.progressItemName,
                            color: classColors[index % classColors.length]
                        }}>
                            {item.name}
                        </div>
                        
                        {/* 进度条容器 */}
                        <div style={trainMainStyle.progressBarContainer}>
                            {/* 进度条 */}
                            <div style={{
                                ...trainMainStyle.progressBar,
                                width: `${Math.round(item.confidence * 100)}%`,
                                backgroundColor: classColors[index % classColors.length], // 使用循环颜色
                            }} />
                            
                            {/* 百分比文字 */}
                            <div style={{ 
                                ...trainMainStyle.progressPercentage,
                                left: `${Math.round(item.confidence * 100)}%`,  // 使用相同的百分比
                            }}>
                                {Math.round(item.confidence * 100)}%
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <>
            {/* 预览面板 */}
            {renderPreviewPanel()}
        </>
    );
};

export default PoseIdentifyWindows;
