mysql:
  type: mysql
  host: ************ # 可以修改为生产环境主机地址
  port: 13306
  username: logicleap
  password: "123456" # 生产环境密码
  database: logicleap # 生产环境数据库名
  charset: utf8mb4
  entities:
    - "dist/**/*.entity{.ts,.js}"
  synchronize: false # 生产环境禁用自动同步
  # 添加连接池配置
  extra:
    connectionLimit: 100       # 生产环境更多连接
    waitForConnections: true
    queueLimit: 200           # 更大的队列
    acquireTimeout: 60000     # 获取连接超时时间
    timeout: 60000            # 查询超时时间
    reconnect: true           # 自动重连
    maxReconnects: 3          # 最大重连次数
  # 添加重试机制
  retryAttempts: 3
  retryDelay: 3000
  keepConnectionAlive: true
  connectTimeout: 60000

redis:
  host: ************
  port: 26379
  password: '12345678'
  db: 0