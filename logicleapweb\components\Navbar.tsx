/** @jsxImportSource react */
'use client'

import Link from 'next/link'
import Image from 'next/image'
import { UserCircle, Zap, LogOut, Settings, User, Palette, Heart, X, Link as LinkIcon, MessageCircle, Edit2, UserPlus } from 'lucide-react'
import { UserOutlined, FileOutlined, HeartOutlined, SettingOutlined, LogoutOutlined, TeamOutlined, UserSwitchOutlined } from '@ant-design/icons'
import { useContext, useEffect, useState } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import auth from '../lib/auth'
// import LoginDialog from './login-dialog' // 移除登录弹窗
import { pointsApi } from '../lib/api/points'
import { UserInfo } from '../types/user'
import ProfileModal from './profile-modal'
import { useDispatch, useSelector } from 'react-redux'
import { setUser, clearUser, RootState } from '../lib/store'
import { Menu, Dropdown, Tooltip, notification } from 'antd'
import { LogoutModal } from './logout-modal'
import PermissionModal from './permission-modal'
import PermissionTemplateModal from './permission-template-modal'
import RoleAssignModal from './role-assign-modal'
import { packageApi } from '../lib/api/package'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { HomeContext } from '@/app/(main)/home/<USER>'
import taskApi from '../lib/api/task'
import { announcementApi } from '../lib/api/announcement'
import { TaskStatus } from '../lib/api/task'
import { genId } from 'wiz-editor-react'
import userApi from '@/lib/api/user'
import RoleSwitcher from './role-switcher'
import RoleSwitcherModal from './role-switcher-modal'
import ModifyPhoneModal from './modify-phone-modal'
import { message } from 'antd'//导入新用户身份选择组件
import RoleSelectionModalForNewUser from '../app/components/userAuth/RoleSelectionModalForNewUser';
import RoleSelectionTransition from './RoleSelectionTransition';
import InitialPasswordModal from '../app/login/components/initial-password-modal';

import { GetNotification } from 'logic-common/dist/components/Notification';
// 定义导航项的类型
interface NavItem {
  label: string;
  href?: string;
  action?: () => void;
  target?: string;
}

export default function Navbar() {
  // const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)
  const isLoggedIn = useSelector((state: RootState) => state.user.userState.isLoggedIn)
  const userInfo = useSelector((state: RootState) => state.user.userState)
  const [localUserInfo, setLocalUserInfo] = useState<Partial<UserInfo> | null>(null)  // 添加用户信息状态
  const [showBindPhoneModal, setShowBindPhoneModal] = useState(false)
  const pathname = usePathname()
  const [scrollProgress, setScrollProgress] = useState(0)
  const [points, setPoints] = useState<number>(0)
  const [availablePoints, setAvailablePoints] = useState<number>(0)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [menuTimeout, setMenuTimeout] = useState<NodeJS.Timeout | null>(null)
  const [showProfileModal, setShowProfileModal] = useState(false)
  const router = useRouter()
  const dispatch = useDispatch()
  const userId = useSelector((state: RootState) => state.user.userState.userId)
  const roleId = useSelector((state: RootState) => state.user.userState.roleId)
  const [activeSection, setActiveSection] = useState<string | null>(null)
  const [showLogoutModal, setShowLogoutModal] = useState(false)
  const [permissionModalVisible, setPermissionModalVisible] = useState(false)
  const [permissionTemplateModalVisible, setPermissionTemplateModalVisible] = useState(false)
  const [roleAssignModalVisible, setRoleAssignModalVisible] = useState(false)
  const [packageDetails, setPackageDetails] = useState<any[]>([])
  const [hasUnreadMessage, setHasUnreadMessage] = useState(false)
  const [hasUnreadNotification, setHasUnreadNotification] = useState(false)
  const [showRoleSwitcherModal, setShowRoleSwitcherModal] = useState(false)
  // 新用户身份选择state
  const [showRoleSelectionModalForNewUser, setShowRoleSelectionModalForNewUser] = useState(false)
  // 初始密码设置弹框状态
  const [showInitialPasswordModal, setShowInitialPasswordModal] = useState(false)
  //获取消息中心弹出的值
  const { isMessageCenterVisible, setIsMessageCenterVisible } = useContext(HomeContext);


  // 1.判断是否角色Id为999未知从而弹出身份认证
  const [hasCheckedRole999, setHasCheckedRole999] = useState(false)

  const nt = GetNotification()


  // 获取用户信息的函数
  const fetchUserInfo = async () => {
    if (auth.isLoggedIn()) {
      try {
        // 获取当前用户ID
        const userStr = localStorage.getItem('user')
        const user = userStr ? JSON.parse(userStr) : null
        const userId = user?.userId || localStorage.getItem('userId')

        // 如果没有用户ID，无法获取用户信息
        if (!userId) {
          console.error('No userId found in localStorage');
          return;
        }

        const response = await userApi.getUserInfo(userId);
        if (response && response.code === 200) {
          // 确保用户数据存在
          const userDataResponse = response.data || {};
          setLocalUserInfo(userDataResponse);

          // 检查是否需要显示手机号绑定提示
          if (!userDataResponse.phone &&
            userDataResponse.register_type === 'weixin' &&
            userDataResponse.createTime || !userDataResponse.phone && userDataResponse.roleId === 1) {

            // // 计算账号创建时间是否在一分钟内
            // const createTime = new Date(userDataResponse.createTime);
            // const currentTime = new Date();
            // const timeDiff = (currentTime.getTime() - createTime.getTime()) / 1000 / 60; // 分钟差

            // if (timeDiff <= 1) {
            //   console.log('新微信注册用户，需要绑定手机号');

            //   setShowBindPhoneModal(true);
            // }

            // 检查是否有"刚刚登录"标志和是否缺少手机号
            // const justLoggedIn = localStorage.getItem('justLoggedIn') === 'true';

            // if (justLoggedIn) {
            //   if (!userDataResponse.phone) {
            //     console.log('用户缺少手机号且刚刚登录，显示绑定弹窗');
            //     setShowBindPhoneModal(true);
            //   }
            //   // 检查是否需要弹出绑定密码弹窗
            //   // 如果后台查询用户的密码为空，则附带需要设置密码的标志位
            //   if (userDataResponse.needSetPwd) {
            //     // 弹出初始化密码的弹框
            //     setShowInitialPasswordModal(true);
            //   }
            //   // 移除标志，这样页面刷新时不会再次显示
            //   localStorage.removeItem('justLoggedIn');
            // }
          }




          // 确保Redux状态也更新
          const userData = {
            userId: response.data.id,
            nickName: response.data.nickName,
            avatarUrl: response.data.avatarUrl,
            gender: response.data.gender,
            phone: response.data.phone,
            isLoggedIn: true,
            roleId: response.data.roleId,
            registerType: response.data.register_type || '' // 添加注册类型
          };

          dispatch(setUser(userData));
          localStorage.setItem('user', JSON.stringify(userData));
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    }
  };


  // 获取最新的用户信息并检查是否需要显示角色选择弹窗
  const checkLatestUserRoleAndShowModal = async () => {
    try {
      console.log("获取最新用户信息");
      if (!userId) {
        console.log("没有用户ID，无法获取用户信息");
        return;
      }

      const response = await userApi.getUserInfo(userId);
      if (response && response.code === 200 && response.data) {
        const latestRoleId = response.data.roleId;
        const latestRegisterType = response.data.register_type || '';

        console.log("获取到最新用户信息 - 注册类型:", latestRegisterType, "角色ID:", latestRoleId);

        // 如果是微信用户且roleId为999，显示角色选择弹窗
        if (latestRegisterType === 'weixin' && latestRoleId === 999) {
          console.log("最新用户信息符合条件，显示角色选择弹窗");
          setTimeout(() => {
            console.log("执行setShowRoleModal(true)");
            setShowRoleSelectionModalForNewUser(true);
          }, 500);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("获取用户信息失败:", error);
      return false;
    }
  };

  // 获取用户积分
  const fetchUserPoints = async () => {
    try {
      console.log('开始获取用户积分，roleId =', roleId);

      // 如果roleId还未加载，尝试从localStorage获取
      const userJson = localStorage.getItem('user');
      const userRole = roleId || (userJson ? JSON.parse(userJson)?.roleId : null);

      console.log('用于积分获取的角色ID:', userRole);

      // 如果是学生用户，需要获取可用积分
      if (userRole === 1) {
        // 获取学生可用积分
        const permissionResponse = await pointsApi.getTotal();
        console.log('获取学生可用积分', permissionResponse);
        if (permissionResponse.data.code === 200) {
          const permissions = permissionResponse.data.data;
          // 计算未过期的可用积分总和
          const totalAvailable = Number(typeof permissions === 'object' ? (permissions as { total: number }).total : permissions);
          console.log('学生de可用积分', totalAvailable);
          setAvailablePoints(totalAvailable);
        }
      } else {
        // 获取用户总积分
        const { data: response } = await pointsApi.getTotal();
        console.log('获取用户总积分', response);
        if (response.code === 200) {
          setPoints(Number(typeof response.data === 'object' ? (response.data as { total: number }).total : response.data));
        }
      }

    } catch (error) {
      console.error('获取积分失败:', error);
    }
  };

  // 获取用户套餐详情
  const fetchPackageDetails = async () => {
    try {
      if (!userId) return;
      const response = await packageApi.getUserPackageDetails(userId);
      if (response.data.code === 200) {
        setPackageDetails(response.data.data);
      }
    } catch (error) {
      console.error('获取套餐详情失败:', error);
    }
  };

  // 3.初始化检测是否需要弹出身份认证
  useEffect(() => {
    // 检查是否需要进行身份认证
    const needRoleAuth = localStorage.getItem('needRoleAuth');
    // 移除这里的检查，因为已经在登录表单中处理
    localStorage.removeItem('needRoleAuth');
  }, [userInfo]);

  // 组件初始化时检查登录状态并获取用户信息
  useEffect(() => {
    console.log('Navbar组件初始化，检查登录状态');

    // 首先检查localStorage中的token和user
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    const userJson = localStorage.getItem('user');
    const userId = localStorage.getItem('userId');

    console.log('初始化状态检查：', {
      token: token ? '存在' : '不存在',
      refreshToken: refreshToken ? '存在' : '不存在',
      user: userJson ? '存在' : '不存在',
      userId
    });

    // 如果有token但Redux中未登录，尝试恢复登录状态
    if (token && !isLoggedIn) {
      console.log('检测到token但Redux未登录，尝试恢复状态');

      if (userJson) {
        try {
          // 如果localStorage中有user数据，直接恢复到Redux
          const userData = JSON.parse(userJson);
          dispatch(setUser({ ...userData, isLoggedIn: true }));
          console.log('从localStorage恢复用户数据到Redux', userData);

          // 这是页面刷新的情况，确保不显示手机号绑定弹窗
          localStorage.removeItem('justLoggedIn');
        } catch (e) {
          console.error('解析localStorage中的user数据失败', e);
        }
      }

      // 无论如何都尝试获取一次用户信息
      fetchUserInfo();
    } else if (!token && refreshToken && !isLoggedIn) {
      // 没有token但有refreshToken，尝试无感刷新
      console.log('🔄 Navbar初始化检测到没有token但有refreshToken，尝试无感刷新');

      const attemptRefresh = async () => {
        try {
          const response = await fetch('/api/router-guard/refresh-token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              refreshToken: refreshToken
            })
          });

          const data = await response.json();

          if (response.ok && data.code === 200) {
            console.log('✅ Navbar初始化无感刷新成功');
            // 更新token
            localStorage.setItem('token', data.data.token);
            localStorage.setItem('refreshToken', data.data.refreshToken);

            // 获取用户信息
            await fetchUserInfo();
            await fetchUserPoints();
            await fetchPackageDetails();
          } else {
            console.log('❌ Navbar初始化无感刷新失败，清除refreshToken');
            localStorage.removeItem('refreshToken');
          }
        } catch (error) {
          console.error('❌ Navbar初始化无感刷新异常:', error);
          localStorage.removeItem('refreshToken');
        }
      };

      attemptRefresh();
    }

    // 获取积分和其他信息
    if (token) {
      fetchUserPoints();
      fetchPackageDetails();
    }
  }, []);

  // // 1. 用户认证、信息和积分相关的 useEffect
  // useEffect(() => {
  //   console.log("Navbar useEffect [auth检查] 被调用");
  //   const initializeUserData = async () => {
  //     // 1.1 检查登录状态
  //     const isLogged = auth.isLoggedIn();
  //     if (!isLogged) return;

  //     try {
  //       // 1.2 获取用户信息
  //       const userId = Number(localStorage.getItem("userId"))
  //       if (!userId) {
  //         console.warn('初始化用户数据失败: 用户ID不存在');
  //         handleLogout();
  //         return;
  //       }

  //       const response = await userApi.getUserInfo(userId);
  //       if (response.code === 200 && response.data) {
  //         // 确保必要的用户信息存在
  //         if (!response.data.roleId) {
  //           throw new Error('获取用户角色信息失败');
  //         }

  //         const userData = {
  //           userId: response.data.id,
  //           nickName: response.data.nickName,
  //           avatarUrl: response.data.avatarUrl,
  //           gender: response.data.gender,
  //           phone: response.data.phone,
  //           isLoggedIn: true,
  //           roleId: response.data.roleId
  //         };

  //         // 更新状态
  //         dispatch(setUser(userData));
  //         setLocalUserInfo(response.data as unknown as UserInfo);
  //         localStorage.setItem('user', JSON.stringify(userData));

  //         // 获取积分
  //         await fetchUserPoints();
  //       } else {
  //         throw new Error('获取用户信息失败');
  //       }
  //     } catch (error) {
  //       console.error('初始化用户数据失败:', error);
  //       handleLogout();
  //     }
  //   };

  //   initializeUserData();

  //   // 1.3 监听积分更新事件
  //   const handlePointsUpdate = (event: CustomEvent) => {
  //     const { points, change } = event.detail;


  //     // 根据用户角色更新不同的积分显示
  //     if (roleId === 1) {
  //       // 学生用户 - 更新可用能量
  //       setAvailablePoints(Number(points));
  //     } else {
  //       // 其他用户 - 更新总能量
  //       setPoints(Number(points));
  //     }
  //   };

  //   // 监听用户状态更新事件 - 添加这段代码处理扫码登录状态同步
  //   const handleUserStateUpdate = (event: Event) => {
  //     console.log('接收到用户状态更新事件', (event as CustomEvent).detail);
  //     const { isLoggedIn, userData } = (event as CustomEvent).detail;

  //     if (isLoggedIn && userData) {
  //       // 用户已登录，刷新用户信息和积分
  //       fetchUserInfo();
  //       fetchUserPoints();
  //     }
  //   };

  //   window.addEventListener('pointsUpdate', handlePointsUpdate as EventListener);
  //   window.addEventListener('userStateUpdate', handleUserStateUpdate as EventListener);

  //   // 1.4 设置登录检查
  //   let loginCheckCleanup: (() => void) | undefined;
  //   if (auth.isLoggedIn()) {
  //     loginCheckCleanup = auth.startLoginCheck();
  //   }

  //   // 清理函数
  //   return () => {
  //     window.removeEventListener('pointsUpdate', handlePointsUpdate as EventListener);
  //     window.removeEventListener('userStateUpdate', handleUserStateUpdate as EventListener);
  //     if (loginCheckCleanup) {
  //       loginCheckCleanup();
  //     }
  //   };
  // }, []); // 只在组件挂载时执行一次

  const handleLoginSuccess = async () => {
    try {
      // 设置"刚刚登录"的标志，用于控制手机号绑定弹窗
      // localStorage.setItem('justLoggedIn', 'true');

      await fetchUserInfo();
      await fetchUserPoints();
      
    } catch (error) {
      console.error('登录后获取用户信息失败:', error);
      // 如果登录后获取用户信息失败，但是用户已经登录了，只是网络问题，
      handleLogout();
    }
  };

  const handleLogout = () => {
    console.log("zww:退出登录，当前是在Navbar组件中的handleLogout函数中,并且触发了清除Redux中的用户信息");
    auth.logout()
    dispatch(clearUser())
    localStorage.removeItem('user')
    localStorage.removeItem('userId')
    localStorage.removeItem('token')  // 确保清除 token
    setLocalUserInfo(null)
    setPoints(0)  // 清除积分
    router.push('/home')  // 可选：后跳转到首页
  }

  // 监听跨标签页的登录状态
  useEffect(() => {
    const handleStorageChange = async (event: StorageEvent) => {
      if (event.key === 'token') {
        if (event.newValue) {
          // 在另一个标签页登录
          handleLoginSuccess();
        } else {
          // token被删除，检查是否有refreshToken
          const refreshToken = localStorage.getItem('refreshToken');

          if (refreshToken) {
            console.log('🔄 Navbar检测到token被删除但有refreshToken，尝试无感刷新');

            try {
              // 尝试使用refreshToken刷新
              const response = await fetch('/api/router-guard/refresh-token', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  refreshToken: refreshToken
                })
              });

              const data = await response.json();

              if (response.ok && data.code === 200) {
                console.log('✅ Navbar无感刷新成功');
                // 更新token
                localStorage.setItem('token', data.data.token);
                localStorage.setItem('refreshToken', data.data.refreshToken);

                // 刷新用户信息
                await handleLoginSuccess();
              } else {
                console.log('❌ Navbar无感刷新失败，执行登出');
                handleLogout();
              }
            } catch (error) {
              console.error('❌ Navbar无感刷新异常:', error);
              handleLogout();
            }
          } else {
            // 没有refreshToken，直接登出
            console.log('🚪 Navbar检测到token和refreshToken都不存在，执行登出');
            handleLogout();
          }
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 检查当前路径和区域
  const isActive = (item: NavItem) => {
    // 首页特殊处理：只有当路径是/home且没有活跃区域，或者当前路径是/或/home时，首页才处于活跃状态
    if (item.label === '首页') {
      // 首页项在首页路径且没有活跃区域时激活
      if ((pathname === '/home' || pathname === '/') && !activeSection) {
        return true
      }
      return false
    }

    // 其他导航项处理
    if (pathname === '/home') {
      if (item.label === '创造' && activeSection === 'creation') return true
      if (item.label === '发现' && activeSection === 'gallery') return true
    }
    return pathname === item.href
  }

  // 修改滚动函数，添加区域设置
  const scrollToCreationTools = () => {
    const creationToolsSection = document.querySelector('.creation-tools-section')
    if (creationToolsSection) {
      setActiveSection('creation')
      const navbarHeight = 56
      const elementPosition = creationToolsSection.getBoundingClientRect().top + window.scrollY
      const offsetPosition = elementPosition - navbarHeight - 20

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }

  const scrollToGallery = () => {
    const gallerySection = document.querySelector('.showcase-content')
    if (gallerySection) {
      setActiveSection('gallery')
      const navbarHeight = 56
      const elementPosition = gallerySection.getBoundingClientRect().top + window.scrollY
      const offsetPosition = elementPosition - navbarHeight - 20

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }

  const scrollToTop = () => {
    setActiveSection(null)
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  // 修改导航项
  const navItems: NavItem[] = [
    {
      label: '首页',
      action: () => {
        if (pathname !== '/home') {
          router.replace('/home')
        } else {
          scrollToTop()
        }
      }
    },
    {
      label: '创造',
      action: () => {
        if (pathname !== '/home') {
          router.replace('/home?section=creation')
        } else {
          scrollToCreationTools()
        }
      }
    },
    {
      label: '发现',
      action: () => {
        if (pathname !== '/home') {
          router.replace('/home?section=gallery')
        } else {
          scrollToGallery()
        }
      }
    },
    // 移除编辑器按钮
    { label: '资源中心', href: '/resources' },
    { label: '关于我们', href: '/about' },
  ]

  // 2. 滚动和路由相关的 useEffect
  useEffect(() => {
    // 2.1 处理滚动进度
    const handleProgressScroll = () => {
      const winScroll = document.documentElement.scrollTop
      const height = document.documentElement.scrollHeight - document.documentElement.clientHeight
      const scrolled = (winScroll / height) * 100
      setScrollProgress(scrolled / 100)
    }

    // 2.2 处理区域滚动
    const handleSectionScroll = () => {
      if (pathname === '/home') {
        const creationSection = document.querySelector('.creation-tools-section')
        const gallerySection = document.querySelector('.showcase-content')
        const scrollPosition = window.scrollY + 100

        if (creationSection && gallerySection) {
          const creationTop = creationSection.getBoundingClientRect().top + window.scrollY
          const galleryTop = gallerySection.getBoundingClientRect().top + window.scrollY

          if (scrollPosition < creationTop) {
            setActiveSection(null)
          } else if (scrollPosition >= creationTop && scrollPosition < galleryTop) {
            setActiveSection('creation')
          } else {
            setActiveSection('gallery')
          }
        }
      }
    }

    // 2.3 处理 URL 参数
    const params = new URLSearchParams(window.location.search)
    const section = params.get('section')

    if (section === 'creation') {
      scrollToCreationTools()
    } else if (section === 'gallery') {
      scrollToGallery()
    }

    // 添加滚动监听器
    window.addEventListener('scroll', handleProgressScroll)
    window.addEventListener('scroll', handleSectionScroll)

    // 清理函数
    return () => {
      window.removeEventListener('scroll', handleProgressScroll)
      window.removeEventListener('scroll', handleSectionScroll)
    }
  }, [pathname])

  // 3. UI 状态相关的 useEffect
  useEffect(() => {
    // 3.1 处理菜单超时
    const menuCleanup = () => {
      if (menuTimeout) {
        clearTimeout(menuTimeout)
      }
    }

    // 3.2 处理模态框滚动
    if (showProfileModal) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // 清理函数
    return () => {
      menuCleanup()
      document.body.style.overflow = 'unset'
    }
  }, [menuTimeout, showProfileModal])

  // 4. 登录状态变化的监听
  useEffect(() => {
    if (userInfo && userInfo.isLoggedIn) {
      // 将Redux中的用户信息同步到本地状态
      setLocalUserInfo(prev => ({
        ...prev,
        ...userInfo,
      }));
    }
  }, [userInfo]);

  // 在用户登录状态变化时获取套餐详情
  useEffect(() => {
    if (userInfo && userInfo.isLoggedIn && userId) {
      fetchPackageDetails();
    }
  }, [userInfo, userId]);

  const handleProfileUpdate = async () => {
    // 不需要在这里重新获取用户信息，因为ProfileModal已经更新了Redux中的用户信息
    // await fetchUserInfo()  // 移除这一行，避免重复请求
  }

  const handleLogin = async () => {
    try {


    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  // 导航菜单项的样式
  const menuItems = [
    {
      icon: <UserOutlined className="w-5 h-5" />,
      label: '个人信息',
      href: '/profile'
    },
    // 添加角色切换选项
    {
      icon: <UserSwitchOutlined className="w-5 h-5" />,
      label: '切换账号',
      component: <RoleSwitcher />
    },
    // 根据角色显示不同的空间入口
    ...(roleId === 2 ? [{
      icon: <TeamOutlined className="w-5 h-5" />,
      label: '教师空间',
      href: '/teacher-space'
    }] : roleId === 1 ? [{
      icon: <TeamOutlined className="w-5 h-5" />,
      label: '班级空间',
      href: '/class-space'
    }] : roleId === 4 ? [{
      icon: <TeamOutlined className="w-5 h-5" />,
      label: '管理员空间',
      href: '/admin-space'
    }, {
      icon: <TeamOutlined className="w-5 h-5" />,
      label: '运营空间',
      href: '/operation-space'
    }] : roleId === 5 ? [{
      icon: <TeamOutlined className="w-5 h-5" />,
      label: '运营空间',
      href: '/operation-space'
    }] : []),
    {
      icon: <FileOutlined className="w-5 h-5" />,
      label: '我的空间',
      href: '/my-works'
    },

    // {
    //   icon: <HeartOutlined className="w-5 h-5" />,
    //   label: '我的点赞',
    //   href: '/my-likes'
    // },
    // {
    //   icon: <SettingOutlined className="w-5 h-5" />,
    //   label: '设置',
    //   href: '/settings'
    // },
    {
      icon: <LogoutOutlined className="w-5 h-5 text-red-500" />,
      label: '退出登录',
      onClick: handleLogout,
      className: 'text-red-500'
    }
  ].map(item => (
    <Menu.Item
      key={item.label}
      icon={item.icon}
      className={`flex items-center gap-3 px-4 py-2 hover:bg-gray-50 ${item.className || ''}`}
    >
      {item.component ? (
        item.component
      ) : item.href ? (
        <Link href={item.href} className="w-full">
          {item.label}
        </Link>
      ) : (
        <span onClick={item.onClick} className="w-full cursor-pointer">
          {item.label}
        </span>
      )}
    </Menu.Item>
  ));

  const handleLogoutClick = () => {
    setShowLogoutModal(true)
  }

  useEffect(() => {
    let cleanup: () => void;

    if (userInfo && userInfo.isLoggedIn) {
      cleanup = auth.startLoginCheck();
    }

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [userInfo]);

  // 修改 renderPackageTooltip 函数
  const renderPackageTooltip = () => {
    // 检查是否有套餐数据
    if (!packageDetails || packageDetails.length === 0) {
      return (
        <div className="max-w-xs">
          <div className="text-sm font-medium mb-2 text-gray-800">
            {roleId === 1 ? '可用能量详情' : '套餐详情'}
          </div>
          <div className="text-sm text-gray-500">暂无可用套餐</div>
        </div>
      );
    }

    const formatRemainingTime = (pkg: any) => {
      const { remainingDays, remainingHours, remainingMinutes, expireTime } = pkg;

      // 如果没有过期时间，表示永久有效
      if (!expireTime) {
        return '永久有效';
      }

      const parts = [];
      if (remainingDays > 0) {
        parts.push(`${remainingDays}天`);
      }
      if (remainingHours > 0 || remainingDays > 0) {
        parts.push(`${remainingHours}小时`);
      }
      parts.push(`${remainingMinutes}分钟`);

      return parts.join('');
    };

    // 根据用户角色过滤显示的套餐
    const filteredPackages = roleId === 1
      ? packageDetails.filter(pkg =>
        pkg.isPermission || // 教师分配的能量
        pkg.isNonSpecial || // 非特殊套餐
        pkg.assignType === 3 // 兼容旧版本数据格式
      ) // 学生只显示教师分配的能量和非特殊套餐
      : packageDetails;

    return (
      <div className="max-w-xs">
        <div className="text-sm font-medium mb-2 text-gray-800">
          {roleId === 1 ? '可用能量详情' : '套餐详情'}
        </div>
        {filteredPackages.map((pkg, index) => (
          <div
            key={pkg.id}
            className={`${index !== filteredPackages.length - 1 ? 'mb-3 pb-3 border-b border-gray-200' : ''
              }`}
          >
            <div className="text-sm text-gray-700">
              {pkg.packageName}
              <span className="ml-2 text-xs text-gray-500">
                {pkg.expireTime ? `(剩余 ${formatRemainingTime(pkg)})` : '(永久有效)'}
              </span>
            </div>
            <div className="mt-1 flex items-center justify-between text-xs">
              <span className="text-gray-500">
                {pkg.isPermission ? '可用能量:' : '剩余能量:'}
              </span>
              <span className="text-blue-600 font-medium">
                {pkg.remainingPoints}/{pkg.totalPoints}
              </span>
            </div>
            {pkg.expireTime && (
              <div className="mt-1 text-xs text-gray-500">
                有效期: {dayjs(pkg.startTime).format('YYYY/MM/DD HH:mm')} -
                {dayjs(pkg.expireTime).format('YYYY/MM/DD HH:mm')}
              </div>
            )}
            {pkg.isPermission && pkg.teacherName && (
              <div className="mt-1 text-xs text-gray-500">
                分配教师: {pkg.teacherName}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // 修改检查未读消息的函数
  const checkUnreadMessages = async () => {
    if (!userInfo || !userInfo.isLoggedIn || !roleId) return;

    try {
      // 检查未读任务
      if (roleId === 1) {
        const response = await taskApi.getTaskList({
          studentId: userId,
          roleId: roleId,
          page: 1,
          size: 100
        });
        // 单独检查未完成任务的数量
        let incompleteTaskCount = 0;

        if (response.data.code === 200) {
          const taskList = response.data.data.list || [];
          
          // 检查是否有未读任务
          const hasUnreadTask = taskList.some((task: any) => {
            // 检查任务是否过期
            const now = new Date();
            const endDate = new Date(task.endDate);
            const isExpired = now > endDate;

            // 判断条件1：未过期且未读的任务
            return !isExpired && task.assignments?.some((assignment: any) => 
              assignment.isRead === 0
            );
          });
          
          
          taskList.forEach((task: any) => {
            // 检查任务是否过期
            const now = new Date();
            const endDate = new Date(task.endDate);
            const isExpired = now > endDate;
            
            // 检查是否有未开始(0)或过期未完成(4)的任务
            const isIncomplete = !isExpired && task.assignments?.some((assignment: any) => 
              assignment.taskStatus === 0 || assignment.taskStatus === 4
            );
            
            if (isIncomplete) {
              incompleteTaskCount++;
            }
          });

          
          
          // 只在有未完成任务时显示一次提示，并显示具体数量
          if(incompleteTaskCount > 0){
            // 防止重复提示，先检查是否已经显示过
            const hasShownIncompleteNotification = localStorage.getItem('hasShownIncompleteNotification');
            console.log('是否已经显示过了', hasShownIncompleteNotification);
            if (!hasShownIncompleteNotification) {
              GetNotification().info(`您有${incompleteTaskCount}个任务未完成，请及时前往班级空间完成！`);
              // 记录已经显示过提示，避免重复显示
              localStorage.setItem('hasShownIncompleteNotification', 'true');
              // 5分钟后重置，允许再次提示
              setTimeout(() => {
                localStorage.removeItem('hasShownIncompleteNotification');
              }, 300000); // 5分钟
            }
          }
          
          console.log('任务数量', taskList);
          console.log('未读任务', hasUnreadTask);
          console.log('未完成任务数量', incompleteTaskCount);
          setHasUnreadMessage(hasUnreadTask);
          setHasUnreadNotification(hasUnreadTask);
          // 如果有未读任务，自动弹出消息中心
          if (hasUnreadTask) {
            setIsMessageCenterVisible(true);
          }
        }
      }

      // 检查未读公告
      const announcementResponse = await announcementApi.getListUsePage({
        status: 1,
        target: getTargetByRole(roleId),
      });

      if (announcementResponse.data.code === 200) {
        const announcements = announcementResponse.data.data.list || [];
        // 检查是否有未读公告
        const hasUnreadAnnouncement = announcements.some(
          (announcement: any) => announcement.isRead === 0
        );

        // 如果有未读公告或未读任务，显示红点
        setHasUnreadMessage(prevState => prevState || hasUnreadAnnouncement);
        setHasUnreadNotification(prevState => prevState || hasUnreadAnnouncement);
      }
    } catch (error) {
      console.error('检查未读消息失败:', error);
    }
  };

  // 修改消息中心点击处理函数
  const handleMessageCenterClick = () => {
    console.log('点击消息中心按钮');
    console.log('当前isMessageCenterVisible状态:', isMessageCenterVisible);
    console.log('HomeContext:', { isMessageCenterVisible, setIsMessageCenterVisible });

    setIsMessageCenterVisible(true);

    // 添加延迟检查状态是否正确更新
    setTimeout(() => {
      console.log('点击后isMessageCenterVisible状态:', isMessageCenterVisible);
    }, 100);

    // 点击消息中心时清除红点
    setHasUnreadMessage(false);
    setHasUnreadNotification(false);
  };

  // 在组件挂载和登录状态变化时检查未读消息
  useEffect(() => {
    if (userInfo && userInfo.isLoggedIn) {
      checkUnreadMessages();
    }
  }, [userInfo]);

  // 添加定期检查未读消息的逻辑
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (userInfo && userInfo.isLoggedIn) {
      interval = setInterval(checkUnreadMessages, 60000); // 每分钟检查一次
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [userInfo]);

  // 添加 getTargetByRole 函数
  const getTargetByRole = (roleId: number) => {
    switch (roleId) {
      case 1: return 2; // 学生
      case 2: return 1; // 老师
      case 3: // 校长
      case 4: // 管理员
        return 0; // 全部公告
      default: return 0;
    }
  };

  // 监听Redux状态变化，及时更新用户信息
  useEffect(() => {
    console.log('检测到Redux状态变化：isLoggedIn =', isLoggedIn, 'userId =', userId, 'roleId =', roleId);

    if (isLoggedIn && userId) {
      // 如果已登录且有userId，但没有userInfo，则获取用户信息
      if (!userInfo || userInfo.id !== userId) {
        console.log('Redux状态已登录但用户信息不匹配，重新获取');
        fetchUserInfo();
        fetchUserPoints();
      }
    } else if (!isLoggedIn) {
      // 如果未登录，清除用户信息
      setLocalUserInfo(null);
    }
  }, [isLoggedIn, userId, roleId]);

  // 手机号绑定成功回调
  const handlePhoneBindSuccess = async () => {
    await fetchUserInfo();
    message.success('手机号绑定成功');
  };

  // 密码设置成功回调
  const handlePasswordSetSuccess = () => {
    setShowInitialPasswordModal(false);
  };

  return (
    <>
      <nav className="fixed top-0 left-0 right-0 bg-[#4766C2] text-white shadow-lg z-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center space-x-20">
              <Link
                href="/"
                className="flex items-center transition-transform duration-300 hover:scale-105"
              >
                <Image
                  src="/images/logic_leap.png"
                  alt="洛基飞跃 Logo"
                  width={32}
                  height={32}
                  className="mr-2.5"
                />
                <span className="text-lg font-semibold">洛基飞跃</span>
              </Link>
              <div className="flex items-center space-x-8">
                {navItems.map((item) => (
                  item.action ? (
                    <button
                      key={item.label}
                      onClick={item.action}
                      className={`relative py-1 transition-colors duration-200 hover:text-white/90
                        ${isActive(item) ? 'text-white' : 'text-white/75'}
                        group`}
                    >
                      {item.label}
                      <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300
                        ${isActive(item) ? 'scale-x-100' : 'scale-x-0'} 
                        group-hover:scale-x-100`}
                      />
                    </button>
                  ) : (
                    <Link
                      key={item.label}
                      href={item.href || '/'}
                      target={item.target}
                      className={`relative py-1 transition-colors duration-200 hover:text-white/90
                        ${isActive(item) ? 'text-white' : 'text-white/75'}
                        group`}
                    >
                      {item.label}
                      <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-white transform origin-left transition-transform duration-300
                        ${isActive(item) ? 'scale-x-100' : 'scale-x-0'} 
                        group-hover:scale-x-100`}
                      />
                    </Link>
                  )
                ))}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {userInfo && userInfo.isLoggedIn ? (
                <div className="flex items-center space-x-3">
                  <div className="relative group">
                    <div className="relative transition-all duration-500 ease-in-out transform
                      group-hover:translate-y-[110%] group-hover:-translate-x-[60%]">
                      <div className="absolute top-2 left-1/2 -translate-x-1/2
                        w-[280px] bg-white rounded-xl shadow-lg opacity-0 invisible 
                        group-hover:opacity-100 group-hover:visible 
                        transition-all duration-500 ease-in-out z-40
                        border border-gray-200/60">
                        <div className="h-14" />

                        <div className="pt-2 pb-4 text-center border-b border-gray-100 bg-white">
                          <div className="text-[#18191C] font-medium text-lg leading-6">
                            {localUserInfo?.nickName || '用户'}
                          </div>
                          <div className="text-[#9499A0] text-sm mt-1">
                            {localUserInfo?.phone || ''}
                          </div>
                        </div>

                        {/* <div className="grid grid-cols-3 py-4 border-b border-gray-100/80">
                          <div className="text-center">
                            <div className="text-[#18191C] font-medium">0</div>
                            <div className="text-[#9499A0] text-xs mt-1">动态</div>
                          </div>
                          <div className="text-center border-l border-r border-gray-100/80">
                            <div className="text-[#18191C] font-medium">0</div>
                            <div className="text-[#9499A0] text-xs mt-1">关注</div>
                          </div>
                          <div className="text-center">
                            <div className="text-[#18191C] font-medium">0</div>
                            <div className="text-[#9499A0] text-xs mt-1">粉丝</div>
                          </div>
                        </div> */}

                        <div className="py-2 border-b border-gray-100/80">
                          <button
                            onClick={() => setShowProfileModal(true)}
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] 
                              hover:bg-[#F6F7F8] transition-all duration-200 ease-out"
                          >
                            <User className="w-4 h-4 mr-3 text-[#9499A0]" />
                            个人信息
                          </button>

                          {/* 消息中心 */}
                          <div
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] 
                              hover:bg-[#F6F7F8] transition-all duration-200 ease-out cursor-pointer relative"
                            onClick={handleMessageCenterClick}
                          >
                            <MessageCircle className="w-4 h-4 mr-3 text-[#9499A0]" />
                            <span>消息中心</span>
                            {hasUnreadMessage && (
                              <span className="absolute top-2 right-6 w-2 h-2 bg-red-500 rounded-full" />
                            )}
                          </div>

                          {/* 根据角色显示不同的空间入口 */}
                          {roleId === 2 ? (
                            <Link
                              href="/teacher-space"
                              className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                                transition-all duration-200 ease-out"
                            >
                              <TeamOutlined className="w-4 h-4 mr-3 text-[#9499A0]" />
                              教师空间
                            </Link>
                          ) : roleId === 1 ? (
                            <Link
                              href="/class-space"
                              className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                                transition-all duration-200 ease-out"
                            >
                              <TeamOutlined className="w-4 h-4 mr-3 text-[#9499A0]" />
                              班级空间
                            </Link>
                          ) : roleId === 4 ? (
                            <>
                              <Link
                                href="/admin-space"
                                className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                                  transition-all duration-200 ease-out"
                              >
                                <Settings className="w-4 h-4 mr-3 text-[#9499A0]" />
                                管理员空间
                              </Link>
                              <Link
                                href="/operation-space"
                                className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                                  transition-all duration-200 ease-out"
                              >
                                <Settings className="w-4 h-4 mr-3 text-[#9499A0]" />
                                运营空间
                              </Link>
                            </>
                          ) : roleId === 5 ? (
                            <Link
                              href="/operation-space"
                              className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                                transition-all duration-200 ease-out"
                            >
                              <Settings className="w-4 h-4 mr-3 text-[#9499A0]" />
                              运营空间
                            </Link>
                          ) : null}

                          <Link
                            href="/my-works"
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                              transition-all duration-200 ease-out"
                          >
                            <Palette className="w-4 h-4 mr-3 text-[#9499A0]" />
                            我的空间
                          </Link>
                          <div
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                              transition-all duration-200 ease-out cursor-pointer"
                            onClick={() => {
                              setShowRoleSwitcherModal(true);
                            }}
                          >
                            <UserSwitchOutlined className="w-4 h-4 mr-3 text-[#9499A0]" />
                            <span className="w-full">切换账号</span>
                          </div>

                          {/* 将RoleSwitcher放在导航栏外部，避免嵌套问题 */}
                          <div className="hidden">
                            <RoleSwitcher />
                          </div>

                          {/* <button 
                            onClick={() => setRoleAssignModalVisible(true)}
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                              transition-all duration-200 ease-out"
                          >
                            <UserPlus className="w-4 h-4 mr-3 text-[#9499A0]" />
                            分配角色
                          </button> */}

                          {/* <button 
                            onClick={() => setPermissionModalVisible(true)}
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] hover:bg-[#F6F7F8]
                              transition-all duration-200 ease-out"
                          >
                            <Settings className="w-4 h-4 mr-3 text-[#9499A0]" />
                            积木权限
                          </button> */}

                          {/* 权限管理弹窗 */}
                          {permissionModalVisible && (
                            <PermissionModal
                              userId={userId}
                              visible={permissionModalVisible}
                              onClose={() => setPermissionModalVisible(false)}
                            />
                          )}

                          {/* 权限模板弹窗 */}
                          {permissionTemplateModalVisible && localUserInfo && (
                            <PermissionTemplateModal
                              userId={userId}
                              roleId={roleId || 1}
                              visible={permissionTemplateModalVisible}
                              onClose={() => setPermissionTemplateModalVisible(false)}
                            />
                          )}
                        </div>

                        {/* <div className="py-2 border-b border-gray-100/80">
                          <Link 
                            href="/settings"
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#18191C] 
                              hover:bg-[#F6F7F8] transition-all duration-200 ease-out"
                          >
                            <Settings className="w-4 h-4 mr-3 text-[#9499A0]" />
                            设置
                          </Link>
                        </div> */}

                        <div className="py-2">
                          <button
                            onClick={handleLogoutClick}
                            className="flex items-center w-full px-8 py-3 text-[14px] text-[#FB7299] hover:bg-[#F6F7F8]
                              transition-colors duration-200"
                          >
                            <LogOut className="w-4 h-4 mr-3" />
                            退出登录
                          </button>
                        </div>
                      </div>

                      <div className="relative w-10 h-10 cursor-pointer rounded-full overflow-visible
                        transition-all duration-500 ease-in-out transform origin-center
                        group-hover:scale-[2]
                        hover:ring-2 hover:ring-blue-400 bg-white z-50"
                      >
                        <div className="w-full h-full rounded-full overflow-hidden">
                          {localUserInfo?.avatarUrl ? (
                            <img
                              src={localUserInfo.avatarUrl}
                              alt="用户头像"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <UserCircle className="w-full h-full text-white/90" />
                          )}
                        </div>

                        {hasUnreadNotification && (
                          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full 
                            border-2 border-white z-50"
                          />
                        )}
                      </div>
                    </div>
                  </div>
                  <Tooltip
                    title={renderPackageTooltip()}
                    placement="bottom"
                    color="#fff"
                    overlayInnerStyle={{
                      padding: '16px',
                      borderRadius: '8px',
                      boxShadow: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
                    }}
                  >
                    <div
                      className="flex items-center bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-1.5 rounded-full shadow-lg transition-all duration-300 hover:bg-white/15 cursor-pointer group"
                      onMouseEnter={() => {
                        if (userInfo && userInfo.isLoggedIn && userId) {
                          fetchPackageDetails();
                          fetchUserPoints();
                        }
                      }}
                      onClick={() => {
                        router.push('/recharge');
                      }}
                      title="点击充值"
                    >
                      <Zap className="energy-icon w-4 h-4 text-yellow-400 mr-2 animate-pulse group-hover:scale-110 transition-transform" strokeWidth={2.5} />
                      <span className="text-white text-sm font-medium">
                        {roleId === 1 ? (
                          <>能量: {availablePoints}</>
                        ) : (
                          <>能量: {points}</>
                        )}
                        {/* <>能量: {points}</> */}
                      </span>
                      <span className="ml-2 text-white/60 text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                        充值
                      </span>
                    </div>
                  </Tooltip>
                </div>
              ) : (
                <button
                  onClick={() => {
                    localStorage.removeItem("user")
                    localStorage.removeItem("userId")
                    localStorage.removeItem("token")
                    const redirectUrl = pathname || '/home';
                    router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                  }}
                  className="bg-white/10 hover:bg-white/20 text-white px-4 py-1.5 rounded-full text-sm font-medium 
                    transition-all duration-300 flex items-center backdrop-blur-sm border border-white/20 shadow-lg
                    hover:scale-105 active:scale-95"
                >
                  <UserCircle className="mr-1.5" size={16} />
                  登录
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div
        className="fixed top-14 left-0 right-0 h-1.5 bg-[#46B1E1] origin-left transition-transform duration-300 ease-out z-40"
        style={{ transform: `scaleX(${scrollProgress})` }}
      />
      {/* {isLoginModalOpen && (
        <LoginDialog
          isOpen={isLoginModalOpen}
          onClose={() => setIsLoginModalOpen(false)}
          onSuccess={handleLoginSuccess}
        />
      )} */}
      {showProfileModal && (
        <ProfileModal
          isOpen={showProfileModal}
          onClose={() => setShowProfileModal(false)}
          points={points}
          onUpdate={handleProfileUpdate}
        />
      )}
      <LogoutModal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
      />
      {/* 添加角色分配模态框 */}
      {roleAssignModalVisible && (
        <RoleAssignModal
          visible={roleAssignModalVisible}
          onClose={() => setRoleAssignModalVisible(false)}
          userId={userId}
          currentRoleId={roleId}
        />
      )}

      {/* 角色切换模态框 */}
      {showRoleSwitcherModal && (
        <RoleSwitcherModal
          isOpen={showRoleSwitcherModal}
          onClose={() => setShowRoleSwitcherModal(false)}
          currentUser={localUserInfo}
        />
      )}

      {/* 添加手机号绑定模态框 */}
      {(showBindPhoneModal) && (
        <ModifyPhoneModal
          isOpen={showBindPhoneModal}
          onClose={() => {
            setShowBindPhoneModal(false);
            // 手机号绑定关闭后，检查用户角色是否为999，如果是则显示身份认证
            checkLatestUserRoleAndShowModal();
          }}
          currentPhone=""
          onSuccess={handlePhoneBindSuccess}
        />
      )}


      {/* 新用户的初次身份选择弹框 */}
      {
        // 如果为真，则返回具体的dom显示  if（true）的语法糖 + ()  为默认返回
        showRoleSelectionModalForNewUser && (
          <RoleSelectionTransition
            onSuccess={() => setShowRoleSelectionModalForNewUser(false)}
            onClose={async () => {
              // 提示用户如果关闭弹框则自动绑定普通角色
              // 若用户关闭了弹框，默认给其当作普通用户,后续可以在其他地方进行认证
              const res = await userApi.assignRole(userId, 3)
              if (res.data.code === 200) {
                nt.success("已选择普通用户作为默认角色")
              }
              setShowRoleSelectionModalForNewUser(false)
            }}
            userId={userId}
          />
        )
      }

      {/* 初始密码设置弹框 */}
      {showInitialPasswordModal && (
        <InitialPasswordModal
          isOpen={showInitialPasswordModal}
          onClose={() => setShowInitialPasswordModal(false)}
          onSuccess={handlePasswordSetSuccess}
          userId={userId}
        />
      )}
    </>
  )
}

