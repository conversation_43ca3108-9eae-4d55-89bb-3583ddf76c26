/* eslint-disable import/no-nodejs-modules */
// eslint-disable-next-line import/no-commonjs
const ArgumentType = require('@scratch-vm/extension-support/argument-type');
// eslint-disable-next-line import/no-commonjs
const BlockType = require('@scratch-vm/extension-support/block-type');
// eslint-disable-next-line import/no-commonjs
const Cast = require('@scratch-vm/util/cast');
const {getExtensionPermissions} = require('../index.jsx');
// eslint-disable-next-line import/no-commonjs
const TrainDialogComponent = require('../utils/trainUtil/trainDialog/train-dialog.jsx').default;
// eslint-disable-next-line import/no-unresolved
const {getExtensionPermissions: getExtensionPermissionsUtils} = require('../utils/userIdHelper');
// 引入文件上传器
const createFileUploader = require('../utils/fileUploader');
// 引入JSON文件上传器
const createJsonFileUploader = require('../utils/jsonFileUploader');
const React = require('react');
const ReactDOM = require('react-dom');
// 引入通知模块
const notification = require('../utils/notification').default;;
// 引入ImageMode类
const ImageMode = require('./recognitions/imageMode');
// 引入加载动画模块
const { createLoadingAnimation, updateLoadingProgress, removeLoadingAnimation } = require('../utils/loadingAnimation');
// 导入 workApi
const workApi = require('@api/work_api.ts').default; // 注意 .default
const { getImage } = require('../utils/mediaHandler');
// 导入图片资源
// eslint-disable-next-line import/no-commonjs
const IconURI = require('./image_train.svg');
// 导入按钮处理模块
const buttonHandler = require('../utils/button-handler.js');

class LogicLeapImageTrainBlocks {
    constructor (runtime) {
        /**
         * The runtime instantiating this block package.
         * @type {Runtime}
         */
        this.runtime = runtime;

        // 初始化各种状态F
        this.currentModel = null;
        this.lastUploadedImage = null;
        this.modelStatus = '未开始';
        
        // 模型导入相关状态
        this.modelData = null;
        this.modelImported = false;
        this.imageMode = null;
        this.modelCount = 0; // 添加模型计数器
        
        // 最近一次预测结果
        this.lastPrediction = null;

        // 添加模型缓存系统
        this.modelCache = {};
        this.modelDataCache = {}; // 用于存储实际的模型数据

        // 添加图像识别窗口相关状态
        this.imageIdentifyRoot = null;

        window._logicleapImageTrainInstance = this;
        
        // 只初始化缓存索引结构，不读取存储的模型数据
        try {
            const savedCacheIndex = localStorage.getItem('logicleap_model_cache_index');
            if (savedCacheIndex) {
                this.modelCache = JSON.parse(savedCacheIndex);
                
                // 不从本地存储加载模型数据，只维护索引
            }
        } catch (error) {
            console.error('初始化模型缓存索引失败:', error);
            // 如果初始化失败，使用空对象
            this.modelCache = {};
        }

        // 从预加载的权限数据中获取本扩展的权限
        this.visibleBlocks = getExtensionPermissions('logicleapImageTrain');

        // 绑定方法
        this.showTrainDialog = this.showTrainDialog.bind(this);
        this.closeTrainDialog = this.closeTrainDialog.bind(this);
        this.uploadModel = this.uploadModel.bind(this);
        this.importModel = this.importModel.bind(this);
        this.editModel = this.editModel.bind(this);
        this.predictImage = this.predictImage.bind(this);
        this.closeCamera = this.closeCamera.bind(this);
        this.getImageProperty = this.getImageProperty.bind(this);
        this.getModelNumber = this.getModelNumber.bind(this);
        this.loadModelByNumber = this.loadModelByNumber.bind(this);
        this.listCachedModels = this.listCachedModels.bind(this);
        this.showImageIdentifyWindow = this.showImageIdentifyWindow.bind(this);
        this.openTrainerWithLabels = this.openTrainerWithLabels.bind(this);
        // 初始化模型数据
        this.initializeModelData();

        // 初始化按钮处理模块
        buttonHandler.initialize(this);

        // 注册"训练模型"按钮
        buttonHandler.registerButton(
            this.extensionId, 
            '训练图像模型', 
            'showTrainDialog', 
            this,
            { color: '#9392A9' } // 紫色
        );

        // 注册"上传模型"按钮
        buttonHandler.registerButton(
            this.extensionId,
            '上传图像模型',
            'importModel',
            this,
            { color: '#7B68EE' } // 深紫色
        );

        // 注册"编辑模型"按钮
        buttonHandler.registerButton(
            this.extensionId,
            '编辑图像模型',
            'editModel',
            this,
            { color: '#FF6B6B' } // 红色
        );
    }

    /**
     * 初始化模型数据
     * 从后端获取当前作品的所有模型数据
     */
    async initializeModelData() {
        try {
            const workId = await this.getWorkId();
            
            if (!workId || workId === 'null') {
                console.warn('未能获取作品ID，跳过模型数据初始化');
                return;
            }
            const token = localStorage.getItem('token'); // workApi 内部会用 token
            

            
            // ---- 使用 workApi 获取模型数据 ----
            const response = await workApi.getWorkModels(Number(workId));
            // ---------------------------------
            
            
            if (response.code === 200 && response.data) {
                // 只获取图像类型的模型
                const imageModels = response.data.filter(model => model.modelType === 'image');
                // 初始化模型数据
                this.modelData = imageModels;
                // 设置初始modelCount为已有模型的最大编号
                const existingModelNumbers = imageModels.map(m => parseInt(m.modelNumber) || 0);
                this.modelCount = existingModelNumbers.length > 0 ? Math.max(...existingModelNumbers) : 0;
                

                // 如果有模型数据，开始下载和处理每个模型
                if (this.modelCount > 0) {
                    notification.show(`正在加载${this.modelCount}个图像模型数据...`);
                    
                    // 清空现有的缓存
                    this.modelCache = {};
                    this.modelDataCache = {};
                    localStorage.removeItem('logicleap_model_cache_index');
                    
                    // 处理每个模型
                    for (const model of this.modelData) {
                        try {
                            
                            
                            // 从URL下载JSON文件
                            
                            const modelResponse = await fetch(model.modelUrl);
                            if (!modelResponse.ok) {
                                notification.show(`下载模型失败: ${modelResponse.statusText}`);
                                continue; // 跳过这个模型
                            }
                            
                            // 获取JSON数据
                            const modelData = await modelResponse.text();
                            
                            
                            // 验证JSON格式
                            try {
                                const modelJson = JSON.parse(modelData);
                                
                                
                                if (!modelJson.dataset || !modelJson.labels || modelJson.modelType !== 'knn-classifier') {
                                    notification.show('无效的模型文件格式');
                                }
                                
                                // 检查并转换数据集格式
                                if (modelJson.dataset) {
                                    const datasetKeys = Object.keys(modelJson.dataset);
                                    if (datasetKeys.length > 0) {
                                        const firstKey = datasetKeys[0];
                                        const firstEntry = modelJson.dataset[firstKey];
                                        
                                        // 如果是旧格式（数组），转换为新格式（带values属性）
                                        if (Array.isArray(firstEntry)) {
                                            // //console.log(`转换模型 ${model.modelNumber} 的数据集格式`);
                                            const convertedDataset = {};
                                            datasetKeys.forEach(key => {
                                                convertedDataset[key] = {
                                                    values: modelJson.dataset[key],
                                                    shape: [modelJson.dataset[key].length / 1024, 1024]
                                                };
                                            });
                                            modelJson.dataset = convertedDataset;
                                            // 更新modelData为新的JSON字符串
                                            modelData = JSON.stringify(modelJson);
                                        }
                                    }
                                }
                                
                                // 更新模型缓存
                                this.modelCache[model.modelNumber] = {
                                    modelName: model.description || `模型${model.modelNumber}`,
                                    importTime: new Date().toISOString()
                                };
                                
                                // 保存模型数据到内存缓存
                                this.modelDataCache[model.modelNumber] = modelData;
                                
                                // 保存到localStorage
                                try {
                                    localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                    localStorage.setItem(`logicleap_model_data_${model.modelNumber}`, modelData);
                                    
                                } catch (storageError) {
                                    console.error('保存模型到localStorage失败:', storageError);
                                    // 尝试清理缓存后重新保存
                                    this.cleanupModelCache();
                                    localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                    localStorage.setItem(`logicleap_model_data_${model.modelNumber}`, modelData);
                                }
                                
                            } catch (jsonError) {
                                console.error(`模型 ${model.modelNumber} 的JSON格式无效:`, jsonError);
                                continue;
                            }
                            
                        } catch (modelError) {
                            console.error(`处理模型 ${model.modelNumber} 失败:`, modelError);
                            continue;
                        }
                    }
                    
                    // 触发模型缓存更新事件
                    this.dispatchModelCacheUpdatedEvent();
                    
                    // 如果有模型，默认加载第一个
                    if (Object.keys(this.modelCache).length > 0) {
                        const firstModelNumber = Object.keys(this.modelCache)[0];
                        const success = await this.switchToModel(parseInt(firstModelNumber));
                        if (success) {
                            
                        } else {
                            console.error('加载第一个模型失败');
                        }
                    }
                    notification.show(`成功加载 ${Object.keys(this.modelCache).length} 个模型`, 'success');
                }
            } else {
                console.warn('获取模型数据失败:', response.message);
            }
        } catch (error) {
            console.error('初始化模型数据时发生错误:', error);
            notification.show('加载模型数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取当前作品ID
     */
    async getWorkId() {
        try {
            // 从localStorage获取workId
            const workId = localStorage.getItem('modelWorkId');
            if (workId) {
                return workId;
            }else{
                localStorage.removeItem('modelWorkId');
            }

            // 如果localStorage中没有,尝试从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('workId');
        } catch (error) {
            console.error('获取作品ID失败:', error);
            return null;
        }
    }

    getInfo () {
        // 定义所有积木块
        const allBlocks = [
            {
                opcode: 'showTrainDialog',
                blockId: 'train_show_dialog',
                blockType: BlockType.BUTTON,
                text: '训练图像模型',
                
            },
            
            {
                opcode: 'importModel',
                blockId: 'train_import_model',
                blockType: BlockType.BUTTON,
                text: '上传图像模型'
            },
            {
                opcode: 'editModel',
                blockId: 'train_edit_model',
                blockType: BlockType.BUTTON,
                text: '编辑图像模型'
            },
            {
                opcode: 'showImageIdentifyWindow',
                blockId: 'train_show_image_identify_window',
                blockType: BlockType.COMMAND,
                text: '打开图像识别窗口',
            },
            {
                opcode: 'openTrainerWithLabels',
                blockId: 'train_open_trainer_with_labels_image',
                blockType: BlockType.COMMAND,
                text: '打开训练器, 标签1 [LABEL1], 标签2 [LABEL2]',
                arguments: {
                    LABEL1: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别1'
                    },
                    LABEL2: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别2'
                    }
                }
            },
            {
                opcode: 'loadModelByNumber',
                blockId: 'train_load_model_by_number',
                blockType: BlockType.COMMAND,
                text: '加载编号为 [MODEL_NUMBER] 的模型',
                arguments: {
                    MODEL_NUMBER: {
                        type: ArgumentType.STRING,
                        defaultValue: '1'
                    }
                }
            },
            {
                opcode: 'addTrainingData',
                blockId: 'train_add_data',
                blockType: BlockType.COMMAND,
                text: '将当前图片添加为训练数据 标签为 [LABEL]',
                arguments: {
                    LABEL: {
                        type: ArgumentType.STRING,
                        defaultValue: '标签1'
                    }
                }
            },
            {
                opcode: 'predictImage',
                blockId: 'train_predict_image',
                blockType: BlockType.REPORTER,
                text: '使用摄像头预测图像的 [PROPERTY]',
                arguments: {
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'imageProperties',
                        defaultValue: 'label'
                    }
                }
            },
            {
                opcode: 'getImageProperty',
                blockId: 'train_get_image_property',
                blockType: BlockType.REPORTER,
                text: '预测 [IMAGE] 的 [PROPERTY]',
                arguments: {
                    IMAGE: {
                        type: ArgumentType.STRING,
                        defaultValue: '图像数据'
                    },
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'imageProperties',
                        defaultValue: 'label'
                    }
                }
            },
            // {
            //     opcode: 'closeCamera',
            //     blockId: 'train_close_camera',
            //     blockType: BlockType.COMMAND,
            //     text: '关闭摄像头'
            // },
            {
                opcode: 'getModelNumber',
                blockId: 'train_get_model_number',
                blockType: BlockType.REPORTER,
                text: '当前模型编号'
            },
            {
                opcode: 'listCachedModels',
                blockId: 'train_list_cached_models',
                blockType: BlockType.REPORTER,
                text: '已缓存的所有模型编号'
            }
        ];

        // 根据权限过滤积木块
        const blocks = allBlocks.filter(block => this.visibleBlocks[block.blockId] === true);
        
        // 确保预测图像积木块总是可见
        const hasPredictBlock = blocks.some(block => block.opcode === 'predictImage');
        if (!hasPredictBlock) {
            blocks.push({
                opcode: 'predictImage',
                blockType: BlockType.REPORTER,
                text: '使用摄像头预测图像的 [PROPERTY]',
                arguments: {
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'imageProperties',
                        defaultValue: 'both'
                    }
                }
            });
        }
        
        // 确保图像属性积木块总是可见
        const hasPropertyBlock = blocks.some(block => block.opcode === 'getImageProperty');
        if (!hasPropertyBlock) {
            blocks.push({
                opcode: 'getImageProperty',
                blockType: BlockType.REPORTER,
                text: '图像数据 [IMAGE] 预测的 [PROPERTY]',
                arguments: {
                    IMAGE: {
                        type: ArgumentType.STRING,
                        defaultValue: ''
                    },
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'imageProperties',
                        defaultValue: 'label'
                    }
                }
            });
        }
        
        // 确保模型编号积木块总是可见
        const hasModelNumberBlock = blocks.some(block => block.opcode === 'getModelNumber');
        if (!hasModelNumberBlock) {
            blocks.push({
                opcode: 'getModelNumber',
                blockType: BlockType.REPORTER,
                text: '当前模型编号'
            });
        }
        
        return {
            id: 'logicleapImageTrain',
            name: '图像训练',
            color1: '#6A8D6D',
            blockIconURI: IconURI,
            menuIconURI: IconURI,
            blocks: blocks,
            menus: {
                imageProperties: {
                    acceptReporters: false,
                    items: [
                        {
                            text: '标签',
                            value: 'label'
                        },
                        {
                            text: '置信度',
                            value: 'confidence'
                        },
                        {
                            text: '标签 + 置信度',
                            value: 'both'
                        }
                    ]
                },
                modelLabels: this._getModelLabelsMenu()
            }
        };
    }

    // showTrainDialog () {
    //     const stageWrapper = document.querySelector('[class*="stage-wrapper_stage-wrapper"]');
    //     if (!stageWrapper) {
    //         console.error('Stage wrapper not found');
    //         return;
    //     }

    //     // 保存实例引用到全局变量，便于ImageMode访问
    //     window._logicleapImageTrainInstance = this;

    //     this.trainDialog.show({
    //         container: stageWrapper,
    //         title: '图像训练',
    //         onClose: () => {
    //             // //console.log('训练窗口已关闭');
    //         }
    //     });
    //     return '已打开训练窗口';
    // }

    showTrainDialog () {
        // 如果对话框已经打开，先关闭它
        if (this.dialogRoot) {
            this.closeTrainDialog();
        }

        // 创建对话框容器
        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
        
        // 渲染React组件
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'image',
                onClose:  this.closeTrainDialog
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
                //console.log('训练窗口已打开');
            }
        );
        notification.show('已打开训练窗口', 'success');
        return ;
    }
    
    closeTrainDialog() {
        if (this.dialogRoot) {
            // 卸载React组件
            ReactDOM.unmountComponentAtNode(this.dialogRoot);
            // 移除容器
            if (this.dialogRoot.parentNode) {
                this.dialogRoot.parentNode.removeChild(this.dialogRoot);
            }
            // 重置引用
            this.dialogRoot = null;
            this.dialogContainer = null;
            
            // 恢复页面滚动
            document.body.style.overflow = '';
            
            //console.log('训练窗口已关闭');
        }
    }

    openTrainerWithLabels (args) {
        const { LABEL1, LABEL2 } = args;
        // 过滤掉空的标签，并确保至少有两个标签
        const classLabels = [LABEL1, LABEL2].filter(Boolean);
        if (classLabels.length === 0) {
            classLabels.push('类别1', '类别2');
        } else if (classLabels.length === 1) {
            classLabels.push('类别2');
        }
    
        // 如果对话框已经打开，先关闭它
        if (this.dialogRoot) {
            this.closeTrainDialog();
        }
    
        // 创建对话框容器
        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
        
        // 渲染React组件
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'image',
                onClose: this.closeTrainDialog,
                initialClassLabels: classLabels
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
            }
        );
        notification.show('已打开训练窗口', 'success');
        return ;
    }

    showImageIdentifyWindow () {
        if(this.imageIdentifyRoot!=null){
            notification.show('请关闭之前打开的图像识别窗口', 'error');
            return;
        }

        if(!this.modelImported) {
            notification.show('请先导入模型', 'error');
            return;
        }

        if(!this.currentModel) {
            notification.show('请先加载模型', 'error');
            return;
        }

        if (!this.modelDataCache[this.currentModel.modelNumber]) {
            notification.show('请先加载模型', 'error');
            return;
        }

        // 如果对话框已经打开，先关闭它
        // 创建一个悬浮窗口来显示声音识别窗口
        const tempDiv = document.createElement('div');
        tempDiv.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50px;
            z-index: 9999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            overflow: hidden;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            width: 280px;
            max-width: 280px;
            min-width: 280px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            border-radius: 8px;
        `;
        
        const modalHeader = document.createElement('div');
        modalHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background:rgb(255, 255, 255);
            color: #333;
            cursor: move;
            border-bottom: 1px solid #e0e0e0;
            user-select: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        `;
        
        const modalTitle = document.createElement('h3');
        modalTitle.textContent = '识别窗口';
        modalTitle.style.cssText = `
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        `;
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'X';
        closeButton.style.cssText = `
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 4px 8px;
        `;
        closeButton.onclick = () => {
            document.body.removeChild(tempDiv);
            if (this.imageIdentifyRoot) {
                ReactDOM.unmountComponentAtNode(this.imageIdentifyRoot);
                this.imageIdentifyRoot = null;
            }
        };
        
        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeButton);
        modalContent.appendChild(modalHeader);
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            padding: 16px;
            overflow-y: auto;
        `;
        modalContent.appendChild(contentContainer);
        
        // 创建React组件挂载点
        this.imageIdentifyRoot = document.createElement('div');
        contentContainer.appendChild(this.imageIdentifyRoot);
        tempDiv.appendChild(modalContent);
        document.body.appendChild(tempDiv);
        
        // 添加拖动功能
        let isDragging = false;
        let offsetX, offsetY;
        
        modalHeader.onmousedown = (e) => {
            isDragging = true;
            offsetX = e.clientX - tempDiv.getBoundingClientRect().left;
            offsetY = e.clientY - tempDiv.getBoundingClientRect().top;
            
            // 添加鼠标样式反馈
            modalHeader.style.cursor = 'grabbing';
        };
        
        document.onmousemove = (e) => {
            if (!isDragging) return;
            
            const left = e.clientX - offsetX;
            const top = e.clientY - offsetY;
            
            // 确保窗口不会拖出视口
            const maxX = window.innerWidth - tempDiv.offsetWidth;
            const maxY = window.innerHeight - tempDiv.offsetHeight;
            
            tempDiv.style.left = `${Math.max(0, Math.min(left, maxX))}px`;
            tempDiv.style.top = `${Math.max(0, Math.min(top, maxY))}px`;
        };
        
        document.onmouseup = () => {
            isDragging = false;
            modalHeader.style.cursor = 'move';
        };
        
        try {
            // 导入并渲染SoundIdentifyWindow组件
            const ImageIdentifyWindows = require('./ImageIdentifyWindows.jsx').default;
            
            if (ImageIdentifyWindows) {
                ReactDOM.render(
                    React.createElement(ImageIdentifyWindows, {
                        modelData :this.modelDataCache[this.currentModel.modelNumber]
                        // loadedModel: this.loadedModel
                    }),
                    this.imageIdentifyRoot
                );
            } else {
                document.body.removeChild(tempDiv);
            }
        } catch (error) {
            console.error('渲染图像识别窗口时出错:', error);
            document.body.removeChild(tempDiv);
        }
    }
    
    // eslint-disable-next-line require-await
    async addTrainingData (args) {
        const label = Cast.toString(args.LABEL);
        // TODO: 实现添加训练数据的功能
        notification.show(`添加训练数据: ${label}`, 'success');
        return ;
    }

    /**
     * 上传已训练的模型
     * @returns {Promise<string>} 操作状态
     */
    async uploadModel() {
        try {
            // 使用专门的JSON文件上传器选择模型文件
            const modelData = await createJsonFileUploader({
                title: '选择要上传的模型文件',
                themeColor: '#4766C2',
                maxSize: 10 * 1024 * 1024 // 限制为10MB
            });
            
            if (!modelData) {
                return ''; // 返回空字符串避免显示取消消息
            }
            
            // 检查是否为有效的JSON
            const modelJson = JSON.parse(modelData);
            // 验证是否为模型文件 - 支持两种格式
            if (!modelJson.dataset || !modelJson.labels || modelJson.modelType !== 'knn-classifier') {
                console.error('模型验证失败：缺少必要字段或格式不正确');
                notification.show('无效的模型文件，请上传正确的模型文件', 'error');
                return;
            }
            
            // 检查数据集格式并尝试转换
            if (modelJson.dataset) {
                const datasetKeys = Object.keys(modelJson.dataset);
                if (datasetKeys.length > 0) {
                    const firstKey = datasetKeys[0];
                    const firstEntry = modelJson.dataset[firstKey];
                    
                    // 检测数据是对象格式（包含values）
                    if (firstEntry && typeof firstEntry === 'object' && firstEntry.values) {
                        // 已经是正确格式，无需转换
                    } 
                    // 检测数据是数组格式
                    else if (Array.isArray(firstEntry)) {
                        // 将旧格式转换为新格式
                        const convertedDataset = {};
                        datasetKeys.forEach(key => {
                            convertedDataset[key] = {
                                values: modelJson.dataset[key],
                                shape: [modelJson.dataset[key].length / 1024, 1024] // 假设形状
                            };
                        });
                        
                        // 更新模型数据
                        modelJson.dataset = convertedDataset;
                        // 更新原始字符串
                        modelData = JSON.stringify(modelJson);
                    }
                }
            }
            
            // 保存模型数据到实例变量
            this.modelData = modelData;
            
            // 创建加载动画
            const loadingAnimation = createLoadingAnimation(`正在保存上传的模型...`);
            
            try {
                // 更新模型编号 - 自增
                this.modelCount++;
                const modelNumberToUse = String(this.modelCount);
                
                // 获取模型名称 - 尝试从模型数据中提取或使用默认名称
                let modelName = '上传的模型';
                try {
                    // 尝试从标签中获取名称信息
                    if (modelJson.labels && modelJson.labels.length > 0) {
                        modelName = `包含 ${modelJson.labels.join(', ')} 的模型`;
                    }
                } catch (e) {
                    console.warn('无法提取模型名称，使用默认名称');
                }
                
                // 将模型数据保存到缓存
                // 更新内存中的模型索引信息
                this.modelCache[modelNumberToUse] = {
                    modelName: modelName,
                    importTime: new Date().toISOString()
                };
                
                // 只在内存中保存实际模型数据，用于当前会话
                this.modelDataCache[modelNumberToUse] = modelData;
                
                // 将索引信息和模型数据保存到浏览器的localStorage中
                try {
                    // 保存模型索引
                    localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                    // 保存模型数据到localStorage
                    localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelData);
                    
                    // 触发模型缓存更新事件，通知其他组件
                    this.dispatchModelCacheUpdatedEvent();
                    
                    // 移除加载动画
                    removeLoadingAnimation();
                    
                    notification.show(`模型已上传`, 'success');
                    return ;
                } catch (error) {
                    console.error('保存模型到浏览器缓存失败:', error);
                    // 如果保存失败，尝试清理旧模型
                    this.cleanupModelCache();
                    
                    // 再次尝试保存
                    try {
                        localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                        localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelData);
                        
                        // 触发模型缓存更新事件
                        this.dispatchModelCacheUpdatedEvent();
                        
                        // 移除加载动画
                        removeLoadingAnimation();
                        
                        return ;
                    } catch (err) {
                        console.error('清理后仍然无法保存模型:', err);
                        removeLoadingAnimation();
                        notification.show('保存模型失败: ' + err.message, 'error');
                        return;
                    }
                }
            } catch (error) {
                console.error('保存上传的模型失败:', error);
                removeLoadingAnimation();
                notification.show('保存模型失败: ' + error.message, 'error');
                return;
            }
        } catch (error) {
            console.error('上传模型错误:', error);
            notification.show(error.message || '上传模型失败', 'error');
            return;
        }
    }
    
    /**
     * 导入已训练的模型
     * @returns {string} 操作状态
     */
    async importModel() {
        try {
            // 创建模型选择弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 24px;
                border-radius: 8px;
                width: 600px;
                max-width: 90%;
                max-height: 80vh;
                display: flex;
                flex-direction: column;
            `;

            const modalTitle = document.createElement('h3');
            modalTitle.textContent = '导入已上传的模型';
            modalTitle.style.cssText = `
                margin: 0 0 16px 0;
                font-size: 20px;
            `;

            // 创建标签页容器
            const tabsContainer = document.createElement('div');
            tabsContainer.style.cssText = `
                display: flex;
                border-bottom: 1px solid #e0e0e0;
                margin-bottom: 16px;
            `;

            // 创建本地模型标签
            const localTab = document.createElement('div');
            localTab.textContent = '本地模型';
            localTab.style.cssText = `
                padding: 8px 16px;
                        cursor: pointer;
                font-weight: bold;
                color: #4766C2;
                border-bottom: 2px solid #4766C2;
            `;
            
            // 创建云端模型标签
            const cloudTab = document.createElement('div');
            cloudTab.textContent = '云端模型';
            cloudTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: #666;
                    `;
            
            tabsContainer.appendChild(localTab);
            tabsContainer.appendChild(cloudTab);

            const modelList = document.createElement('div');
            modelList.style.cssText = `
                flex: 1;
                overflow-y: auto;
                margin-bottom: 16px;
                min-height: 200px;
            `;

            // 添加上传按钮容器
            const uploadContainer = document.createElement('div');
            uploadContainer.style.cssText = `
                display: flex;
                justify-content: flex-start;
                margin-top: 16px;
            `;

            const uploadButton = document.createElement('button');
            uploadButton.textContent = '上传并导入新模型';
            uploadButton.style.cssText = `
                padding: 8px 16px;
                background: #4766C2;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                &:hover {
                    background: #3d57a3;
                }
            `;

            const cancelButton = document.createElement('button');
            cancelButton.textContent = '取消';
            cancelButton.style.cssText = `
                padding: 8px 16px;
                background: #f5f5f5;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-left: 16px;
                &:hover {
                    background: #e8e8e8;
                }
            `;

            uploadContainer.appendChild(uploadButton);
            uploadContainer.appendChild(cancelButton);

            modalContent.appendChild(modalTitle);
            modalContent.appendChild(tabsContainer);
            modalContent.appendChild(modelList);
            modalContent.appendChild(uploadContainer);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 显示加载中提示的辅助函数
            const showLoading = (message) => {
                modelList.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                        <p style="color: #666;">${message}</p>
                    </div>
                `;
            };

            // 显示错误提示的辅助函数
            const showError = (message) => {
                modelList.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                        <p style="color: #ff4d4f;">${message}</p>
                    </div>
                `;
            };

            // 加载本地模型
            const loadLocalModels = () => {
                showLoading('正在加载本地模型...');
                
                try {
                    // 从localStorage获取模型缓存索引
                    const cacheIndex = localStorage.getItem('logicleap_model_cache_index');
                    if (!cacheIndex) {
                        modelList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #666;">
                                暂无保存的本地模型
                            </div>
                        `;
                        return;
                    }
                    
                    const modelCache = JSON.parse(cacheIndex);
                    const models = Object.entries(modelCache).map(([number, data]) => ({
                        number,
                        name: data.modelName,
                        importTime: data.importTime
                    }));
                    
                    if (models.length === 0) {
                        modelList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #666;">
                                暂无保存的本地模型
                            </div>
                        `;
                        return;
                    }
                    
                    // 清空现有内容
                    modelList.innerHTML = '';
                    
                    // 按导入时间倒序排序
                    models.sort((a, b) => new Date(b.importTime) - new Date(a.importTime));
                    
                    // 渲染每个模型项
                    models.forEach(model => {
                        const modelItem = document.createElement('div');
                        modelItem.style.cssText = `
                            padding: 16px;
                            border: 1px solid #e8e8e8;
                            border-radius: 4px;
                            margin-bottom: 8px;
                            cursor: pointer;
                            transition: all 0.3s;
                            &:hover {
                                background: #f5f5f5;
                                border-color: #4766C2;
                            }
                        `;

                        modelItem.innerHTML = `
                            <div style="font-weight: 500; margin-bottom: 8px;">
                                ${model.name} (编号: ${model.number})
                            </div>
                            <div style="color: #999; font-size: 12px;">
                                导入时间: ${new Date(model.importTime).toLocaleString()}
                            </div>
                        `;

                        // 为每个模型项添加点击事件
                        modelItem.onclick = async () => {
                            try {
                                // 显示加载状态
                                modelItem.style.opacity = '0.5';
                                modelItem.style.pointerEvents = 'none';
                                
                                // 尝试从localStorage获取模型数据
                                const modelData = localStorage.getItem(`logicleap_model_data_${model.number}`);
                                if (!modelData) {
                                    throw new Error('模型数据不存在或已损坏');
                                }
                                
                                document.body.removeChild(modal);
                                notification.show(`模型"${model.name}"（编号：${model.number}）已加载`, 'success');
                            } catch (error) {
                                console.error('加载本地模型失败:', error);
                                modelItem.style.opacity = '1';
                                modelItem.style.pointerEvents = 'auto';
                                notification.show('加载模型失败: ' + error.message, 'error');
                            }
                        };
                        
                        modelList.appendChild(modelItem);
                    });
                    
                } catch (error) {
                    console.error('加载本地模型失败:', error);
                    showError('加载本地模型失败: ' + error.message);
                }
            };

            // 加载云端模型
            const loadCloudModels = async () => {
                showLoading('正在加载云端模型列表...');
                
                try {
                    // ---- 使用 workApi 获取云端模型列表 ----
                    const result = await workApi.getImageModelsList();
                    // -------------------------------------

                    if (result.code === 200 && result.data) {
                        if (result.data.length === 0) {
                            modelList.innerHTML = `
                                <div style="text-align: center; padding: 20px; color: #666;">
                                    暂无保存的云端模型
                                </div>
                            `;
                            return;
                        }
                        
                        // 清空现有内容
                        modelList.innerHTML = '';
                        
                        // 如果有最近上传的模型，显示在列表最上方
                        if (this.modelData) {
                            try {
                                const recentModel = JSON.parse(this.modelData);
                                const modelItem = document.createElement('div');
                                modelItem.style.cssText = `
                                    padding: 16px;
                                    border: 2px solid #4766C2;
                                    border-radius: 4px;
                                    margin-bottom: 16px;
                                    cursor: pointer;
                                    transition: all 0.3s;
                                    background: #f0f5ff;
                                    &:hover {
                                        background: #e6f7ff;
                                    }
                                `;

                                modelItem.innerHTML = `
                                    <div style="font-weight: 500; margin-bottom: 8px; color: #4766C2;">
                                        最近上传的模型
                                        <span style="
                                            background: #4766C2;
                                            color: white;
                                            padding: 2px 8px;
                                            border-radius: 10px;
                                            font-size: 12px;
                                            margin-left: 8px;
                                        ">最新</span>
                                    </div>
                                    <div style="color: #666; font-size: 14px; margin-bottom: 4px;">
                                        包含 ${recentModel.labels.length} 个类别的分类器模型
                                    </div>
                                    <div style="color: #999; font-size: 12px;">
                                        刚刚上传
                                    </div>
                                `;

                                modelItem.onclick = async () => {
                                    // 关闭弹窗
                                    document.body.removeChild(modal);
                                    
                                    // 创建加载动画
                                    const loadingAnimation = createLoadingAnimation(`正在保存最近上传的模型...`);
                                    
                                    try {
                                        // 更新模型编号 - 自增
                                        this.modelCount++;
                                        const modelNumberToUse = String(this.modelCount);
                                        
                                        // 将模型数据保存到缓存
                                        // 更新内存中的模型索引信息
                                        this.modelCache[modelNumberToUse] = {
                                            modelName: '最近上传的模型',
                                            importTime: new Date().toISOString()
                                        };
                                        
                                        // 只在内存中保存实际模型数据，用于当前会话
                                        this.modelDataCache[modelNumberToUse] = this.modelData;
                                        
                                        // 将索引信息和模型数据保存到浏览器的localStorage中
                                        try {
                                            // 保存模型索引
                                            localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                            // 保存模型数据到localStorage
                                            localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, this.modelData);
                                            
                                            // 触发模型缓存更新事件，通知其他组件
                                            this.dispatchModelCacheUpdatedEvent();
                                            
                                            // 移除加载动画
                                            removeLoadingAnimation();
                                            notification.show(`模型"${model.name}"（编号：${model.number}）已加载`, 'success');
                                        } catch (error) {
                                            console.error('保存模型到浏览器缓存失败:', error);
                                            // 如果保存失败，尝试清理旧模型
                                            this.cleanupModelCache();
                                            
                                            // 再次尝试保存
                                            try {
                                                localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                                localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, this.modelData);
                                                
                                                // 触发模型缓存更新事件，通知其他组件
                                                this.dispatchModelCacheUpdatedEvent();
                                                
                                                // 移除加载动画
                                                removeLoadingAnimation();
                                                notification.show(`模型"${model.name}"（编号：${model.number}）已加载Z`, 'success');
                                            } catch (err) {
                                                console.error('清理后仍然无法保存模型:', err);
                                                removeLoadingAnimation();
                                                notification.show('保存模型失败: ' + err.message, 'error');
                                            }
                                        }
                                    } catch (error) {
                                        console.error('保存最近上传的模型失败:', error);
                                        removeLoadingAnimation();
                                        notification.show('保存模型失败: ' + error.message, 'error');
                                    }
                                };

                                modelList.appendChild(modelItem);

                                // 添加分隔线
                                const divider = document.createElement('div');
                                divider.style.cssText = `
                                    margin: 16px 0;
                                    border-top: 1px solid #e8e8e8;
                                `;
                                modelList.appendChild(divider);
                            } catch (error) {
                                console.error('解析最近上传的模型失败:', error);
                            }
                        }
                        
                        // 渲染云端模型列表
                        result.data.forEach(model => {
                            const modelItem = document.createElement('div');
                            modelItem.style.cssText = `
                                padding: 16px;
                                border: 1px solid #e8e8e8;
                                border-radius: 4px;
                                margin-bottom: 8px;
                                cursor: pointer;
                                transition: all 0.3s;
                                &:hover {
                                    background: #f5f5f5;
                                    border-color: #4766C2;
                                }
                            `;

                            modelItem.innerHTML = `
                                <div style="font-weight: 500; margin-bottom: 8px;">${model.name}</div>
                                <div style="color: #666; font-size: 14px; margin-bottom: 4px;">
                                    ${model.description || '无描述'}
                                </div>
                                <div style="color: #999; font-size: 12px;">
                                    创建时间: ${new Date(model.createTime).toLocaleString()}
                                </div>
                            `;

                            modelItem.onclick = async () => {
                                try {
                                    modelItem.style.opacity = '0.5';
                                    modelItem.style.pointerEvents = 'none';

                                    // 获取模型文件内容 (fetch 保持不变)
                                    const fileResponse = await fetch(model.fileUrl);
                                    if (!fileResponse.ok) {
                                        throw new Error('获取模型文件失败');
                                    }
                                    const modelDataString = await fileResponse.text();
                                    
                                    document.body.removeChild(modal);
                                    
                                    // 创建加载动画
                                    const loadingAnimation = createLoadingAnimation(`正在保存云端模型"${model.name}"...`);
                                    
                                    try {
                                        // 更新模型编号 - 自增
                                        this.modelCount++;
                                        const modelNumberToUse = String(this.modelCount);
                                        
                                        // 将模型数据保存到缓存
                                        // 更新内存中的模型索引信息
                                        this.modelCache[modelNumberToUse] = {
                                            modelName: model.name,
                                            importTime: new Date().toISOString()
                                        };
                                        
                                        // 只在内存中保存实际模型数据，用于当前会话
                                        this.modelDataCache[modelNumberToUse] = modelDataString;
                                        
                                        // 将索引信息和模型数据保存到浏览器的localStorage中
                                        try {
                                            // 保存模型索引
                                            localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                            // 保存模型数据到localStorage
                                            localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelDataString);
                                            
                                            // 触发模型缓存更新事件，通知其他组件
                                            this.dispatchModelCacheUpdatedEvent();
                                            
                                            // 移除加载动画
                                            removeLoadingAnimation();
                                            notification.show(`模型"${model.name}"（编号：${modelNumberToUse}）已加载`, 'success');
                                        } catch (error) {
                                            console.error('保存模型到浏览器缓存失败:', error);
                                            // 如果保存失败，尝试清理旧模型
                                            this.cleanupModelCache();
                                            
                                            // 再次尝试保存
                                            try {
                                                localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                                localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelDataString);
                                                
                                                // 触发模型缓存更新事件，通知其他组件
                                                this.dispatchModelCacheUpdatedEvent();
                                                
                                                // 移除加载动画
                                                removeLoadingAnimation();
                                                notification.show(`模型"${model.name}"（编号：${modelNumberToUse}）已加载`, 'success');
                                            } catch (err) {
                                                console.error('清理后仍然无法保存模型:', err);
                                                removeLoadingAnimation();
                                                notification.show('保存模型失败: ' + err.message, 'error');
                                            }
                                        }
                                    } catch (error) {
                                        console.error('保存云端模型失败:', error);
                                        removeLoadingAnimation();
                                        notification.show('保存模型失败: ' + error.message, 'error');
                                    }
                                } catch (error) {
                                    console.error('获取云端模型失败:', error);
                                    notification.show('获取模型失败: ' + error.message, 'error');
                                    modelItem.style.opacity = '1';
                                    modelItem.style.pointerEvents = 'auto';
                                }
                            };

                            modelList.appendChild(modelItem);
                        });
                    } else {
                        showError(result.message || '获取云端模型失败');
                    }
                } catch (error) {
                    console.error('获取模型列表失败:', error);
                    showError('获取模型列表失败: ' + error.message);
                }
            };

            // 标签切换事件
            localTab.addEventListener('click', () => {
                localTab.style.fontWeight = 'bold';
                localTab.style.color = '#4766C2';
                localTab.style.borderBottom = '2px solid #4766C2';
                
                cloudTab.style.fontWeight = 'normal';
                cloudTab.style.color = '#666';
                cloudTab.style.borderBottom = 'none';
                
                loadLocalModels();
            });
            
            cloudTab.addEventListener('click', () => {
                cloudTab.style.fontWeight = 'bold';
                cloudTab.style.color = '#4766C2';
                cloudTab.style.borderBottom = '2px solid #4766C2';
                
                localTab.style.fontWeight = 'normal';
                localTab.style.color = '#666';
                localTab.style.borderBottom = 'none';
                
                loadCloudModels();
            });

            // 绑定按钮事件
            uploadButton.onclick = async () => {
                document.body.removeChild(modal);
                await this.uploadModel();
            };

            cancelButton.onclick = () => {
                document.body.removeChild(modal);
            };

            // 默认加载本地模型
            loadLocalModels();

            return;
        } catch (error) {
            console.error('导入模型出错:', error);
            notification.show('导入模型失败: ' + error.message, 'error');
            return;
        }
    }

    /**
     * 编辑模型 - 选择模型文件并在训练界面中编辑
     * @returns {Promise<string>} 操作状态
     */
    async editModel() {
        console.log('editModel: 开始执行编辑模型功能');
        try {
            // 使用JSON文件上传器选择模型文件
            console.log('editModel: 打开文件选择器');
            const modelData = await createJsonFileUploader({
                title: '选择要编辑的模型文件',
                themeColor: '#FF6B6B',
                maxSize: 10 * 1024 * 1024 // 限制为10MB
            });

            console.log('editModel: 文件选择完成，数据长度:', modelData?.length);

            if (!modelData) {
                return ''; // 返回空字符串避免显示取消消息
            }

            // 检查是否为有效的JSON
            let modelJson;
            try {
                modelJson = JSON.parse(modelData);
                console.log('editModel: JSON解析成功，模型数据:', modelJson);
            } catch (parseError) {
                console.error('editModel: JSON解析失败:', parseError);
                notification.show('模型文件格式错误，无法解析JSON数据', 'error');
                return;
            }

            // 验证是否为模型文件
            if (!modelJson || !modelJson.labels || modelJson.modelType !== 'knn-classifier') {
                console.error('editModel: 模型验证失败：缺少必要字段或格式不正确');
                console.log('editModel: 模型数据详情:', {
                    hasModelJson: !!modelJson,
                    hasLabels: !!(modelJson && modelJson.labels),
                    modelType: modelJson && modelJson.modelType
                });
                notification.show('无效的模型文件，请选择正确的模型文件', 'error');
                return;
            }

            // 保存模型数据到全局变量，供训练界面使用
            console.log('editModel: 保存模型数据到全局变量');
            window._pendingModelDataForEdit = modelData;
            console.log('editModel: 全局变量已设置，数据长度:', window._pendingModelDataForEdit?.length);

            // 打开训练界面
            console.log('editModel: 打开训练界面');
            this.showTrainDialog();

            notification.show('训练界面已打开，模型数据将自动导入', 'success');

        } catch (error) {
            console.error('编辑模型错误:', error);
            notification.show(error.message || '编辑模型失败', 'error');
            return;
        }
    }

    /**
     * 导入模型数据
     * @param {string} modelData - 模型数据字符串（JSON格式）
     * @param {string} modelName - 模型名称
     * @param {string|null} modelNumber - 模型编号，如果为null则自动生成
     * @param {boolean} [skipAnimation] - 是否跳过加载动画，默认为false
     * @private
     */
    async _importModelData(modelData, modelName, modelNumber = null, skipAnimation = false) {
        // 添加调试日志
        console.log(`_importModelData 被调用，skipAnimation=${skipAnimation}`);
        
        // 返回一个Promise，确保方法完成后才解析
        return new Promise((resolve, reject) => {
            // 显示加载动画（如果不跳过）
            let loadingAnimation = null;
            if (!skipAnimation) {
                console.log('显示加载动画');
                loadingAnimation = createLoadingAnimation(`正在导入模型"${modelName}"...`);
                updateLoadingProgress('初始化...');
            } else {
                console.log('跳过加载动画');
            }
            
            // 使用setTimeout确保UI能够更新
            setTimeout(async () => {
                try {
                    // 初始化ImageMode但不打开训练窗口
                    if (!this.imageMode) {
                        if (!skipAnimation) {
                            updateLoadingProgress('初始化图像模型引擎...');
                        }
                        this.imageMode = new ImageMode();
                    }
                    
                    // 导入模型数据
                    try {
                        if (!skipAnimation) {
                            updateLoadingProgress('解析模型数据...');
                            // 给模型导入添加一个小延迟，确保动画显示
                            await new Promise(resolveDelay => setTimeout(resolveDelay, 300));
                            
                            updateLoadingProgress('导入模型中...');
                        }
                        
                        // 创建一个Promise来等待importModelData完成
                        const importPromise = new Promise(resolveImport => {
                            // 传递skipAnimation参数
                            try {
                                const result = this.imageMode.importModelData(modelData, skipAnimation);
                                resolveImport(result);
                            } catch (err) {
                                console.error('导入模型数据时出错:', err);
                                reject(err);
                            }
                        });
                        
                        // 等待导入完成
                        await importPromise;
                        
                        if (!skipAnimation) {
                            updateLoadingProgress('完成！');
                            // 延迟一下再移除动画，让用户看到完成状态
                            setTimeout(() => {
                                // 移除加载动画
                                removeLoadingAnimation();
                            }, 500);
                        }
                        
                        // 更新模型状态
                        this.modelImported = true;
                        
                        // 更新模型编号 - 如果提供了编号则使用该编号，否则自增
                        let modelNumberToUse;
                        if (modelNumber) {
                            modelNumberToUse = modelNumber;
                        } else {
                            this.modelCount++; // 只有未提供编号时才自增
                            modelNumberToUse = String(this.modelCount);
                        }
                        
                        console.log('模型已成功导入，当前模型编号:', modelNumberToUse);
                        
                        // 更新内存中的模型索引信息
                        this.modelCache[modelNumberToUse] = {
                            modelName: modelName,
                            importTime: new Date().toISOString()
                        };
                        
                        // 只在内存中保存实际模型数据，用于当前会话
                        this.modelDataCache[modelNumberToUse] = modelData;
                        
                        // 更新当前模型信息 - 这是关键修复
                        this.currentModel = {
                            modelNumber: modelNumberToUse,
                            modelName: modelName
                        };
                        
                        console.log('当前模型已设置为:', this.currentModel);
                        
                        // 将索引信息和模型数据保存到浏览器的localStorage中
                        try {
                            // 保存模型索引
                            localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                            // 保存模型数据到localStorage
                            localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelData);
                            
                            // 触发模型缓存更新事件，通知其他组件
                            this.dispatchModelCacheUpdatedEvent();
                        } catch (error) {
                            console.error('保存模型到浏览器缓存失败:', error);
                            // 如果保存失败，尝试清理旧模型
                            this.cleanupModelCache();
                            
                            // 再次尝试保存
                            try {
                                localStorage.setItem('logicleap_model_cache_index', JSON.stringify(this.modelCache));
                                localStorage.setItem(`logicleap_model_data_${modelNumberToUse}`, modelData);
                            } catch (err) {
                                console.error('清理后仍然无法保存模型:', err);
                            }
                            
                            // 即便清理后，仍然尝试触发事件
                            this.dispatchModelCacheUpdatedEvent();
                        }
                        
                        // 导入成功，解析Promise
                        resolve();
                    } catch (error) {
                        console.error('导入模型失败:', error);
                        // 移除加载动画
                        if (!skipAnimation) {
                            removeLoadingAnimation();
                        }
                        notification.show('导入模型失败: ' + error.message, 'error');
                        reject(error);
                    }
                } catch (error) {
                    console.error('初始化模型引擎失败:', error);
                    // 移除加载动画
                    if (!skipAnimation) {
                        removeLoadingAnimation();
                    }
                    notification.show('初始化模型引擎失败: ' + error.message, 'error');
                    reject(error);
                }
            }, 100);
        });
    }
    
    /**
     * 获取模型标签菜单
     * @returns {Array} 模型标签菜单项
     * @private
     */
    _getModelLabelsMenu() {
        // 如果没有加载模型，返回默认项
        if (!this.imageMode || !this.modelImported || !this.imageMode.nodeData || !this.imageMode.nodeData.classNodes) {
            return [
                {
                    text: '未加载模型',
                    value: 'none'
                }
            ];
        }
        
        try {
            // 从模型中获取标签
            const labels = this.imageMode.nodeData.classNodes.map((node, index) => {
                const labelText = node.node.querySelector('input')?.value || `类别${index}`;
                return {
                    text: labelText,
                    value: labelText
                };
            });
            
            return labels.length > 0 ? labels : [{ text: '未找到标签', value: 'none' }];
        } catch (error) {
            console.error('获取模型标签失败:', error);
            return [{ text: '获取标签失败', value: 'error' }];
        }
    }
    
    /**
     * 获取图像预测的属性（标签或置信度）
     * @param {object} args - 积木参数
     * @returns {string} 属性值
     */
    async getImageProperty(args) {
        // 如果模型未导入，提示用户先导入模型
        if (!this.imageMode || !this.modelImported) {
            // notification.show('请先导入模型', 'error');
            return '';
        }

        const imageInfo = args.IMAGE;
        const property = args.PROPERTY;

        let imageData;
      try {
        // 获取图像数据
        imageData = await getImage(imageInfo, "base64");
      } catch (error) {
        console.error('获取图像数据失败:', error);
        notification.show('获取图像数据失败: ' + error.message, 'error');
        return;
      }
        
        console.log('图像数据:', imageData);
        
        console.log('属性:', property);
        
        // 如果提供了图像数据，则进行预测
        if (imageData) {
            try {
                const tf = require('@tensorflow/tfjs');
                
                // 创建Image对象加载图像
                const img = new Image();
                // 添加crossOrigin属性解决CORS问题
                img.crossOrigin = 'anonymous';
                
                // 创建一个Promise来等待图像加载
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = () => reject(new Error('图像加载失败'));
                    img.src = imageData;
                });
                
                // 创建canvas并设置大小
                const canvas = document.createElement('canvas');
                canvas.width = 227;
                canvas.height = 227;
                const ctx = canvas.getContext('2d');
                
                // 在canvas上绘制图像
                ctx.drawImage(img, 0, 0, 227, 227);
                
                // 使用TensorFlow预测
                const tensor = tf.browser.fromPixels(canvas);
                // 使用与imageMode.js相同的特征层
                const logits = this.imageMode.mobilenet.infer(tensor, 'conv_pw_13_relu');
                
                // 使用KNN分类器预测
                const result = await this.imageMode.knn.predictClass(logits, 10);
                
                // 获取预测结果
                const classIndex = result.label;
                const confidence = result.confidences[classIndex] * 100;
                
                //console.log('预测结果详情:', {
                    // classIndex: classIndex,
                //     allConfidences: result.confidences,
                //     confidence: confidence
                // });
                
                // 获取类别名称
                let className = `类别${Number(classIndex) + 1}`; // 默认标签
                
                // 尝试从不同地方获取标签
                let labels = [];
                
                // 1. 首先尝试从modelData获取，因为这是最可靠的来源
                if (this.modelDataCache[this.currentModel.modelNumber]) {
                    try {
                        const modelData = JSON.parse(this.modelDataCache[this.currentModel.modelNumber]);
                        if (modelData.labels && modelData.labels.length > 0) {
                            labels = modelData.labels;
                        }
                    } catch (e) {
                        console.warn('从缓存解析标签失败:', e);
                    }
                }
                
                // 2. 如果从modelData获取失败，再尝试从nodeData中获取
                if ((!labels || labels.length === 0) && this.imageMode.nodeData && this.imageMode.nodeData.classNodes) {
                    labels = this.imageMode.nodeData.classNodes.map(node => {
                        return node.node.querySelector('input')?.value || `类别${node.index + 1}`;
                    });
                }
                
                //console.log('可用标签:', labels);
                
                // 确保索引在有效范围内
                if (labels && labels.length > 0 && classIndex >= 0 && classIndex < labels.length) {
                    className = labels[classIndex];
                    //console.log(`使用标签: ${className} (索引${classIndex})`);
                } else {
                    //console.log(`使用默认标签: ${className} (索引${classIndex})`);
                }
                
                // 确保置信度是有效数值
                let validConfidence = confidence;
                if (isNaN(validConfidence) || !isFinite(validConfidence)) {
                    console.warn('置信度无效:', confidence);
                    validConfidence = 0;
                }
                
                // 保存预测结果
                this.lastPrediction = {
                    label: className,
                    confidence: validConfidence
                };
                
                // 清理资源
                tensor.dispose();
                logits.dispose();
                
                // //console.log(`预测完成，结果: ${className}，置信度: ${confidence.toFixed(1)}%`);
            } catch (error) {
                console.error('图像预测失败:', error);
                notification.show(error.message, 'error');
                return;
            }
        }
        
        // 根据参数返回对应的属性
        switch (property) {
            case 'label':
                return this.lastPrediction.label || '未知标签';
            case 'confidence':
                return this.lastPrediction.confidence > 0 ? 
                    `${this.lastPrediction.confidence.toFixed(1)}%` : 
                    '未知置信度';
            case 'both':
                if (!this.lastPrediction.label) return '未知标签';
                const label = this.lastPrediction.label;
                const confidence = this.lastPrediction.confidence > 0 ? 
                    `${this.lastPrediction.confidence.toFixed(1)}%` : 
                    '未知置信度';
                return `${label} (${confidence})`;
            default:
                return '未知属性';
        }
    }
    
    /**
     * 使用导入的模型进行图像预测
     * @param {object} args - 积木参数
     * @returns {Promise<string>} 预测结果
     */
    async predictImage(args) {
        try {
            // 获取用户选择的属性
            const property = args.PROPERTY || 'both';
            
            // 检查模型是否已导入
            if (!this.imageMode || !this.modelImported) {
                // console.error('模型尚未导入，请先导入模型');
                // notification.show('请先导入模型', 'error');
                return '';
            }
            
            // //console.log('开始获取摄像头画面进行预测...');
            
            // 启用摄像头并显示在舞台上
            const videoDesc = {
                width: {min: 640, ideal: 1280},
                height: {min: 480, ideal: 720}
            };
            this.runtime.ioDevices.video.enableVideo(videoDesc);
            this.runtime.ioDevices.video.mirror = true;
            this.runtime.ioDevices.video.setPreviewGhost(0); // 设置透明度为0，完全显示
            
            // 等待摄像头准备好
            if (!this.runtime.ioDevices.video.videoReady) {
                await new Promise(resolve => {
                    const checkVideo = () => {
                        if (this.runtime.ioDevices.video.videoReady) {
                            resolve();
                        } else {
                            setTimeout(checkVideo, 100);
                        }
                    };
                    checkVideo();
                });
            }
            
            // 等待一点时间，确保摄像头图像稳定
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 单次预测
            try {
                const tf = require('@tensorflow/tfjs');
                
                // 获取视频帧
                const frame = this.runtime.ioDevices.video.getFrame({
                    format: 'image-data',
                    dimensions: [227, 227] // MobileNet 输入大小
                });
                
                if (!frame) {
                    console.error('无法获取视频帧');
                    notification.show('无法获取视频画面', 'error');
                    return;
                }
                
                // 创建临时canvas以将ImageData转换为tf tensor
                const canvas = document.createElement('canvas');
                canvas.width = 227;
                canvas.height = 227;
                const ctx = canvas.getContext('2d');
                ctx.putImageData(frame, 0, 0);
                
                // 进行预测
                const image = tf.browser.fromPixels(canvas);
                // 使用与imageMode.js相同的特征层
                const logits = this.imageMode.mobilenet.infer(image, 'conv_pw_13_relu');
                
                // 使用KNN分类器预测
                const result = await this.imageMode.knn.predictClass(logits, 10);
                
                // 获取预测结果
                const classIndex = result.label;
                const confidence = result.confidences[classIndex] * 100;
                
                //console.log('预测结果详情:', {
                //     classIndex: classIndex,
                //     allConfidences: result.confidences,
                //     confidence: confidence
                // });
                
                // 获取类别名称
                let className = `类别${Number(classIndex) + 1}`; // 修正:索引从0开始，但显示从1开始
                
                // 尝试从不同地方获取标签
                let labels = [];
                
                // 1. 首先尝试从modelData获取，因为这是最可靠的来源
                if (this.modelDataCache[this.currentModel.modelNumber]) {
                    try {
                        const modelData = JSON.parse(this.modelDataCache[this.currentModel.modelNumber]);
                        if (modelData.labels && modelData.labels.length > 0) {
                            labels = modelData.labels;
                        }
                    } catch (e) {
                        console.warn('从缓存解析标签失败:', e);
                    }
                }
                
                // 2. 如果从modelData获取失败，再尝试从nodeData中获取
                if ((!labels || labels.length === 0) && this.imageMode.nodeData && this.imageMode.nodeData.classNodes) {
                    labels = this.imageMode.nodeData.classNodes.map(node => {
                        return node.node.querySelector('input')?.value || `类别${node.index + 1}`;
                    });
                }
                
                //console.log('可用标签:', labels);
                
                // 确保索引在有效范围内
                if (labels && labels.length > 0 && classIndex >= 0 && classIndex < labels.length) {
                    className = labels[classIndex];
                    //console.log(`使用标签: ${className} (索引${classIndex})`);
                } else {
                    //console.log(`使用默认标签: ${className} (索引${classIndex})`);
                }
                
                // 确保置信度是有效数值
                let validConfidence = confidence;
                if (isNaN(validConfidence) || !isFinite(validConfidence)) {
                    console.warn('置信度无效:', confidence);
                    validConfidence = 0;
                }
                
                // 保存预测结果
                this.lastPrediction = {
                    label: className,
                    confidence: validConfidence
                };
                
                // 清理资源
                image.dispose();
                logits.dispose();
                
                // //console.log(`预测完成，结果: ${className}，置信度: ${confidence.toFixed(1)}%`);
                
                // 根据用户选择的属性返回不同的结果
                switch (property) {
                    case 'label':
                        return className;
                    case 'confidence':
                        return `${confidence.toFixed(1)}%`;
                    case 'both':
                    default:
                        return `${className} (${confidence.toFixed(1)}%)`;
                }
            } catch (error) {
                console.error('预测失败:', error);
                notification.show(error.message, 'error');
                return;
            }
        } catch (error) {
            console.error('预测图像失败:', error);
            notification.show(error.message, 'error');
            return;
        }
    }

    /**
     * 关闭摄像头
     * @returns {string} 操作状态
     */
    closeCamera() {
        try {
            if (this.runtime && this.runtime.ioDevices && this.runtime.ioDevices.video) {
                this.runtime.ioDevices.video.disableVideo();
                // //console.log('成功关闭摄像头');
            }
            notification.show('已关闭摄像头', 'success');
            return;
        } catch (error) {
            console.error('关闭摄像头失败:', error);
            notification.show('关闭摄像头失败', 'error');
            return;
        }
    }

    /**
     * 获取当前模型编号
     * @returns {string} 当前模型编号
     */
    getModelNumber() {
        // 如果有当前模型，返回当前模型编号
        //console.log('获取当前模型编号，currentModel =', this.currentModel);
        //console.log('当前模型已导入状态:', this.modelImported);
        //console.log('可用模型缓存:', Object.keys(this.modelCache));
        
        if (this.currentModel && this.currentModel.modelNumber) {
            //console.log('返回当前模型编号:', this.currentModel.modelNumber);
            return String(this.currentModel.modelNumber);
        }
        // 如果没有当前模型但有缓存的模型，返回提示信息
        const modelNumbers = Object.keys(this.modelCache);
        if (modelNumbers.length > 0) {
            //console.log('有模型但未加载任何模型，返回提示信息');
            notification.show('未加载模型', 'error');
            return '';
        }
        // 如果完全没有模型，返回提示信息
        //console.log('没有可用模型，返回提示信息');
        notification.show('未加载模型', 'error');
        return '';
    }

    /**
     * 清理模型缓存，移除最旧的模型直到缓存大小适合保存
     * @private
     */
    cleanupModelCache() {
        try {
            // 从 localStorage 读取最新的缓存索引
            const cacheIndex = localStorage.getItem('logicleap_model_cache_index');
            if (!cacheIndex) {
                // //console.log('没有找到模型缓存索引，无需清理');
                return;
            }
            
            // 解析缓存索引
            const modelCache = JSON.parse(cacheIndex);
            
            // 将缓存转换为数组并按照导入时间排序
            const cacheEntries = Object.entries(modelCache)
                .sort((a, b) => new Date(a[1].importTime) - new Date(b[1].importTime));
            
            // //console.log(`开始清理模型缓存，当前有 ${cacheEntries.length} 个模型`);
            
            // 如果缓存条目数量超过5个，移除最旧的条目，只保留最新的5个
            while (cacheEntries.length > 5) {
                const [oldestKey] = cacheEntries.shift();
                // 从模型缓存中移除
                delete modelCache[oldestKey];
                // 从本地存储中移除模型数据
                localStorage.removeItem(`logicleap_model_data_${oldestKey}`);
                // //console.log('已移除最旧的模型缓存:', oldestKey);
            }
            
            // 更新本地存储中的缓存索引
            localStorage.setItem('logicleap_model_cache_index', JSON.stringify(modelCache));
            // //console.log('模型缓存已清理，当前保留', Object.keys(modelCache).length, '个模型');
            
            // 同步更新内存中的缓存
            this.modelCache = modelCache;
        } catch (error) {
            console.error('清理模型缓存失败:', error);
            
            // 如果清理过程出错，尝试移除全部缓存
            try {
                // 获取所有 localStorage 中的键
                const keys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('logicleap_model_data_')) {
                        keys.push(key);
                    }
                }
                
                // 移除所有模型数据
                keys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // 移除缓存索引
                localStorage.removeItem('logicleap_model_cache_index');
                
                // //console.log('已清空所有模型缓存');
                
                // 重置内存中的缓存
                this.modelCache = {};
                this.modelDataCache = {};
            } catch (err) {
                console.error('清空模型缓存失败:', err);
            }
        }
    }
    
    /**
     * 根据模型编号加载已缓存的模型
     * @param {object} args 包含模型编号的参数对象
     * @returns {string} 操作状态
     */
    loadModelByNumber(args) {
        // 返回Promise，使积木块保持高亮直到加载完成
        return new Promise(async (resolve) => {
            try {
                const modelNumber = Cast.toString(args.MODEL_NUMBER);
                console.log('尝试加载模型编号:', modelNumber);
                
                // 检查缓存中是否有该编号的模型
                if (!this.modelCache[modelNumber]) {
                    notification.show(`找不到编号为 ${modelNumber} 的模型`, 'error');
                    return resolve();
                }
                
                const cachedModel = this.modelCache[modelNumber];
                console.log('找到缓存的模型:', cachedModel);
                
                // 检查是否有内存中的模型数据
                if (!this.modelDataCache[modelNumber]) {
                    // 尝试从localStorage获取
                    const storedModelData = localStorage.getItem(`logicleap_model_data_${modelNumber}`);
                    if (storedModelData) {
                        // 如果找到了，加载到内存中
                        this.modelDataCache[modelNumber] = storedModelData;
                        console.log(`已从localStorage恢复模型数据（编号：${modelNumber}）`);
                    } else {
                        notification.show(`模型"${cachedModel.modelName}"（编号：${modelNumber}）数据不在内存中，请重新上传`, 'warning');
                        return resolve();
                    }
                }
                
                console.log('准备调用_importModelData，skipAnimation=true');
                
                // 创建一个Promise来等待_importModelData完成
                try {
                    // 调用导入模型函数，传递原有的模型编号，并跳过动画
                    await this._importModelData(this.modelDataCache[modelNumber], cachedModel.modelName, modelNumber, true);
                    
                    // 导入成功后显示成功通知
                    notification.show(`已加载模型 "${cachedModel.modelName}"（编号：${modelNumber}）`, 'success');
                    
                    // 完成后解析Promise
                    resolve(`已加载模型 "${cachedModel.modelName}"（编号：${modelNumber}）`);
                } catch (importError) {
                    console.error('导入模型数据失败:', importError);
                    notification.show('导入模型数据失败: ' + importError.message, 'error');
                    resolve('导入模型数据失败: ' + importError.message);
                }
            } catch (error) {
                console.error('加载缓存模型失败:', error);
                notification.show('加载缓存模型失败: ' + error.message, 'error');
                resolve('加载缓存模型失败: ' + error.message);
            }
        });
    }

    /**
     * 列出所有缓存的模型
     * @returns {string} 缓存的模型编号列表
     */
    listCachedModels() {
        const modelNumbers = Object.keys(this.modelCache);
        if (modelNumbers.length === 0) {
            notification.show('没有缓存的模型', 'error');
            return;
        }
        
        // 格式化输出：编号: 模型名称 (导入时间) [状态]
        const modelList = modelNumbers.map(num => {
            const model = this.modelCache[num];
            const importDate = new Date(model.importTime);
            const dateStr = `${importDate.getFullYear()}-${importDate.getMonth()+1}-${importDate.getDate()}`;
            const status = this.modelDataCache[num] ? '可用' : '需重新上传';
            return `${num}: ${model.modelName} (${dateStr}) [${status}]`;
        }).join('\n');
        
        return modelList;
    }

    /**
     * 获取所有缓存的模型数据
     * @returns {Object} 所有缓存的模型数据
     */
    getCachedModelsData() {
        const result = {};
        try {
            // 从 localStorage 读取模型缓存索引
            const cacheIndex = localStorage.getItem('logicleap_model_cache_index');
            if (cacheIndex) {
                const modelCache = JSON.parse(cacheIndex);
                
                // 遍历所有模型索引，从 localStorage 读取每个模型的数据
                Object.keys(modelCache).forEach(modelNumber => {
                    try {
                        const modelData = localStorage.getItem(`logicleap_model_data_${modelNumber}`);
                        if (modelData) {
                            result[modelNumber] = {
                                ...modelCache[modelNumber],
                                data: modelData
                            };
                        }
                    } catch (err) {
                        console.error(`读取模型 ${modelNumber} 数据失败:`, err);
                    }
                });
            }
        } catch (error) {
            console.error('获取缓存模型数据失败:', error);
        }
        return result;
    }

    /**
     * 触发模型缓存更新事件
     * 该事件可以被其他组件监听，以便在模型缓存更新时执行相应操作
     */
    dispatchModelCacheUpdatedEvent() {
        try {
            // 创建一个自定义事件
            const event = new CustomEvent('logicleap_model_cache_updated', {
                detail: {
                    modelCache: this.modelCache,
                    modelsCount: Object.keys(this.modelCache).length
                }
            });
            
            // 在全局 window 对象上触发事件
            window.dispatchEvent(event);
            // //console.log('已触发模型缓存更新事件');
        } catch (error) {
            console.error('触发模型缓存更新事件失败:', error);
        }
    }

    /**
     * 切换到指定编号的模型
     * @param {number} modelNumber - 模型编号
     * @returns {Promise<boolean>} 是否切换成功
     */
    async switchToModel(modelNumber) {
        try {
            // 检查模型是否在缓存中
            if (!this.modelCache[modelNumber]) {
                console.warn(`模型 ${modelNumber} 不在缓存中`);
                return false;
            }

            // 获取缓存的模型数据
            const modelData = this.modelDataCache[modelNumber];
            if (!modelData) {
                console.warn(`模型 ${modelNumber} 的数据不在内存中`);
                return false;
            }

            // 创建新的ImageMode实例并导入模型数据
            try {
                if (!this.imageMode) {
                    this.imageMode = new ImageMode();
                }
                
                // 导入模型数据
                this.imageMode.importModelData(modelData);
                
                // 更新状态
                this.currentModel = {
                    modelNumber: modelNumber,
                    ...this.modelCache[modelNumber]
                };
                this.modelImported = true;
                return true;
            } catch (error) {
                console.error('导入模型数据失败:', error);
                notification.show('切换模型失败: ' + error.message, 'error');
                return false;
            }
        } catch (error) {
            console.error('切换模型失败:', error);
            notification.show('切换模型失败: ' + error.message, 'error');
            return false;
        }
    }

    /**
     * 获取当前模型编号
     * @returns {number} 当前模型编号，如果没有模型则返回0
     */
    getCurrentModelNumber() {
        // 如果有当前模型，返回当前模型编号
        if (this.currentModel && this.currentModel.modelNumber) {
            return parseInt(this.currentModel.modelNumber);
        }
        // 如果没有当前模型但有缓存的模型，返回最后一个模型的编号
        const modelNumbers = Object.keys(this.modelCache);
        if (modelNumbers.length > 0) {
            return Math.max(...modelNumbers.map(num => parseInt(num)));
        }
        // 如果完全没有模型，返回0
        return 0;
    }

    /**
     * 获取所有可用的模型编号列表
     * @returns {string} 模型编号列表，用逗号分隔
     */
    getAvailableModelNumbers() {
        if (!this.modelData || this.modelData.length === 0) {
            notification.show('未加载模型', 'error');
            return;
        }
        return this.modelData.map(m => m.modelNumber).join(',');
    }
}

// 恢复为 CommonJS 导出
module.exports = function (runtime) {
    return new LogicLeapImageTrainBlocks(runtime);
};
