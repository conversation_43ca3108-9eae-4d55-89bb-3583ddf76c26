import { WebSocketClient } from 'logic-common/dist/api/web_socket/webSocket';

type NetworkStatusListener = (isOnline: boolean) => void;

// 定义检测级别和权重
enum DetectionLevel {
  HIGH = 'high',       // 高优先级检测，如navigator.onLine
  MEDIUM = 'medium',   // 中优先级检测，如WebSocket
  LOW = 'low'          // 低优先级检测，如心跳请求
}

interface DetectionResult {
  level: DetectionLevel;
  passed: boolean;
  name: string;
}

/**
 * 网络状态检测器
 * 结合多种方式检测网络连接状态，使用分级检测策略:
 * 1. 高级：浏览器navigator.onLine API（高可靠性，快速）
 * 2. 中级：WebSocket连接状态（中可靠性）
 * 3. 低级：心跳请求检测（高准确性，但较慢）
 */
export class NetworkDetector {
  private webSocket?: WebSocketClient;
  private listeners: NetworkStatusListener[] = [];
  private isOnline: boolean = true;
  private checkInterval: NodeJS.Timeout | null = null;
  private pingUrl: string = '';
  private pingIntervalMs: number = 30000; // 默认30秒检测一次
  private consecutiveFailures: number = 0;
  private maxConsecutiveFailures: number = 2; // 连续失败次数阈值
  
  // 检测级别权重配置
  private levelWeights = {
    [DetectionLevel.HIGH]: 3,    // 高优先级权重
    [DetectionLevel.MEDIUM]: 2,  // 中优先级权重
    [DetectionLevel.LOW]: 1      // 低优先级权重
  };
  
  // 各检测方法的通过阈值比例
  private passThreshold: number = 0.6; // 总分数达到60%认为网络正常

  /**
   * 创建网络检测器实例
   * @param options 配置选项
   */
  constructor(options?: {
    webSocket?: WebSocketClient;
    pingUrl?: string;
    pingIntervalMs?: number;
    maxConsecutiveFailures?: number;
    passThreshold?: number;
    levelWeights?: {
      high?: number;
      medium?: number;
      low?: number;
    };
  }) {
    this.webSocket = options?.webSocket;
    this.pingUrl = options?.pingUrl || '/api/web/user/info/ping';
    this.pingIntervalMs = options?.pingIntervalMs || 30000;
    this.maxConsecutiveFailures = options?.maxConsecutiveFailures || 2;
    
    // 可自定义通过阈值
    if (options?.passThreshold && options.passThreshold > 0 && options.passThreshold <= 1) {
      this.passThreshold = options.passThreshold;
    }
    
    // 可自定义级别权重
    if (options?.levelWeights) {
      if (options.levelWeights.high) this.levelWeights[DetectionLevel.HIGH] = options.levelWeights.high;
      if (options.levelWeights.medium) this.levelWeights[DetectionLevel.MEDIUM] = options.levelWeights.medium;
      if (options.levelWeights.low) this.levelWeights[DetectionLevel.LOW] = options.levelWeights.low;
    }

    // 初始状态使用navigator.onLine
    this.isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
    
    this.init();
  }

  /**
   * 初始化检测器
   */
  private init(): void {
    if (typeof window !== 'undefined') {
      // 监听浏览器在线状态变化事件
      window.addEventListener('online', () => this.updateStatus(true));
      window.addEventListener('offline', () => this.updateStatus(false));

      // 启动定期检测
      this.startChecking();
    }
  }

  /**
   * 开始定期检测网络状态
   */
  private startChecking(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      this.checkNetworkStatus();
    }, this.pingIntervalMs);

    // 立即执行一次检测
    this.checkNetworkStatus();
  }

  /**
   * 停止定期检测
   */
  public stopChecking(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * 检测浏览器API网络状态（高优先级）
   * @returns 检测结果
   */
  private async checkBrowserAPI(): Promise<DetectionResult> {
    const result: DetectionResult = {
      level: DetectionLevel.HIGH,
      passed: false,
      name: 'navigator.onLine'
    };
    
    if (typeof navigator !== 'undefined') {
      result.passed = navigator.onLine;
    }
    
    return result;
  }
  
  /**
   * 检测WebSocket连接状态（中优先级）
   * @returns 检测结果
   */
  private async checkWebSocket(): Promise<DetectionResult> {
    const result: DetectionResult = {
      level: DetectionLevel.MEDIUM,
      passed: false,
      name: 'WebSocket'
    };
    
    if (this.webSocket) {
      try {
        const isConnected = this.webSocket.isConnected();
        result.passed = isConnected;
        
        // 获取WebSocket详细连接信息
        try {
          if (this.webSocket.getConnectionInfo) {
            const connectionInfo: any = this.webSocket.getConnectionInfo();
            
            // 格式化连接信息输出
            if (connectionInfo && typeof connectionInfo === 'object') {
              // 解析连接状态信息
              const connStatus = connectionInfo.status || 'unknown';
              const socketId = connectionInfo.socketId || 'unknown';
              const userId = connectionInfo.userId || 'unknown';
              
              // 转换时间戳为可读格式
              let lastConnTime = '未连接';
              if (connectionInfo.lastConnectionTime) {
                const date = new Date(connectionInfo.lastConnectionTime);
                lastConnTime = date.toLocaleTimeString();
              }
              
              // 根据状态字段判断连接状态
              if (connStatus === 'connected') {
                result.passed = true;
              }
              
              console.log('WebSocket连接详情:');
              console.log(`- 状态: ${connStatus}`);
              console.log(`- Socket ID: ${socketId}`);
              console.log(`- 用户ID: ${userId}`);
              console.log(`- 最近连接时间: ${lastConnTime}`);
              
              if (connectionInfo.error) {
                console.log(`- 错误信息: ${connectionInfo.error}`);
              }
              
              // 服务器信息
              if (connectionInfo.serverInfo) {
                console.log('- 服务器信息:', connectionInfo.serverInfo);
              }
            } else if (typeof connectionInfo === 'string') {
              console.log(`WebSocket连接信息: ${connectionInfo}`);
            } else {
              console.log('WebSocket连接信息不可用');
            }
          }
        } catch (infoError) {
          console.error('获取WebSocket连接详情时出错:', infoError);
        }
        
        console.log(`WebSocket连接状态: ${result.passed ? '已连接' : '未连接'}`);
      } catch (error) {
        console.error('检测WebSocket连接状态时出错:', error);
        result.passed = false;
      }
    } else {
      // console.log('WebSocket未初始化，跳过WebSocket连接检测');
    }
    
    return result;
  }
  
  /**
   * 检测心跳请求（低优先级）
   * @returns 检测结果
   */
  private async checkHeartbeat(): Promise<DetectionResult> {
    const result: DetectionResult = {
      level: DetectionLevel.LOW,
      passed: false,
      name: 'Heartbeat'
    };
    
    try {
      const pingResult = await this.sendPing();
      result.passed = pingResult;
    } catch (error) {
      console.error('心跳检测失败:', error);
    }
    
    return result;
  }

  /**
   * 分级检测网络状态
   */
  private async checkNetworkStatus(): Promise<void> {
    // console.log('===== 开始网络检测 =====');
    try {
      // 运行各级别检测
      // console.log('1. 执行浏览器API网络状态检测...');
      const browserResult = await this.checkBrowserAPI();
      // console.log(`浏览器API检测结果: ${browserResult.passed ? '通过' : '失败'}`);
      
      // console.log('2. 执行WebSocket连接状态检测...');
      const webSocketResult = await this.checkWebSocket();
      
      // console.log('3. 执行心跳请求检测...');
      const heartbeatResult = await this.checkHeartbeat();
      // console.log(`心跳检测结果: ${heartbeatResult.passed ? '通过' : '失败'}`);
      
      // 收集检测结果
      const results = [browserResult, webSocketResult, heartbeatResult];
      
      // 计算加权分数
      let totalScore = 0;
      let totalWeight = 0;
      
      results.forEach(result => {
        const weight = this.levelWeights[result.level];
        totalWeight += weight;
        if (result.passed) {
          totalScore += weight;
        }
      });
      
      // 计算得分率
      const scoreRate = totalWeight > 0 ? totalScore / totalWeight : 0;
      
      // 生成详细日志
      // console.log('网络检测详情:', results.map(r => `${r.name}: ${r.passed ? '通过' : '失败'}`).join(', '));
      // console.log(`网络检测得分: ${totalScore}/${totalWeight}, 得分率: ${(scoreRate * 100).toFixed(2)}%`);
      
      // 特殊情况：如果高优先级检测(navigator.onLine)通过，则直接判定为在线
      if (browserResult.passed) {
        // console.log('浏览器API检测通过，直接判定为在线');
        if (this.consecutiveFailures > 0) {
          this.consecutiveFailures = 0;
        }
        this.updateStatus(true);
        return;
      }
      
      // 根据得分率判断网络状态
      const isConnected = scoreRate >= this.passThreshold;
      
      if (!isConnected) {
        this.consecutiveFailures++;
        // console.log(`连续失败次数: ${this.consecutiveFailures}/${this.maxConsecutiveFailures}`);
        
        // 只有连续多次失败才判断为离线
        if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
          // console.log(`达到最大连续失败次数 ${this.maxConsecutiveFailures}，网络状态更新为离线`);
          this.updateStatus(false);
        } else {
          // console.log(`未达到最大连续失败次数，维持当前网络状态: ${this.isOnline ? '在线' : '离线'}`);
        }
      } else {
        // 检测成功时，重置连续失败计数
        if (this.consecutiveFailures > 0) {
          // console.log('网络恢复正常，重置失败计数');
          this.consecutiveFailures = 0;
        }
        // console.log('网络检测通过，状态更新为在线');
        this.updateStatus(true);
      }
    } catch (error) {
      console.error('网络检测过程出错:', error);
      // 出现未知错误时不改变当前状态
      // console.log('由于检测过程出错，维持当前网络状态不变');
    } finally {
      // console.log(`===== 网络检测完成，当前状态: ${this.isOnline ? '在线' : '离线'} =====`);
    }
  }

  /**
   * 发送心跳请求检测网络连接
   */
  private async sendPing(): Promise<boolean> {
    // console.log('开始执行心跳检测...');
    try {
      const controller = new AbortController();
      const signal = controller.signal;
      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => {
          controller.abort();
          // reject(new Error('心跳请求超时'));
        }, 5000);
      });
      
      // 主要心跳检测URL (优先使用配置的URL)
      const mainPingUrl = this.pingUrl;
      // console.log(`尝试主要心跳URL: ${mainPingUrl}`);
      
      try {
        // 使用Promise.race来处理超时
        const response = await Promise.race([
          fetch(mainPingUrl, {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache',
              'X-Requested-With': 'XMLHttpRequest'
            },
            signal
          }),
          timeoutPromise
        ]) as Response;
        
        if (response.ok) {
          // console.log(`心跳检测成功: ${mainPingUrl}`);
          return true;
        } else {
          // console.log(`心跳检测失败 - 状态码: ${response.status}, URL: ${mainPingUrl}`);
        }
      } catch (error: any) {
        // 如果主要URL失败，尝试备用URL
        // console.log(`主要心跳URL失败: ${error.message || '未知错误'}, 尝试备用URL`);
        
        // 备用心跳检测URL
        const backupUrls = [
          '/api/web/user/info/ping',
        ].filter(url => url !== mainPingUrl); // 排除已尝试的主URL
        
        // 依次尝试备用URL
        for (const url of backupUrls) {
          // console.log(`尝试备用心跳URL: ${url}`);
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 备用URL使用更短的超时
            
            const response = await fetch(url, {
              method: 'GET',
              headers: {
                'Cache-Control': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
              },
              signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (response.ok) {
              // console.log(`备用心跳URL成功: ${url}`);
              return true;
            } else {
              // console.log(`备用心跳URL失败 - 状态码: ${response.status}, URL: ${url}`);
            }
          } catch (e: any) {
            // console.log(`备用心跳URL请求出错: ${url}, 错误: ${e.message || '未知错误'}`);
          }
        }
      }
      
      // console.log('所有心跳检测URL都失败');
      return false;
    } catch (error: any) {
      // console.error('心跳检测过程中发生未处理错误:', error.message || error);
      return false;
    }
  }

  /**
   * 手动检测网络状态
   * @returns 当前网络状态
   */
  public async checkNow(): Promise<boolean> {
    await this.checkNetworkStatus();
    return this.isOnline;
  }

  /**
   * 更新网络状态并通知监听器
   * @param status 新的网络状态
   */
  private updateStatus(status: boolean): void {
    if (this.isOnline !== status) {
      this.isOnline = status;
      this.notifyListeners();
    }
  }

  /**
   * 通知所有监听器网络状态变化
   */
  private notifyListeners(): void {
    for (const listener of this.listeners) {
      listener(this.isOnline);
    }
  }

  /**
   * 添加网络状态监听器
   * @param listener 监听函数
   */
  public addListener(listener: NetworkStatusListener): void {
    this.listeners.push(listener);
  }

  /**
   * 移除网络状态监听器
   * @param listener 要移除的监听函数
   */
  public removeListener(listener: NetworkStatusListener): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 获取当前网络状态
   */
  public getStatus(): boolean {
    return this.isOnline;
  }

  /**
   * 设置WebSocket客户端
   * @param webSocket WebSocket客户端
   */
  public setWebSocket(webSocket: WebSocketClient): void {
    this.webSocket = webSocket;
  }

  /**
   * 销毁检测器并清理资源
   */
  public destroy(): void {
    this.stopChecking();
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', () => this.updateStatus(true));
      window.removeEventListener('offline', () => this.updateStatus(false));
    }
    
    this.listeners = [];
  }
}

// 创建单例实例
let networkDetectorInstance: NetworkDetector | null = null;

/**
 * 获取网络检测器实例（单例模式）
 */
export function getNetworkDetector(options?: {
  webSocket?: WebSocketClient;
  pingUrl?: string;
  pingIntervalMs?: number;
  maxConsecutiveFailures?: number;
  passThreshold?: number;
  levelWeights?: {
    high?: number;
    medium?: number;
    low?: number;
  };
}): NetworkDetector {
  if (!networkDetectorInstance) {
    networkDetectorInstance = new NetworkDetector(options);
  } else if (options?.webSocket) {
    networkDetectorInstance.setWebSocket(options.webSocket);
  }
  
  return networkDetectorInstance;
} 