// 积木命名映射关系
const blockNameMapping = {
    // 基础功能积木
    logicleapBase_showInputBox: "base_show_input",
    logicleapBase_getLastInput: "base_get_last_input",
    logicleapBase_setPresetPrompt: "base_set_preset_prompt",
    logicleapBase_getPresetPrompt: "base_get_preset_prompt",
    logicleapBase_uploadImageCommand: "base_upload_image",
    logicleapBase_getImageData: "base_get_image_data",
    logicleapBase_setBackdropFromBase64: "base_set_backdrop",
    logicleapBase_setCostumeFromBase64: "base_set_costume",
    logicleapBase_showImage: "base_show_image",
    logicleapBase_saveStageSnapshot: "base_save_stage_snapshot",
    logicleapBase_showStageSnapshot: "base_show_stage_snapshot",
    logicleapBase_getStageImageData: "base_get_stage_image_data",
    logicleapBase_getJsonValue: "base_get_json_value",
    logicleapBase_getListLength: "base_get_list_length",
    logicleapBase_calculateMathExpression: "base_calculate_math",
    logicleapBase_countNumbersInExpression: "base_count_numbers",
    logicleapBase_uploadSoundCommand: "base_upload_sound",

    // 文本功能积木
    logicleapText_setResponseFormat: "text_set_response_format",
    logicleapText_toggleContext: "text_toggle_context",
    logicleapText_toggleWebSearch: "text_toggle_websearch",
    logicleapText_toggleDeepThinking: "text_toggle_deep_thinking",
    logicleapText_clearHistory: "text_clear_history",
    logicleapText_showChatHistory: "text_show_chat_history",
    logicleapText_combinePrompts: "text_combine_prompts",
    logicleapText_selectModel: "text_select_model",
    logicleapText_startAIResponse: "text_start_ai_response",
    logicleapText_getAIResponseStatus: "text_get_ai_status",
    logicleapText_getAIResponse: "text_get_ai_response",
    logicleapText_setTemperature: "text_set_temperature",
    logicleapText_setTopP: "text_set_top_p",
    logicleapText_showThinkingPopup: "text_show_thinking_popup",

    // 图像生成积木
    logicleapImage_startGenerateImage: "image_generate",
    logicleapImage_startGenerateImageFromReference: "image_generate_from_reference",
    logicleapImage_startGenerateImageWithOptions: "image_generate_with_options",
    logicleapImage_getImageGenerationStatus: "image_get_status",
    logicleapImage_showGeneratedImageGuide: "image_show",
    logicleapImage_getGeneratedImageUrl: "image_get_url",
    logicleapImage_setImageRatio: "image_set_ratio",
    logicleapImage_setImageSeed: "image_set_seed",

    // 图像识别积木
    // 'logicleapImageRecognize_selectVisionModel': 'vision_select_model',
    logicleapImageRecognize_startVisionAnalysis: "vision_analyze",
    logicleapImageRecognize_getVisionAnalysisStatus: "vision_get_status",
    logicleapImageRecognize_getVisionAnalysisResult: "vision_get_result",
    logicleapImageRecognize_toggleContext: "vision_toggle_context",
    logicleapImageRecognize_showChatHistory: "vision_show_history",
    logicleapImageRecognize_setTemperature: "vision_set_temperature",
    logicleapImageRecognize_setTopP: "vision_set_top_p",
    logicleapImageRecognize_setTopK: "vision_set_top_k",

    // 语音功能积木
    logicleapVoice_startMinimaxTextToSpeech: "voice_generate",
    logicleapVoice_getTextToSpeechStatus: "voice_get_status",
    logicleapVoice_getGeneratedData: "voice_get_data",
    logicleapVoice_playGeneratedSpeech: "voice_play",
    logicleapVoice_stopGeneratedSpeech: "voice_stop",
    logicleapVoice_setVoiceSpeed: "voice_set_speed",
    logicleapVoice_setVoicePitch: "voice_set_pitch",
    logicleapVoice_setVoiceEmotion: "voice_set_emotion",
    logicleapVoice_setVoiceVolume: "voice_set_volume",

    // 语音识别积木（移除录音相关的积木）
    logicleapVoiceRecognize_startVoiceRecognition: "recognize_start",
    logicleapVoiceRecognize_getSpeechRecognitionResult: "recognize_get_result",
    logicleapVoiceRecognize_getSpeechRecognitionStatus: "recognize_get_status",
    // 老作品的语音识别映射命名关系
    logicleapVoiceRecognize_startRecording: "recorder_start_record",
    logicleapVoiceRecognize_stopRecording: "recorder_stop_record",
    logicleapVoiceRecognize_getRecordingStatus: "recorder_get_record_status",
    logicleapVoiceRecognize_playLastRecording: "recorder_play_record",
    logicleapVoiceRecognize_getRecordPath: "recorder_get_record_data",
    logicleapVoiceRecognize_uploadAudioFile: "recorder_upload_file",
    logicleapVoiceRecognize_getUploadPath: "recorder_get_upload_data",
    logicleapVoiceRecognize_getUploadRelay: "recorder_play_upload",
    logicleapVoiceRecognize_getRecordingHistory: "recorder_get_history",
    logicleapVoiceRecognize_getRecordingCount: "recorder_get_count",
    logicleapVoiceRecognize_deleteRecordingHistory: "recorder_clear_history",
    logicleapVoiceRecognize_relayAudio: "recorder_play_audio",

    // 录音工具积木（新增）
    logicleapRecorder_startRecording: "recorder_start_record",
    logicleapRecorder_stopRecording: "recorder_stop_record",
    logicleapRecorder_getRecordingStatus: "recorder_get_record_status",
    logicleapRecorder_playLastRecording: "recorder_play_record",
    logicleapRecorder_getRecordPath: "recorder_get_record_data",
    logicleapRecorder_relayAudio: "recorder_play_audio",
    logicleapRecorder_uploadAudioFile: "recorder_upload_file",
    logicleapRecorder_getUploadPath: "recorder_get_upload_data",
    logicleapRecorder_getUploadRelay: "recorder_play_upload",
    logicleapRecorder_getRecordingHistory: "recorder_get_history",
    logicleapRecorder_getRecordingCount: "recorder_get_count",
    logicleapRecorder_deleteRecordingHistory: "recorder_clear_history",
    logicleapRecorder_addToSoundLibrary: "recorder_add_to_library",

    // 图像分割积木
    segment_startSegmentImage: "segment_start",
    segment_getSegmentStatus: "segment_get_status",
    segment_showFrontImage: "segment_show",
    segment_getSegmentedImageData: "segment_get_data",

    // 图像增强积木
    imageEnhance_startEnhanceImage: "enhance_start",
    imageEnhance_getEnhanceStatus: "enhance_get_status",
    imageEnhance_showEnhancedImage: "enhance_show",
    imageEnhance_getEnhancedImageData: "enhance_get_data",

    // 图像评分积木
    imageScore_startImageScoring: "score_start",
    imageScore_getScoreStatus: "score_get_status",
    imageScore_getScoreResult: "score_get_result",

    // 摄像头积木
    camera_takePhoto: "camera_take_photo",
    camera_toggleCamera: "camera_toggle",
    camera_setVideoTransparency: "camera_set_transparency",
    camera_showCapturedImage: "camera_show",
    camera_getCapturedImageData: "camera_get_data",
    camera_getSavedImagesCount: "camera_saved_count",
    camera_clearSavedImages: "camera_clear_saved",

    // 背包积木
    logicleapUrl_openBackpack: "url_open_backpack",
    logicleapUrl_getVoiceUrl: "logicleapUrl_getVoiceUrl",
    logicleapUrl_getImageUrl: "logicleapUrl_getImageUrl",

    // 训练相关积木
    train_createModel: "train_create_model",
    train_uploadImage: "train_upload_image",
    train_addTrainingData: "train_add_data",
    train_predictImage: "train_predict",
    train_saveModel: "train_save",
    train_loadModel: "train_load",
    train_clearModel: "train_clear",
    train_uploadMultipleImages: "train_upload_batch",
    train_addBatchTrainingData: "train_add_batch",
    train_getBatchCount: "train_get_batch_count",
    train_loadModelByNumber: "train_load_model_by_number",

    // 图像训练积木
    logicleapImageTrain_showTrainDialog: "train_show_dialog",
    logicleapImageTrain_uploadModel: "train_upload_model",
    logicleapImageTrain_importModel: "train_import_model",
    logicleapImageTrain_editModel: "train_edit_model",
    logicleapImageTrain_predictImage: "train_predict_image",
    logicleapImageTrain_closeCamera: "train_close_camera",
    logicleapImageTrain_getImageProperty: "train_get_image_property",
    logicleapImageTrain_getModelNumber: "train_get_model_number",
    logicleapImageTrain_loadModelByNumber: "train_load_model_by_number",
    logicleapImageTrain_showImageIdentifyWindow:
        "train_show_image_identify_window",
    logicleapImageTrain_openTrainerWithLabels: "train_open_trainer_with_labels_image",

    // 姿态训练积木
    logicleapPoseTrain_showTrainDialogPose: "train_show_dialog_pose",
    logicleapPoseTrain_loadModelPoseByNumber: "train_load_model_pose_by_number",
    logicleapPoseTrain_predictPose: "train_predict_pose",
    logicleapPoseTrain_getPoseProperty: "train_get_pose_property",
    logicleapPoseTrain_closeCamera: "train_close_camera_pose",
    logicleapPoseTrain_getModelNumber: "train_get_model_number_pose",
    logicleapPoseTrain_importModelPose: "train_import_model_pose",
    logicleapPoseTrain_editModelPose: "train_edit_model_pose",
    logicleapPoseTrain_showPoseIdentifyWindow:
        "train_show_pose_identify_window",
    logicleapPoseTrain_openTrainerWithLabels: "train_open_trainer_with_labels_pose",

    // 声音训练积木
    logicleapSoundTrain_showTrainDialogSound: "train_show_dialog_sound",
    logicleapSoundTrain_predictAudioData: "train_predict_wav_file",
    logicleapSoundTrain_loadModelByNumber: "train_load_model_by_number_sound",
    logicleapSoundTrain_importModel: "train_import_model_sound",
    logicleapSoundTrain_predictSound: "train_predict_sound",
    logicleapSoundTrain_getModelNumber: "train_get_model_number_sound",
    logicleapSoundTrain_showSoundIdentifyWindow:
        "train_show_sound_identify_window",
    logicleapSoundTrain_openTrainerWithLabels: "train_open_trainer_with_labels_sound",

    // 列表操作积木
    list_deleteItems: "list_delete_items",
    list_deleteAllOfItem: "list_delete_all",
    list_replaceAllOfItem: "list_replace_all",
    list_repeatList: "list_repeat",
    list_getListJoin: "list_join",
    list_timesItemAppears: "list_count_item",
    list_itemIndex: "list_item_index",
    list_listIsEmpty: "list_is_empty",
    list_itemNumExists: "list_item_exists",
    list_orderIs: "list_check_order",
    list_orderList: "list_set_order",
    list_setListToList: "list_copy",
    list_joinLists: "list_append",
    list_setListArray: "list_from_array",
    list_getListArray: "list_to_array",

    // 运动类
    motion_movesteps: "motion_movesteps",
    motion_turnright: "motion_turnright",
    motion_turnleft: "motion_turnleft",
    motion_goto: "motion_goto",
    motion_gotoxy: "motion_gotoxy",
    motion_glideto: "motion_glideto",
    motion_glidesecstoxy: "motion_glidesecstoxy",

    // 外观类
    looks_say: "looks_say",
    looks_think: "looks_think",
    looks_show: "looks_show",
    looks_hide: "looks_hide",

    // 声音类
    sound_play: "sound_play",
    sound_playuntildone: "sound_playuntildone",
    sound_stopallsounds: "sound_stopallsounds",

    // 事件类
    event_whenflagclicked: "event_whenflagclicked",
    event_whenkeypressed: "event_whenkeypressed",

    // 控制类
    control_repeat: "control_repeat",
    control_forever: "control_forever",
    control_if: "control_if",
    control_if_else: "control_if_else",

    // 运算类
    operator_add: "operator_add",
    operator_subtract: "operator_subtract",
    operator_multiply: "operator_multiply",
    operator_divide: "operator_divide",

    // 感知类
    sensing_touchingobject: "sensing_touchingobject",
    sensing_touchingcolor: "sensing_touchingcolor",
    sensing_keypressed: "sensing_keypressed",

    // 人脸识别积木
    faceOneContrastOne_startCompare: "face_compare_start",
    faceOneContrastOne_getCompareStatus: "face_compare_status",
    faceOneContrastOne_getCompareResult: "face_compare_result",
    faceOneContrastOne_setSimilarityThreshold: "face_compare_set_threshold",

    // 人脸属性识别积木
    faceRecognition_getFaceAttribute: "face_recognition_attribute",
    faceRecognition_getFaceCount: "face_recognition_count",
    faceRecognition_getRecognitionStatus: "face_recognition_status",
    faceRecognition_startFaceRecognition: "face_recognition_start",

    // 表情识别积木
    logicleapExpress_recognizeExpression: "express_recognize",
    logicleapExpress_getExpressionStatus: "express_status",
    logicleapExpress_getDetectedExpression: "express_result",
    logicleapExpress_isExpressionType: "express_is_type",

    // 声纹识别积木
    logicleapVoiceprintRecognition_createVoiceprintLibrary: "voiceprint_create_lib",
    logicleapVoiceprintRecognition_loadVoiceprintLibrary: "voiceprint_load_lib",
    logicleapVoiceprintRecognition_addVoiceprintToLibrary: "voiceprint_add",
    logicleapVoiceprintRecognition_compareVoiceprint: "voiceprint_compare",
    logicleapVoiceprintRecognition_searchVoiceprint: "voiceprint_search",
    logicleapVoiceprintRecognition_getCurrentLibrary: "voiceprint_current_lib",
    logicleapVoiceprintRecognition_getCompareResult: "voiceprint_compare_result",
    logicleapVoiceprintRecognition_getSearchResult: "voiceprint_search_result",
    logicleapVoiceprintRecognition_getVoiceprintStatus: "voiceprint_status",
    logicleapVoiceprintRecognition_getCompareStatus: "voiceprint_compare_status",
    logicleapVoiceprintRecognition_getSearchStatus: "voiceprint_search_status",
    logicleapVoiceprintRecognition_refreshVoiceprintLibraries: "voiceprint_refresh_libs",


    // 静态手势识别积木
    logicleapStaticGestureRecognition_staticGesture_start:
        "staticGesture_start",
    logicleapStaticGestureRecognition_staticGesture_status:
        "staticGesture_status",
    logicleapStaticGestureRecognition_staticGesture_result:
        "staticGesture_result",

    // esp32Audio - 完整的积木映射
    logicleapEsp32Audio_connectESP32: "esp32Audio_connectESP32",
    logicleapEsp32Audio_disconnectESP32: "esp32Audio_disconnectESP32",
    logicleapEsp32Audio_startAudioRecording: "esp32Audio_startAudioRecording",
    logicleapEsp32Audio_stopAudioRecording: "esp32Audio_stopAudioRecording",
    logicleapEsp32Audio_downloadRecording: "esp32Audio_downloadRecording",
    logicleapEsp32Audio_playSelectedAudio: "esp32Audio_playSelectedAudio",
    logicleapEsp32Audio_stopAudioPlayback: "esp32Audio_stopAudioPlayback",
    logicleapEsp32Audio_getAudioInfo: "esp32Audio_getAudioInfo",
    // logicleapEsp32Audio_playRecordedAudio: "esp32Audio_playRecordedAudio",
    // logicleapEsp32Audio_esp32ping: "esp32ping",




    logicleapVoiceprintRecognition_loadVoiceprintLibrary: "voiceprint_load_lib",

    // 物体检测积木
    objectDetection_startObjectDetection: "object_detection_start",
    objectDetection_getDetectionStatus: "object_detection_status",
    objectDetection_getObjectCount: "object_detection_count",
    objectDetection_getObjectAttribute: "object_detection_attribute",
    objectDetection_showDetectionResultImage: "object_detection_show_image",
};

// 积木友好名称映射关系（中文）
const blockDisplayNameMapping = {
    // 事件类
    event_whenflagclicked: "当绿旗被点击",
    event_whenkeypressed: "当按下键盘按键",
    event_whenthisspriteclicked: "当角色被点击",
    event_whenbackdropswitchesto: "当背景切换到",
    event_whengreaterthan: "当传感器值大于",
    event_whenbroadcastreceived: "当接收到广播",
    event_broadcast: "广播消息",
    event_broadcastandwait: "广播并等待",

    // 控制类
    control_wait: "等待秒数",
    control_repeat: "重复执行",
    control_forever: "重复执行",
    control_if: "如果那么",
    control_if_else: "如果那么否则",
    control_wait_until: "等待直到",
    control_repeat_until: "重复直到",
    control_stop: "停止",
    control_start_as_clone: "当作为克隆体启动时",
    control_create_clone_of: "克隆",
    control_delete_this_clone: "删除此克隆体",

    // 运动类
    motion_movesteps: "移动步数",
    motion_turnright: "右转度数",
    motion_turnleft: "左转度数",
    motion_goto: "移到位置",
    motion_gotoxy: "移到坐标",
    motion_glideto: "滑行到位置",
    motion_glidesecstoxy: "滑行到坐标",
    motion_pointindirection: "指向方向",
    motion_pointtowards: "指向位置",
    motion_changexby: "将x坐标增加",
    motion_setx: "将x坐标设为",
    motion_changeyby: "将y坐标增加",
    motion_sety: "将y坐标设为",
    motion_ifonedgebounce: "碰到边缘就反弹",
    motion_setrotationstyle: "设置旋转方式",
    motion_xposition: "x坐标",
    motion_yposition: "y坐标",
    motion_direction: "方向",

    // 外观类
    looks_say: "说",
    looks_sayforsecs: "说并等待",
    looks_think: "思考",
    looks_thinkforsecs: "思考并等待",
    looks_show: "显示",
    looks_hide: "隐藏",
    looks_switchcostumeto: "换装扮为",
    looks_nextcostume: "下一个装扮",
    looks_switchbackdropto: "换背景为",
    looks_nextbackdrop: "下一个背景",
    looks_changesizeby: "将大小增加",
    looks_setsizeto: "将大小设为",
    looks_changeeffectby: "将特效增加",
    looks_seteffectto: "将特效设为",
    looks_cleargraphiceffects: "清除图形特效",
    looks_size: "大小",
    looks_costumenumbername: "装扮编号/名称",
    looks_backdropnumbername: "背景编号/名称",

    // 声音类
    sound_play: "播放声音",
    sound_playuntildone: "播放声音直到结束",
    sound_stopallsounds: "停止所有声音",
    sound_changevolumeby: "将音量增加",
    sound_setvolumeto: "将音量设为",
    sound_volume: "音量",

    // 运算类
    operator_add: "加",
    operator_subtract: "减",
    operator_multiply: "乘",
    operator_divide: "除",
    operator_random: "随机取数",
    operator_lt: "小于",
    operator_equals: "等于",
    operator_gt: "大于",
    operator_and: "且",
    operator_or: "或",
    operator_not: "非",
    operator_join: "连接文本",
    operator_letter_of: "获取字符",
    operator_length: "文本长度",
    operator_contains: "文本包含",
    operator_mod: "取余数",
    operator_round: "四舍五入",
    operator_mathop: "数学运算",
    operator_status_equals: "状态判断",

    // 感知类
    sensing_touchingobject: "碰到对象",
    sensing_touchingcolor: "碰到颜色",
    sensing_coloristouchingcolor: "颜色碰到颜色",
    sensing_distanceto: "到对象的距离",
    sensing_askandwait: "询问并等待",
    sensing_answer: "回答",
    sensing_keypressed: "按下按键",
    sensing_mousedown: "鼠标按下",
    sensing_mousex: "鼠标的x坐标",
    sensing_mousey: "鼠标的y坐标",
    sensing_loudness: "响度",
    sensing_timer: "计时器",
    sensing_resettimer: "重置计时器",
    sensing_of: "获取对象的属性",
    sensing_current: "当前时间",
    sensing_dayssince2000: "2000年至今的天数",

    // AI文本类
    text_start_ai_response: "开始AI响应",
    text_get_ai_response: "获取AI响应",
    text_get_ai_status: "获取AI状态",
    text_combine_prompts: "组合提示词",
    text_clear_history: "清除历史记录",
    text_show_chat_history: "显示聊天历史",
    text_toggle_context: "切换上下文",
    text_toggle_websearch: "切换网络搜索",
    text_toggle_deep_thinking: "设置AI深度思考模式",
    text_set_temperature: "设置温度",
    text_set_top_p: "设置top_p参数",
    text_show_thinking_popup: "打开深度思考窗口",

    // AI图像类
    image_generate: "生成图像",
    image_generate_from_reference: "从参考图生成",
    image_get_status: "获取图像状态",
    image_show: "显示生成的图像",
    image_get_url: "获取图像URL",
    image_set_ratio: "设置图像比例",

    // 自定义积木
    procedures_definition: "定义积木",
    procedures_call: "调用积木",
    argument_reporter_string_number: "参数",
    procedures_prototype: "积木原型",

    // 变量类
    data_variable: "变量",
    data_setvariableto: "设置变量",
    data_changevariableby: "改变变量",
    data_showvariable: "显示变量",
    data_hidevariable: "隐藏变量",

    // 列表类
    data_listcontents: "列表内容",
    data_addtolist: "添加到列表",
    data_deleteoflist: "删除列表项",
    data_deletealloflist: "删除全部",
    data_insertatlist: "插入列表项",
    data_replaceitemoflist: "替换列表项",
    data_itemoflist: "列表项",
    data_itemnumoflist: "项目编号",
    data_lengthoflist: "列表长度",
    data_listcontainsitem: "列表包含",
    data_showlist: "显示列表",
    data_hidelist: "隐藏列表",

    // 画笔类
    pen_clear: "清除画笔",
    pen_stamp: "图章",
    pen_penDown: "落笔",
    pen_penUp: "抬笔",
    pen_setPenColorToColor: "设置笔颜色",
    pen_changePenColorParamBy: "改变笔颜色参数",
    pen_setPenColorParamTo: "设置笔颜色参数",
    pen_changePenSizeBy: "改变笔大小",
    pen_setPenSizeTo: "设置笔大小",

    // 音乐类
    music_playDrumForBeats: "弹奏鼓声",
    music_playNoteForBeats: "弹奏音符",
    music_restForBeats: "休息节拍",
    music_setInstrument: "设置乐器",
    music_setTempo: "设置速度",
    music_changeTempo: "改变速度",
    music_getTempo: "获取速度",

    // 基础功能积木
    base_show_input: "显示输入框",
    base_get_last_input: "获取最后输入",
    base_set_preset_prompt: "设置预设提示",
    base_get_preset_prompt: "获取预设提示",
    base_upload_image: "上传图像",
    base_get_image_data: "获取图像数据",
    base_set_backdrop: "设置背景",
    base_set_costume: "设置造型",
    base_show_image: "显示图像",
    base_save_stage_snapshot: "保存舞台快照",
    base_show_stage_snapshot: "显示舞台快照",
    base_get_stage_image_data: "获取舞台图像数据",
    base_get_json_value: "获取JSON值",
    base_get_list_length: "获取列表长度",
    base_calculate_math: "计算数学表达式",
    base_count_numbers: "计算表达式中的数字",
    base_upload_sound: "上传声音",

    // 语音功能积木
    voice_generate: "生成语音",
    voice_get_status: "获取语音状态",
    voice_get_data: "获取生成的数据",
    voice_play: "播放生成的语音",
    voice_stop: "停止生成的语音",
    voice_set_speed: "设置语音速度",
    voice_set_pitch: "设置语音音调",
    voice_set_emotion: "设置语音情感",
    voice_set_volume: "设置语音音量",

    // 语音识别积木
    recognize_start: "开始语音识别",
    recognize_get_result: "获取语音识别结果",
    recognize_get_status: "获取语音识别状态",

    // 录音工具积木
    recorder_start_record: "开始录音",
    recorder_stop_record: "停止录音",
    recorder_get_record_status: "获取录音状态",
    recorder_play_record: "播放最后录音",
    recorder_get_record_data: "获取录音路径",
    recorder_play_audio: "播放音频",
    recorder_upload_file: "上传音频文件",
    recorder_get_upload_data: "获取上传路径",
    recorder_play_upload: "播放上传的内容",
    recorder_get_history: "获取录音历史",
    recorder_get_count: "获取录音数量",
    recorder_clear_history: "清除录音历史",
    recorder_add_to_library: "添加到声音库",

    // 图像分割积木
    segment_start: "开始分割图像",
    segment_get_status: "获取分割状态",
    segment_show: "显示前景图像",
    segment_get_data: "获取分割后的图像数据",

    // 图像增强积木
    enhance_start: "开始增强图像",
    enhance_get_status: "获取增强状态",
    enhance_show: "显示增强后的图像",
    enhance_get_data: "获取增强后的图像数据",

    // 图像评分积木
    score_start: "开始图像评分",
    score_get_status: "获取评分状态",
    score_get_result: "获取评分结果",

    // 摄像头积木
    camera_take_photo: "拍照",
    camera_toggle: "切换摄像头",
    camera_set_transparency: "设置视频透明度",
    camera_show: "显示拍摄的图像",
    camera_get_data: "获取拍摄的图像数据",
    camera_saved_count: "获取已保存图像数量",
    camera_clear_saved: "清除已保存图像",

    // 声纹识别积木
    voiceprint_create_lib: "新建声纹特征库",
    voiceprint_load_lib: "加载声纹特征库",
    voiceprint_add: "添加声纹特征",
    voiceprint_compare: "声纹对比",
    voiceprint_search: "声纹检索",
    voiceprint_current_lib: "当前声纹库",
    voiceprint_compare_result: "对比结果",
    voiceprint_search_result: "检索结果",
    voiceprint_status: "声纹识别状态",
    voiceprint_compare_status: "声纹对比状态",
    voiceprint_search_status: "声纹检索状态",
    voiceprint_refresh_libs: "刷新声纹特征库列表",

    // 背包积木
    url_open_backpack: "打开背包",
    logicleapUrl_getVoiceUrl: "获取语音URL",
    logicleapUrl_getImageUrl: "获取图像URL",

    // 图像训练积木
    train_create_model: "创建模型",
    train_upload_image: "上传训练图像",
    train_add_data: "添加训练数据",
    train_predict: "预测图像",
    train_save: "保存模型",
    train_load: "加载模型",
    train_clear: "清除模型",
    train_upload_batch: "批量上传图像",
    train_add_batch: "批量添加训练数据",
    train_get_batch_count: "获取批次数量",
    train_load_model_by_number: "通过编号加载模型",
    train_show_dialog: "显示训练对话框",
    train_upload_model: "上传模型",
    train_import_model: "导入模型",
    train_edit_model: "编辑模型",
    train_predict_image: "预测图像",
    train_close_camera: "关闭摄像头",
    train_get_image_property: "获取图像属性",
    train_get_model_number: "获取模型编号",
    train_show_image_identify_window: "显示图像识别窗口",
    train_open_trainer_with_labels_image: "打开训练器并设置标签",

    // 姿态训练积木
    train_show_dialog_pose: "显示姿态训练对话框",
    train_load_model_pose_by_number: "通过编号加载姿态模型",
    train_predict_pose: "预测姿态",
    train_get_pose_property: "获取姿态属性",
    train_close_camera_pose: "关闭姿态摄像头",
    train_get_model_number_pose: "获取姿态模型编号",
    train_import_model_pose: "导入姿态模型",
    train_edit_model_pose: "编辑姿态模型",
    train_show_pose_identify_window: "显示姿态识别窗口",
    train_open_trainer_with_labels: "打开训练器并设置标签",

    // 声音训练积木
    train_show_dialog_sound: "显示声音训练对话框",
    train_predict_wav_file: "预测音频数据",
    train_load_model_by_number_sound: "通过编号加载声音模型",
    train_import_model_sound: "导入声音模型",
    train_predict_sound: "预测声音",
    train_get_model_number_sound: "获取声音模型编号",
    train_show_sound_identify_window: "显示声音识别窗口",
    train_open_trainer_with_labels_sound: "打开训练器并设置标签",

    // 列表操作积木
    list_delete_items: "删除列表中的项目",
    list_replace_all: "替换列表中所有项",
    list_repeat: "重复列表",
    list_join: "列表连接",
    list_count_item: "项目出现次数",
    list_item_index: "项目索引",
    list_is_empty: "列表为空",
    list_item_exists: "项目存在",
    list_check_order: "检查顺序",
    list_set_order: "设置顺序",
    list_copy: "复制列表",
    list_append: "合并列表",
    list_from_array: "从数组创建列表",
    list_to_array: "列表转为数组",

    // 人脸识别积木
    face_compare_start: "开始人脸对比",
    face_compare_status: "获取对比状态",
    face_compare_result: "获取对比结果",
    face_compare_set_threshold: "设置相似度阈值",
    face_recognition_attribute: "获取人脸属性",
    face_recognition_count: "获取人脸数量",
    face_recognition_status: "获取识别状态",
    face_recognition_start: "开始人脸识别",

    // 表情识别积木
    express_recognize: "识别表情",
    express_status: "获取表情状态",
    express_result: "获取检测到的表情",
    express_is_type: "是否为表情类型",

    // 图像识别积木
    vision_analyze: "开始视觉分析",
    vision_get_status: "获取视觉分析状态",
    vision_get_result: "获取视觉分析结果",
    vision_toggle_context: "切换视觉上下文",
    vision_show_history: "显示视觉历史",
    vision_set_temperature: "设置视觉温度",
    vision_set_top_p: "设置视觉top_p",
    vision_set_top_k: "设置视觉top_k",

    // 静态手势识别积木
    staticGesture_start: "开始手势识别",
    staticGesture_status: "手势识别状态",
    staticGesture_result: "手势识别结果",
    // esp32Audio
    esp32Audio_connectESP32: "连接ESP32设备",
    esp32Audio_disconnectESP32: "断开连接ESP32设备",
    esp32Audio_startAudioRecording: "开始录制音频",
    esp32Audio_stopAudioRecording: "停止录制音频",
    esp32Audio_playSelectedAudio: "播放录音",
    esp32Audio_stopAudioPlayback: "停止播放",
    esp32Audio_downloadRecording: "下载录音",
    esp32Audio_getAudioInfo: "录音数据",
    esp32Audio_playRecordedAudio: "播放已录制的音频",
    esp32ping: "手动ping重置状态",


    // 映射blockName到中文
    image_generate_with_options: "使用选项生成图像",

    // 物体检测积木
    object_detection_start: '开始物体检测',
    object_detection_status: '获取物体检测状态',
    object_detection_count: '获取检测到的物体数量',
    object_detection_attribute: '检测到的物体',
    object_detection_show_image: '查看物体检测结果图像',
};

// 积木类别映射关系
const blockCategoryMapping = {
    // 根据积木ID前缀确定类别
    motion_: "运动",
    looks_: "外观",
    sound_: "声音",
    event_: "事件",
    control_: "控制",
    sensing_: "感知",
    operator_: "运算",
    data_: "数据",
    procedures_: "自定义积木",
    music_: "音乐",
    pen_: "画笔",
    Text_: "文本AI",
    Image_: "图像AI",
    Voice_: "语音AI",
    Train_: "AI训练",
    logicleapText_: "文本AI",
    logicleapImage_: "图像AI",
    logicleapVoice_: "语音AI",
    logicleapImageTrain_: "AI训练",
    logicleapPoseTrain_: "AI训练",
    logicleapSoundTrain_: "AI训练",
    logicleapBase_: "基础功能",
    logicleapRecorder_: "录音工具",
    logicleapUrl_: "背包",
    segment_: "图像分割",
    imageEnhance_: "图像增强",
    imageScore_: "图像评分",
    camera_: "摄像头",
    list_: "列表操作",
    faceRecognition_: "人脸识别",
    faceOneContrastOne_: "人脸对比",
    logicleapExpress_: "表情识别",
    logicleapImageRecognize_: "图像识别",
    train_: "AI训练",
    objectDetection_: '物体检测',
    logicleapEsp32Audio_: "ESP32音频",
    esp32ping: "ESP32音频",
};

// 类别颜色映射
const categoryColorMapping = {
    运动: "#4C97FF",
    外观: "#9966FF",
    声音: "#CF63CF",
    事件: "#FFBF00",
    控制: "#FFAB19",
    感知: "#5CB1D6",
    运算: "#59C059",
    数据: "#FF8C1A",
    自定义积木: "#FF6680",
    音乐: "#BB42C3",
    画笔: "#0FBD8C",
    文本AI: "#42A5F5",
    图像AI: "#26A69A",
    语音AI: "#EC407A",
    AI训练: "#8D6E63",
    基础功能: "#607D8B",
    录音工具: "#E91E63",
    背包: "#7986CB",
    图像分割: "#00BCD4",
    图像增强: "#009688",
    图像评分: "#4CAF50",
    摄像头: "#795548",
    列表操作: "#FF9800",
    人脸识别: "#9C27B0",
    人脸对比: "#673AB7",
    表情识别: "#3F51B5",
    图像识别: "#2196F3",
    物体检测: '#5B92E5',
    ESP32音频: "#FF6B35",
    其他: "#777777",
};

// 获取积木的类别
const getBlockCategory = (blockType) => {
    // 查找匹配的前缀
    for (const prefix in blockCategoryMapping) {
        if (blockType.includes(prefix)) {
            return blockCategoryMapping[prefix];
        }
    }
    return "其他";
};

// 获取类别的颜色
const getCategoryColor = (category) => {
    return categoryColorMapping[category] || categoryColorMapping["其他"];
};

module.exports = {
    blockNameMapping,
    blockDisplayNameMapping,
    blockCategoryMapping,
    categoryColorMapping,
    getBlockCategory,
    getCategoryColor,
};
