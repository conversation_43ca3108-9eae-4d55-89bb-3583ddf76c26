import React, { useState, useEffect, useRef, useCallback ,useImperativeHandle} from 'react';
import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
const tf = require('@tensorflow/tfjs');

// 添加十个固定的颜色
const classColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青绿色
    '#FFD166', // 黄色
    '#6A0572', // 紫色
    '#1A936F', // 绿色
    '#3D82AB', // 蓝色
    '#F77F00', // 橙色
    '#9D70CB', // 淡紫色
    '#5D576B', // 深紫色
    '#E63946'  // 深红色
];


// 加载speech-commands工具
const loadSpeechCommands = async () => {
  try {
    // 先检查是否已经加载
    if (window.speechCommands) {
      return window.speechCommands;
    }

    // 如果没有加载，先加载 TensorFlow.js
    if (!window.tf) {
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/static/utils/sound_train/tf.min.js';
        script.crossOrigin = 'anonymous';
        
        script.onload = () => {
          resolve();
        };
        
        script.onerror = (error) => {
          console.error('TensorFlow.js 加载失败:', error);
          reject(error);
        };
        
        document.head.appendChild(script);
      });
    }

    // 然后加载 speech-commands
    await new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/static/utils/sound_train/speech-commands.min.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
        resolve();
      };
      
      script.onerror = (error) => {
        console.error('speech-commands 加载失败:', error);
        reject(error);
      };
      
      document.head.appendChild(script);
    });

    if (window.speechCommands) {
      return window.speechCommands;
    } else {
      throw new Error('语音命令模型加载后未找到 window.speechCommands 对象');
    }
    
  } catch (error) {
    console.error('加载语音命令模型时发生错误:', error);
    throw error;
  }
};

const SoundIdentifyWindow = ({
    //传入预览的属性或者方法
    //预览属性
    model,
    classLabels


}) => {
    //状态定义
    const [isListening, setIsListening] = useState(false);
    const [prediction, setPrediction] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [overlapFactor, setOverlapFactor] = useState(0.5); // 默认重叠系数
    const [predicting, setPredicting] = useState(false);
    const [confidences, setConfidences] = useState([]);
    const [audioData, setAudioData] = useState(null);
    
    //引用
    const recognizerRef = useRef(null);
    const timeoutRef = useRef(null);
    const timeFreqCanvasRef = useRef(null);
    const animationFrameId = useRef(null);
    const timeFreqDataRef = useRef([]);
    const animationCanvasRef = useRef(null);
    const animationFrameIdRef = useRef(null);
    const flashTimeoutRef = useRef(null);
    const linePositionsRef = useRef([]);
    
    //初始化语音识别器
    useEffect(() => {
        const initRecognizer = async () => {
            try {
                if (!recognizerRef.current) {
                    setIsLoading(true);
                    const speechCommands = await loadSpeechCommands();
                    const modelURL = `${window.location.origin}/static/utils/sound_train/model.json`;
                    const metadataURL = `${window.location.origin}/static/utils/sound_train/metadata.json`;
                    recognizerRef.current = await speechCommands.create(
                        'BROWSER_FFT',
                        null,
                        modelURL,
                        metadataURL
                    );
                    await recognizerRef.current.ensureModelLoaded();
                    setIsLoading(false);
                    
                    // 创建音频分析器，为时频图提供数据源
                    createAudioAnalyser();
                    
                    // 语音识别器初始化完成后，检查模型是否有效并自动开始监听
                    if (model) {
                        setPredicting(true);
                        // 使用setTimeout确保状态更新后再启动监听
                        setTimeout(() => {
                            startListening();
                        }, 100);
                    }
                }
            } catch (error) {
                console.error('初始化语音识别器失败:', error);
                setError('初始化语音识别器失败: ' + error.message);
                setIsLoading(false);
            }
        };
        
        initRecognizer();
        
        // 组件卸载时清理资源 - 使用异步函数确保资源清理顺序正确
        return () => {
            // 使用异步函数包装资源清理过程
            const cleanup = async () => {
                // 先停止监听
                stopListening();
                
                // 清理音频分析器
                if (window.activeAnalyser) {
                    try {
                        delete window.activeAnalyser;
                    } catch (err) {
                        console.warn('删除音频分析器时出错:', err);
                    }
                }
                
                // 释放语音识别器资源
                if (recognizerRef.current) {
                    try {
                        // 等待一小段时间确保停止监听操作完成
                        await new Promise(resolve => setTimeout(resolve, 100));
                        
                        // 将引用置为null
                        recognizerRef.current = null;
                    } catch (err) {
                        console.warn('清理语音识别器时出错:', err);
                    }
                }
            };
            
            // 执行清理
            cleanup();
        };
    }, [model]); // 添加model作为依赖，当model变化时重新初始化
    
    //检查模型是否有效
    useEffect(() => {
        if (!model) {
            setError('请先加载模型，然后重新打开此窗口');
        } else {
            setError(null);
            // 仅设置状态，不启动监听（监听将在recognizer初始化完成后启动）
            setPredicting(true);
        }
    }, [model]);
    
    //数据归一化函数
    const normalize = (x) => {
        const mean = -100;
        const std = 10;
        return Array.from(x).map(val => (val - mean) / std);
    };
    
    //开始/停止监听
    const toggleListening = () => {
        if (isListening) {
            stopListening();
        } else {
            startListening();
        }
    };
    
    //开始监听
    const startListening = async () => {
        if (!model) {
            setError('请先加载模型');
            return;
        }
        
        if (!recognizerRef.current) {
            setError('语音识别器未初始化');
            return;
        }
        
        try {
            // 如果已经在监听，先安全地停止
            try {
                if (recognizerRef.current.isListening && recognizerRef.current.isListening()) {
                    await recognizerRef.current.stopListening();
                    // 短暂延迟以确保资源已正确释放
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            } catch (stopError) {
                console.warn('尝试停止现有监听时出错:', stopError);
                // 继续执行，因为我们希望重新开始监听
            }
            
            setIsListening(true);
            setPredicting(true);
            setError(null);
            
            // 移除超时限制 - 让监听持续进行
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
            }
            
            //开始监听 - 使用try-catch包装
            await recognizerRef.current.listen(({spectrogram: {frameSize, data}}) => {
                try {
                    //准备数据
                    const NUM_FRAMES = 3;
                    const vals = normalize(data.subarray(-frameSize * NUM_FRAMES));
                    
                    //转换为张量并进行预测
                    const input = tf.tensor(vals).reshape([1, NUM_FRAMES, 232, 1]);
                    
                    //使用传入的模型和类别标签
                    if (!model) {
                        throw new Error('模型未加载');
                    }
                    
                    //更新音频数据用于可视化
                    setAudioData(Array.from(data.slice(-256)));
                    
                    //使用tidy确保预测过程中创建的中间张量被正确释放
                    const result = tf.tidy(() => {
                        const probs = model.predict(input);
                        const probValues = probs.dataSync();
                        
                        //获取所有类别的预测结果 - 保持原始顺序
                        return (classLabels || []).map((label, index) => ({
                            label,
                            confidence: probValues[index],
                            index // 保存原始索引以保持顺序
                        }));
                    });
                    
                    //更新预测结果
                    setPrediction(result);
                    
                    //更新置信度，用于进度条显示 - 保持原始顺序
                    setConfidences(result.map((item, index) => ({
                        id: index,
                        name: item.label,
                        confidence: item.confidence,
                        originalIndex: item.index // 保存原始索引
                    })));
                    
                    //清理输入张量
                    input.dispose();
                    
                } catch (error) {
                    console.error('处理音频数据时出错:', error);
                    setError('处理音频数据时出错: ' + error.message);
                    // 出错不停止监听，只记录错误
                }
            }, {
                overlapFactor: overlapFactor,
                includeSpectrogram: true,
                probabilityThreshold: 0.75,
                invokeCallbackOnNoiseAndUnknown: true
            }).catch(err => {
                console.error('启动监听失败:', err);
                setError('启动监听失败: ' + err.message);
                setIsListening(false);
                setPredicting(false);
            });
            
        } catch (error) {
            console.error('开始监听失败:', error);
            setError('开始监听失败: ' + error.message);
            setIsListening(false);
            setPredicting(false);
        }
    };
    
    //停止监听
    const stopListening = () => {
        // 先清除超时
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        
        // 停止麦克风输入流
        if (window.activeStream) {
            try {
                const tracks = window.activeStream.getTracks();
                tracks.forEach(track => track.stop());
                window.activeStream = null;
            } catch (err) {
                console.warn('停止麦克风输入流出错:', err);
            }
        }
        
        // 最后停止语音识别器
        if (recognizerRef.current) {
            try {
                // 先检查是否正在监听，再尝试停止
                if (recognizerRef.current.isListening && recognizerRef.current.isListening()) {
                    recognizerRef.current.stopListening()
                        .catch(error => {
                            // 即使出错也继续执行，不要阻止组件卸载
                            console.warn('停止监听时出错，但忽略这个错误:', error);
                        });
                }
            } catch (error) {
                console.warn('检查监听状态时出错:', error);
            }
        }
        
        // 更新状态
        setIsListening(false);
        setPredicting(false);
    };
    
    //渲染预览面板
    const renderPreviewPanel = () => {
        return (
            <div style={trainMainStyle.panel}>
                <div style={trainMainStyle.panelContent}>
                    {renderPreviewContainer()}
                    {renderProgressContainer()}
                </div>
            </div>
        )
        }
    //渲染预览面板具体内容,每个模块预览内容不一样的点，都存储到这里面
    const renderPreviewContainer = () => {
        const timeFreqCanvasRef = useRef(null);
        const animationFrameId = useRef(null);
        const timeFreqDataRef = useRef([]);
        const animationCanvasRef = useRef(null); // 新增动画区域的canvas引用
        const animationFrameIdRef = useRef(null); // 新增动画的动画帧ID
        const flashTimeoutRef = useRef(null); // 新增闪烁效果的timeout引用
        const linePositionsRef = useRef([]); // 用于存储竖线位置
        const maxTimeFreqRows = 64; // 时频图的行数（频率维度）
        const maxTimeFreqCols = 100; // 时频图的列数（时间维度）
        const [isFlashing, setIsFlashing] = useState(false); // 添加闪烁状态
        
        // 初始化时频数据数组
        useEffect(() => {
        // 确保只初始化一次
        if (timeFreqDataRef.current.length === 0) {
            timeFreqDataRef.current = Array(maxTimeFreqRows).fill().map(() => 
            new Uint8Array(maxTimeFreqCols).fill(0)
            );
        }
        
        // 组件卸载时清理资源
        return () => {
            if (animationFrameId.current) {
            cancelAnimationFrame(animationFrameId.current);
            animationFrameId.current = null;
            }
        };
        }, []);
        
        // 初始化竖线位置
        useEffect(() => {
          // 根据重叠系数计算竖线数量和间距
          // 使用更少的线条，并进一步拉大间距
          const baseLineCount = Math.max(2, Math.round(4 + 8 * overlapFactor)); // 先计算基础线条数
          const lineCount = Math.ceil(baseLineCount / 2); // 减少一半的线条数
          const positions = [];
          
          // 创建均匀分布的位置，每隔一个位置放置一条线
          const totalSpacing = 100; // 总空间
          const spacing = totalSpacing / lineCount; // 调整后的间距
          
          for (let i = 0; i < lineCount; i++) {
            positions.push({
              x: i * spacing, // 增大间距
              height: 100, // 固定高度为100%
              speed: 0.28, // 线条移动速度
              width: 2 // 固定宽度为2像素
            });
          }
          
          linePositionsRef.current = positions;
          
          // 移除清理函数，避免在重叠系数变化时停止动画
          // 清理工作将在组件卸载时通过别的useEffect处理
        }, [overlapFactor]); // 当重叠系数变化时重新计算
        
        // 时频图可视化初始化和清理
        useEffect(() => {
        console.log("预览状态:", predicting, "Canvas引用:", !!timeFreqCanvasRef.current);
        
        if (!timeFreqCanvasRef.current) return;
        
        // 开始时频图动画
        startRealTimeFreqAnimation();
        
        return () => {
            // 组件卸载时清理资源
            if (animationFrameId.current) {
            cancelAnimationFrame(animationFrameId.current);
            animationFrameId.current = null;
            }
        };
        }, [timeFreqCanvasRef.current]);
        
        // 启动竖线动画
        useEffect(() => {
          if (!animationCanvasRef.current) return;
          
          const canvas = animationCanvasRef.current;
          const ctx = canvas.getContext('2d');
          const width = canvas.width;
          const height = canvas.height;
          
          // 计算闪烁间隔时间（毫秒）
          // 重叠系数为0时，闪烁间隔为1000毫秒（1秒）
          // 重叠系数为0.5时，闪烁间隔为500毫秒（0.5秒）
          // 重叠系数为1时，闪烁间隔为200毫秒（0.2秒）
          const flashInterval = Math.max(200, Math.round(1000 - (overlapFactor * 800)));
          let lastFlashTime = Date.now();
          
          // 添加防止重复闪烁的计时器
          let isWaitingForFlashToEnd = false;
          
          const animate = () => {
            // 清空画布
            ctx.clearRect(0, 0, width, height);
            ctx.fillStyle = 'rgba(33, 150, 243, 0.05)';
            ctx.fillRect(0, 0, width, height);
            
            // 当前时间
            const currentTime = Date.now();
            
            // 计算是否应该闪烁，只有当不在等待闪烁结束时才触发新闪烁
            if (!isWaitingForFlashToEnd && currentTime - lastFlashTime >= flashInterval) {
              setIsFlashing(true);
              lastFlashTime = currentTime;
              isWaitingForFlashToEnd = true;
              
              // 等待闪烁结束后重置标志
              const flashDuration = Math.max(30, Math.round(150 - (overlapFactor * 120)));
              setTimeout(() => {
                isWaitingForFlashToEnd = false;
              }, flashDuration + 50); // 加上一点额外延迟，确保完全重置
            }
            
            // 绘制竖线
            linePositionsRef.current.forEach(line => {
              // 移动线条
              line.x -= line.speed;
              
              // 如果线条超出了左边界，重新回到右侧
              if (line.x < 0) {
                line.x = 100; // 回到右侧边缘
              }
              
              // 绘制线条
              const xPos = width * line.x / 100;
              ctx.fillStyle = 'rgba(33, 150, 243, 0.7)'; // 蓝色半透明
              ctx.fillRect(xPos, 0, line.width, height); // 从顶部到底部的竖线
            });
            
            // 继续下一帧动画
            animationFrameIdRef.current = requestAnimationFrame(animate);
          };
          
          // 启动动画
          animate();
          
          // 组件卸载时清理
          return () => {
            if (animationFrameIdRef.current) {
              cancelAnimationFrame(animationFrameIdRef.current);
            }
          };
        }, [animationCanvasRef.current, overlapFactor]); // 添加overlapFactor作为依赖
        
        // 监听数据更新以触发闪烁 - 移除该效果，避免多次触发
        useEffect(() => {
          // 不再基于音频数据触发闪烁
          // if (predicting && audioData) {
          //   setIsFlashing(true);
          // }
        }, [audioData]);
        
        // 添加一个useEffect来自动关闭闪烁
        useEffect(() => {
          if (isFlashing) {
            // 根据重叠系数计算闪烁持续时间
            // 重叠系数0时闪烁持续时间为150毫秒，保持闪烁频率约为1秒/次
            // 重叠系数0.5时闪烁持续时间为90毫秒，使闪烁频率约为0.5秒/次
            // 重叠系数1时闪烁持续时间为30毫秒，使闪烁频率约为0.2秒/次
            const flashDuration = Math.max(30, Math.round(150 - (overlapFactor * 120)));
            
            const timer = setTimeout(() => {
              setIsFlashing(false);
            }, flashDuration);
            
            return () => clearTimeout(timer);
          }
        }, [isFlashing, overlapFactor]);
        
        // 全局清理函数，处理组件卸载时的所有资源清理
        useEffect(() => {
        return () => {
            // 清理所有动画和定时器
            if (animationFrameId.current) {
            cancelAnimationFrame(animationFrameId.current);
            animationFrameId.current = null;
            }
            if (animationFrameIdRef.current) {
            cancelAnimationFrame(animationFrameIdRef.current);
            animationFrameIdRef.current = null;
            }
            if (flashTimeoutRef.current) {
            clearTimeout(flashTimeoutRef.current);
            flashTimeoutRef.current = null;
            }
            console.log('清理了所有动画和定时器资源');
        };
        }, []); // 空依赖数组，只在组件卸载时执行一次
        
        // 启动时频图可视化动画 - 从右到左滚动
        const startRealTimeFreqAnimation = () => {
          const timeFreqCanvas = timeFreqCanvasRef.current;
          
          if (!timeFreqCanvas) return;
          
          const TIME_FREQ_WIDTH = timeFreqCanvas.width;
          const TIME_FREQ_HEIGHT = timeFreqCanvas.height;
          
          const timeFreqCtx = timeFreqCanvas.getContext('2d');
          
          // 初始绘制一次，确保容器有内容
          timeFreqCtx.fillStyle = 'rgb(1,1,149)';
          timeFreqCtx.fillRect(0, 0, TIME_FREQ_WIDTH, TIME_FREQ_HEIGHT);
          
          // 上次渲染时间，用于控制帧率
          let lastRenderTime = 0;
          const frameInterval = 1000 / 25; // 每秒25帧 = 40ms一帧，与录制时相同
          
          // 渲染帧
          const renderFrame = (timestamp) => {
            // 如果组件已卸载或不再预测，停止动画
            if (!predicting || !timeFreqCanvasRef.current) {
              if (animationFrameId.current) {
                cancelAnimationFrame(animationFrameId.current);
                animationFrameId.current = null;
              }
              return;
            }
            
            // 请求下一帧
            animationFrameId.current = requestAnimationFrame(renderFrame);
            
            // 检查是否需要渲染新帧，控制帧率与录制时相同
            if (!lastRenderTime || timestamp - lastRenderTime >= frameInterval) {
              lastRenderTime = timestamp;
              
              // 获取频谱数据
              let currentSpectrumData;
              
              // 从全局分析器获取数据
              if (window.activeAnalyser) {
                try {
                  // 获取频谱数据前输出调试信息
                  console.log("从分析器获取数据:", !!window.activeAnalyser);
                  
                  // 从全局分析器获取实时频谱数据
                  const frequencyBinCount = window.activeAnalyser.frequencyBinCount;
                  const spectrumData = new Uint8Array(frequencyBinCount);
                  window.activeAnalyser.getByteFrequencyData(spectrumData);
                  currentSpectrumData = spectrumData;
                  
                  // 输出频谱数据的一些统计信息用于调试
                  const max = Math.max(...spectrumData);
                  const avg = spectrumData.reduce((sum, val) => sum + val, 0) / spectrumData.length;
                  console.log(`频谱数据: 长度=${spectrumData.length}, 最大值=${max}, 平均值=${avg.toFixed(2)}`);
                  
                  // 更新时频图数据
                  updateTimeFreqData(currentSpectrumData);
                  
                  // 绘制时频图
                  drawTimeFreqGraph(timeFreqCtx, TIME_FREQ_WIDTH, TIME_FREQ_HEIGHT);
                } catch (e) {
                  console.error('获取或绘制频谱数据时出错:', e);
                  
                  // 出错时回退到音频数据中获取数据
                  if (audioData && audioData.length > 0) {
                    console.log("回退到音频数据绘制");
                    fallbackToAudioData();
                  }
                }
              } else if (audioData && audioData.length > 0) {
                // 没有全局分析器但有音频数据时
                console.log("无法获取分析器，使用音频数据");
                fallbackToAudioData();
              } else {
                // 没有数据源，显示提示
                timeFreqCtx.fillStyle = 'rgb(1,1,149)';
                timeFreqCtx.fillRect(0, 0, TIME_FREQ_WIDTH, TIME_FREQ_HEIGHT);
                timeFreqCtx.fillStyle = 'rgba(255,255,255,0.5)';
                timeFreqCtx.font = '14px Arial';
                timeFreqCtx.textAlign = 'center';
                timeFreqCtx.fillText('等待音频数据...', TIME_FREQ_WIDTH/2, TIME_FREQ_HEIGHT/2);
              }
            }
          };

          // 使用现有音频数据的回退方案
          const fallbackToAudioData = () => {
            try {
              // 如果有音频数据但没有分析器
              if (audioData && audioData.length > 0) {
                // 模拟频谱数据 - 从最新的音频数据生成简单的频谱
                const dummySpectrumData = new Uint8Array(128);
                
                // 使用最近的audioData样本计算能量分布，限制数据量与录制时相同
                const recentData = audioData.slice(-100);
                
                // 简单地将时域数据映射到频域
                // 真实情况下应使用FFT，但这里只是做个可视化的替代
                for (let i = 0; i < Math.min(128, recentData.length); i++) {
                  dummySpectrumData[i] = Math.abs(recentData[i] * 256);
                }
                
                // 更新时频图数据
                updateTimeFreqData(dummySpectrumData);
                
                // 绘制时频图
                drawTimeFreqGraph(timeFreqCtx, TIME_FREQ_WIDTH, TIME_FREQ_HEIGHT);
              }
            } catch (e) {
              console.error('回退到音频数据绘制时出错:', e);
            }
          };
          
          // 更新时频数据 - 从右向左移动数据，在右侧添加新数据列
          // 调整为更慢的更新速度，模拟录制时的效果
          const updateTimeFreqData = (spectrumData) => {
            if (!spectrumData || spectrumData.length === 0) return;
            
            // 添加慢速更新逻辑
            const updateFrame = Math.floor(Date.now() / frameInterval) % 2;
            if (updateFrame !== 0) return; // 只在特定帧更新，降低更新频率
            
            // 对每行数据（对应不同频率）
            for (let row = 0; row < maxTimeFreqRows; row++) {
              // 将现有数据向左移动一列
              for (let col = 0; col < maxTimeFreqCols - 1; col++) {
                timeFreqDataRef.current[row][col] = timeFreqDataRef.current[row][col + 1];
              }
              
              // 在最右边添加新数据
              // 采样频谱数据以填充时频图行
              const spectrumIndex = Math.floor((row / maxTimeFreqRows) * spectrumData.length);
              if (spectrumIndex < spectrumData.length) {
                timeFreqDataRef.current[row][maxTimeFreqCols - 1] = spectrumData[spectrumIndex];
              }
            }
          };
          
          // 绘制时频图 - 从右到左滚动，实现中间对称向两边扩展的镜像效果
          const drawTimeFreqGraph = (ctx, width, height) => {
            // 清空画布并填充背景色
            ctx.fillStyle = 'rgb(1,1,149)';
            ctx.fillRect(0, 0, width, height);
            
            const colWidth = width / maxTimeFreqCols;
            const centerY = height / 2; // 中心线Y坐标
            const maxRowsHalf = maxTimeFreqRows / 2; // 一半的行数
            const rowHeight = (height / 2) / maxRowsHalf; // 每行高度
            
            // 绘制每个时频点 - 上半部分（镜像）
            for (let col = 0; col < maxTimeFreqCols; col++) {
              for (let row = 0; row < maxRowsHalf; row++) {
                // 使用相同的数据，但镜像绘制
                const actualRow = Math.floor(row * 2); // 取样数据行
                const value = timeFreqDataRef.current[actualRow][col];
                
                if (value > 0) {
                  // 设置颜色 - 根据频谱能量
                  if (value > 200) {
                    ctx.fillStyle = `rgba(255, ${255 - Math.floor((255 - value) * 0.5)}, 0, 0.9)`;
                  } else if (value > 100) {
                    ctx.fillStyle = `rgba(${value}, 255, 0, 0.8)`;
                  } else {
                    ctx.fillStyle = `rgba(0, ${value * 2}, 255, 0.7)`;
                  }
                  
                  // 上半部分 - 从中心向上绘制
                  const yPosUp = centerY - (row + 1) * rowHeight;
                  ctx.fillRect(col * colWidth, yPosUp, colWidth, rowHeight);
                  
                  // 下半部分 - 从中心向下绘制（镜像）
                  const yPosDown = centerY + row * rowHeight;
                  ctx.fillRect(col * colWidth, yPosDown, colWidth, rowHeight);
                }
              }
            }
            
            // 添加中心线
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fillRect(0, centerY - 1, width, 2);
          };
          
          // 启动动画
          renderFrame();
        };

        // 返回预览容器
        return (
            <div style={{
             display: confidences.length > 0 ? 'block' : 'none'
        }}>
            <div 
                style={{ 
                border: '1px solid #ddd',
                borderRadius: '4px',
                width: '100%',
                height: '60px', // 从80px缩短到60px
                marginTop: '10px',
                marginBottom: '10px',
                boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                overflow: 'hidden',
                position: 'relative'
            }}
            >
            {/* 时频图canvas */}
            <canvas
                ref={timeFreqCanvasRef}
                style={{
                width: '60%',
                height: '100%',
                display: 'block'
                }}
            />
            
            {/* 蓝色方框覆盖在时频图右侧 */}
            <div
                style={{
                            position: 'absolute',
                left: 'calc(60% - 20%)', // 定位在时频图内部的最右侧
                top: 0,
                width: '20%', 
                height: '100%',
                border: `2px solid ${isFlashing ? '#90CAF9' : '#2196F3'}`,
                borderLeft: `4px solid ${isFlashing ? '#90CAF9' : '#2196F3'}`,
                boxSizing: 'border-box',
                pointerEvents: 'none',
                zIndex: 10,
                backgroundColor: isFlashing ? 'rgba(33, 150, 243, 0.3)' : 'rgba(33, 150, 243, 0.05)',
                transition: 'background-color 0.1s ease, border-color 0.1s ease',
                boxShadow: isFlashing ? '0 0 8px rgba(33, 150, 243, 0.8)' : 'none'
                }}
            />
            
            {/* 右侧动画区域 */}
            <div
                style={{
                position: 'absolute',
                left: '60%',
                top: 0,
                width: '40%',
                height: '100%',
                boxSizing: 'border-box',
                backgroundColor: 'rgba(240, 245, 255, 0.95)',
                borderLeft: '1px solid #e0e0e0',
                overflow: 'hidden'
                }}
            >
                <canvas
                ref={animationCanvasRef}
                width={200}
                height={60}
                style={{
                            width: '100%',
                    height: '100%',
                    display: 'block'
                }}
                />
            </div>
        </div>
                        
            {/* 重叠系数控制 */}
        <div style={{
            display: 'flex', 
            flexDirection: 'column',
            marginBottom: '10px',
            padding: '5px'
            }}>
            <div style={{
                fontWeight: 'bold',
                marginBottom: '5px',
                fontSize: '14px'
            }}>
                重叠系数
                        </div>
                        <div style={{ 
                display: 'flex',
                alignItems: 'center',
                width: '100%'
            }}>
                <input 
                type="range" 
                min="0"
                max="0.75" 
                step="0.05" 
                value={overlapFactor} 
                onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setOverlapFactor(value);
                }} 
                style={{
                    flex: 1,
                    margin: '0 10px 0 0'
                }}
                />
                <input 
                type="number" 
                min="0" 
                max="0.75" 
                step="0.05" 
                value={overlapFactor.toFixed(2)} 
                onChange={(e) => {
                    const value = parseFloat(e.target.value);
                    setOverlapFactor(Math.min(0.75, Math.max(0, value)));
                }} 
                style={{
                    width: '60px',
                    textAlign: 'center',
                    padding: '4px',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                }}
                />
                <div style={{
                marginLeft: '10px',
                cursor: 'help',
                fontSize: '16px',
                color: '#888'
                }} title="控制模型检测频率：0表示低频率检测（约1秒1次），0.75表示高频率检测（约0.01秒1次）。值越大检测越频繁，CPU占用也越高。">
                ?
                        </div>
                    </div>
            </div>
            </div>
        );
    };
    
    //渲染类别置信度展示区域
    const renderProgressContainer = () => {
    return (
    <div style={{
        ...trainMainStyle.progressContainer,
        display: confidences.length > 0 ? 'block' : 'none'
    }}>
        <h4 style={trainMainStyle.progressHeaderText}>输出</h4>
        {/* 按原始顺序展示类别，不进行排序 */}
        {confidences.map((item,index) => (
            <div key={`conf-${item.id}`} style={{
                ...trainMainStyle.progressItem,
            }}>
                {/* 类别名称 */}
                <div style={{
                    ...trainMainStyle.progressItemName,
                    color: classColors[index % classColors.length]
                }}>
                    {item.name}
                </div>
                
                {/* 进度条容器 */}
                <div style={{
                    ...trainMainStyle.progressBarContainer,
                }}>
                    {/* 进度条 */}
                    <div style={{
                        ...trainMainStyle.progressBar,
                        width: `${Math.round(item.confidence * 100)}%`,
                        backgroundColor: classColors[index % classColors.length], // 使用循环颜色
                    }} />
                    
                    {/* 百分比文字 */}
                    <div style={{ 
                        ...trainMainStyle.progressPercentage,
                        left: `${Math.round(item.confidence * 100)}%`,  // 使用相同的百分比
                    }}>
                        {Math.round(item.confidence * 100)}%
                    </div>
                </div>
            </div>
        ))}
    </div>
    )
    }

    // 创建音频分析器，作为时频图的数据源
    const createAudioAnalyser = () => {
        // 先清理可能存在的旧分析器
        if (window.activeAnalyser) {
            delete window.activeAnalyser;
        }

        // 创建音频上下文和分析器
        try {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            const audioCtx = new AudioContext();
            
            // 创建分析器节点
            const analyser = audioCtx.createAnalyser();
            analyser.fftSize = 2048;
            analyser.smoothingTimeConstant = 0.8;
            
            // 存储分析器到全局变量，供时频图使用
            window.activeAnalyser = analyser;
            console.log("创建了新的分析器节点:", analyser.fftSize);
            
            // 创建麦克风输入
            navigator.mediaDevices.getUserMedia({ audio: true, video: false })
                .then(stream => {
                    // 创建媒体流源节点
                    const source = audioCtx.createMediaStreamSource(stream);
                    
                    // 连接源节点到分析器
                    source.connect(analyser);
                    
                    // 存储流以便后续停止
                    window.activeStream = stream;
                    console.log("麦克风输入连接到分析器");
                })
                .catch(err => {
                    console.error("获取麦克风权限失败:", err);
                    setError("无法访问麦克风: " + err.message);
                });
        } catch (err) {
            console.error("创建音频分析器失败:", err);
            setError("创建音频分析器失败: " + err.message);
        }
    };

    return (
                // 正常界面
                <>
                    
                    {/* 预览面板 */}
                    {renderPreviewPanel()}
                    
                </>
    );
}

export default SoundIdentifyWindow;