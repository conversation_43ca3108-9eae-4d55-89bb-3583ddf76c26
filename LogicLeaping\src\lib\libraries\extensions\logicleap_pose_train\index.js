const BlockType = require('@scratch-vm/extension-support/block-type');
const ArgumentType = require('@scratch-vm/extension-support/argument-type');
const {getExtensionPermissions} = require('../index.jsx');

// 导入React和ReactDOM
const React = require('react');
const ReactDOM = require('react-dom');
// 导入React组件版本的TrainDialog
const TrainDialogComponent = require('../utils/trainUtil/trainDialog/train-dialog.jsx').default;

// 导入加载动画模块
const { createLoadingAnimation, updateLoadingProgress, removeLoadingAnimation } = require('../utils/loadingAnimation');

// 导入通知组件
const notification = require('../utils/notification').default || require('../utils/notification');

// 导入按钮处理模块
const buttonHandler = require('../utils/button-handler.js');

// 导入JSON文件上传器
const createJsonFileUploader = require('../utils/jsonFileUploader');

// 移除对 API_URL 的直接依赖
// const { API_URL } = require('../../../../config/config.js');
// 导入API模块
const workApi = require('@api/work_api.ts').default;

// 导入图片资源
const IconURI = require('./pose_train.svg');
const { getImage } = require('../utils/mediaHandler');


class LogicLeapPoseTrainBlocks {
    constructor (runtime) {
        /**
         * The runtime instantiating this block package.
         * @type {Runtime}
         */
        this.runtime = runtime;

        // 初始化扩展ID
        this.extensionId = 'logicleapPoseTrain';

        // 初始化各种状态
        this.dialogContainer = null;
        this.dialogRoot = null;

        // 从预加载的权限数据中获取本扩展的权限
        this.visibleBlocks = getExtensionPermissions('logicleapPoseTrain');

        // 模型相关属性
        this.loadedModel = null;
        this.loadedModelNumber = null;
        this.modelMetadata = null;
        this.currentPrediction = null;
        this.waitingForPrediction = false;
        this.predictionTimeout = null;
        this.modelData = [];
        this.modelCount = 0;

        // 绑定方法
        this.showTrainDialogPose = this.showTrainDialogPose.bind(this);
        this.closeTrainDialogPose = this.closeTrainDialogPose.bind(this);
        this.openTrainerWithLabels = this.openTrainerWithLabels.bind(this);

        this.importModelPose = this.importModelPose.bind(this);
        this.editModelPose = this.editModelPose.bind(this);
        this.loadModelPoseByNumber = this.loadModelPoseByNumber.bind(this);
        this.predictPose = this.predictPose.bind(this);
        this.getPoseProperty = this.getPoseProperty.bind(this);
        this.closeCamera = this.closeCamera.bind(this);
        this.getModelNumber = this.getModelNumber.bind(this);
        this.getWorkId = this.getWorkId.bind(this);
        this.initializeModelData = this.initializeModelData.bind(this);
        this.showPoseIdentifyWindow = this.showPoseIdentifyWindow.bind(this);
        // 姿态检测相关
        this.detector = null;
        this.video = null;
        this.isDetecting = false;
        this.poseIdentifyRoot = null;

        window._logicleapPoseTrainInstance = this;

        // 初始化检测器
        this.initDetector();
        
        // 初始化模型数据
        this.initializeModelData();

        // 初始化按钮处理模块
        buttonHandler.initialize(this);

        // 注册"训练模型"按钮
        buttonHandler.registerButton(
            this.extensionId, 
            '训练姿态模型', 
            'showTrainDialogPose', 
            this,
            { color: '#9392A9' } // 紫色
        );

        // 注册"上传模型"按钮
        buttonHandler.registerButton(
            this.extensionId,
            '上传姿态模型',
            'importModelPose',
            this,
            { color: '#7B68EE' } // 深紫色
        );

        // 注册"编辑模型"按钮
        console.log('🐱 正在注册编辑姿态模型按钮...');
        buttonHandler.registerButton(
            this.extensionId,
            '编辑姿态模型',
            'editModelPose',
            this,
            { color: '#FF6B6B' } // 红色
        );
        console.log('🐱 编辑姿态模型按钮注册完成');
    }

    getInfo () {
        // 定义所有积木块
        const allBlocks = [
            {
                opcode: 'showTrainDialogPose',
                blockId: 'train_show_dialog_pose',
                blockType: BlockType.BUTTON,
                text: '训练姿态模型'
            },
            {
                opcode: 'importModelPose',
                blockId: 'train_import_model_pose',
                blockType: BlockType.BUTTON,
                text: '上传姿态模型'
            },
            {
                opcode: 'editModelPose',
                blockId: 'train_edit_model_pose',
                blockType: BlockType.BUTTON,
                text: '编辑姿态模型'
            },
            {
                opcode: 'showPoseIdentifyWindow',
                blockId: 'train_show_pose_identify_window',
                blockType: BlockType.COMMAND,
                text: '打开姿态识别窗口',
            },
            {
                opcode: 'openTrainerWithLabels',
                blockId: 'train_open_trainer_with_labels_pose',
                blockType: BlockType.COMMAND,
                text: '打开训练器, 标签1 [LABEL1], 标签2 [LABEL2]',
                arguments: {
                    LABEL1: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别1'
                    },
                    LABEL2: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别2'
                    }
                }
            },
            {       
                opcode: 'loadModelPoseByNumber',
                blockId: 'train_load_model_pose_by_number',
                blockType: BlockType.COMMAND,
                text: '加载编号为 [MODEL_NUMBER] 的姿态模型',
                arguments: {
                    MODEL_NUMBER: {
                        type: ArgumentType.STRING,  
                        defaultValue: '1'
                    }
                }
            },
            {
                opcode: 'predictPose',
                blockId: 'train_predict_pose',
                blockType: BlockType.REPORTER,
                text: '使用摄像头预测姿态的 [PROPERTY]',
                arguments: {
                    PROPERTY: {     
                        type: ArgumentType.STRING,
                        menu: 'poseProperties',
                        defaultValue: 'label'
                    }
                }
            },
            {
                opcode: 'getPoseProperty',
                blockId: 'train_get_pose_property',
                blockType: BlockType.REPORTER,
                text: '预测 [IMAGE] 的 [PROPERTY]',
                arguments: {
                    IMAGE: {
                        type: ArgumentType.STRING,
                        defaultValue: '图像数据'
                    },
                    PROPERTY: {     
                        type: ArgumentType.STRING,
                        menu: 'poseProperties',
                        defaultValue: 'label'
                    }
                }
            },
            
            {
                opcode: 'getModelNumber',
                blockId: 'train_get_model_number_pose',
                blockType: BlockType.REPORTER,
                text: '当前模型编号'
            },
        ];

        // 根据权限过滤积木块
        const blocks = allBlocks.filter(block => this.visibleBlocks[block.blockId] === true);

        return {
            id: 'logicleapPoseTrain',
            name: '姿态训练',
            color1: '#9392A9',
            blockIconURI: IconURI,
            menuIconURI: IconURI,
            blocks: blocks,
            menus: {
                poseProperties: {
                    acceptReporters: false,
                    items: [
                        {
                            text: '类别',
                            value: 'label'
                        },
                        {
                            text: '置信度',
                            value: 'confidence'
                        },
                        {
                            text: '类别+置信度',
                            value: 'both'
                        }
                    ]
                }
            }
        };
    }

    showTrainDialogPose () {
        // 如果对话框已经打开，先关闭它
        if (this.dialogRoot) {
            this.closeTrainDialogPose();
        }

        // 创建对话框容器
        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
        
        // 渲染React组件
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'pose',
                onClose: this.closeTrainDialogPose
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
            }
        );
        
        notification.show('已打开训练窗口', 'success');
        return ;
    }
    
    closeTrainDialogPose () {
        if (this.dialogRoot) {
            // 卸载React组件
            ReactDOM.unmountComponentAtNode(this.dialogRoot);
            // 移除容器
            if (this.dialogRoot.parentNode) {
                this.dialogRoot.parentNode.removeChild(this.dialogRoot);
            }
            // 重置引用
            this.dialogRoot = null;
            this.dialogContainer = null;
            
            // 恢复页面滚动
            document.body.style.overflow = '';
        }
    }

    openTrainerWithLabels (args) {
        const { LABEL1, LABEL2 } = args;
        // 过滤掉空的标签，并确保至少有两个标签
        const classLabels = [LABEL1, LABEL2].filter(Boolean);
        if (classLabels.length === 0) {
            classLabels.push('类别1', '类别2');
        } else if (classLabels.length === 1) {
            classLabels.push('类别2');
        }
    
        // 如果对话框已经打开，先关闭它
        if (this.dialogRoot) {
            this.closeTrainDialogPose();
        }
    
        // 创建对话框容器
        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
        
        // 渲染React组件
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'pose',
                onClose: this.closeTrainDialogPose,
                initialClassLabels: classLabels
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
            }
        );
        
        notification.show('已打开训练窗口', 'success');
        return ;
    }

    showPoseIdentifyWindow () {
        if(this.poseIdentifyRoot!=null){
            notification.show('请关闭之前打开的姿态识别窗口', 'error');
            return;
        }
        if (!this.loadedModel) {
            notification.show('请先导入或训练姿态模型', 'error');
            return;
        }

        // 如果对话框已经打开，先关闭它
        // 创建一个悬浮窗口来显示声音识别窗口
        const tempDiv = document.createElement('div');
        tempDiv.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50px;
            z-index: 9999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            overflow: hidden;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            width: 280px;
            max-width: 280px;
            min-width: 280px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            border-radius: 8px;
        `;
        
        const modalHeader = document.createElement('div');
        modalHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background:rgb(255, 255, 255);
            color: #333;
            cursor: move;
            border-bottom: 1px solid #e0e0e0;
            user-select: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        `;
        
        const modalTitle = document.createElement('h3');
        modalTitle.textContent = '识别窗口';
        modalTitle.style.cssText = `
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        `;
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'X';
        closeButton.style.cssText = `
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 4px 8px;
        `;
        closeButton.onclick = () => {
            document.body.removeChild(tempDiv);
            if (this.poseIdentifyRoot) {
                ReactDOM.unmountComponentAtNode(this.poseIdentifyRoot);
                this.poseIdentifyRoot = null;
            }
        };
        
        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeButton);
        modalContent.appendChild(modalHeader);
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            padding: 16px;
            overflow-y: auto;
        `;
        modalContent.appendChild(contentContainer);
        
        // 创建React组件挂载点
        this.poseIdentifyRoot = document.createElement('div');
        contentContainer.appendChild(this.poseIdentifyRoot);
        tempDiv.appendChild(modalContent);
        document.body.appendChild(tempDiv);
        
        // 添加拖动功能
        let isDragging = false;
        let offsetX, offsetY;
        
        modalHeader.onmousedown = (e) => {
            isDragging = true;
            offsetX = e.clientX - tempDiv.getBoundingClientRect().left;
            offsetY = e.clientY - tempDiv.getBoundingClientRect().top;
            
            // 添加鼠标样式反馈
            modalHeader.style.cursor = 'grabbing';
        };
        
        document.onmousemove = (e) => {
            if (!isDragging) return;
            
            const left = e.clientX - offsetX;
            const top = e.clientY - offsetY;
            
            // 确保窗口不会拖出视口
            const maxX = window.innerWidth - tempDiv.offsetWidth;
            const maxY = window.innerHeight - tempDiv.offsetHeight;
            
            tempDiv.style.left = `${Math.max(0, Math.min(left, maxX))}px`;
            tempDiv.style.top = `${Math.max(0, Math.min(top, maxY))}px`;
        };
        
        document.onmouseup = () => {
            isDragging = false;
            modalHeader.style.cursor = 'move';
        };
        
        try {
            // 导入并渲染SoundIdentifyWindow组件
            const PoseIdentifyWindows = require('./PoseIdentifyWindows.jsx').default;
            
            if (PoseIdentifyWindows) {
                ReactDOM.render(
                    React.createElement(PoseIdentifyWindows, {
                        loadedModel: this.loadedModel
                    }),
                    this.poseIdentifyRoot
                );
            } else {
                document.body.removeChild(tempDiv);
            }
        } catch (error) {
            console.error('渲染姿态识别窗口时出错:', error);
            document.body.removeChild(tempDiv);
        }
    }

    importModelPose() {
        //console.log('[index] 导入姿态模型');
        
        // 创建弹窗容器
        const modalContainer = document.createElement('div');
        modalContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        
        // 创建弹窗内容
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        `;
        
        // 添加标题
        const title = document.createElement('h2');
        title.textContent = '导入姿态模型';
        title.style.marginTop = '0';
        modalContent.appendChild(title);

        // 创建标签页容器
        const tabsContainer = document.createElement('div');
        tabsContainer.style.cssText = `
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 16px;
        `;
        
        // 创建本地模型标签
        const localTab = document.createElement('div');
        localTab.textContent = '本地模型';
        localTab.style.cssText = `
            padding: 8px 16px;
            cursor: pointer;
            font-weight: bold;
            color: #4766C2;
            border-bottom: 2px solid #4766C2;
        `;
        
        // 创建云端模型标签
        const cloudTab = document.createElement('div');
        cloudTab.textContent = '云端模型';
        cloudTab.style.cssText = `
            padding: 8px 16px;
            cursor: pointer;
            color: #666;
        `;
        
        tabsContainer.appendChild(localTab);
        tabsContainer.appendChild(cloudTab);
        modalContent.appendChild(tabsContainer);
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            position: relative;
            min-height: 200px;
        `;
        modalContent.appendChild(contentContainer);

        // 创建上传按钮容器（在左下角）
        const uploadContainer = document.createElement('div');
        uploadContainer.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            display: none;
        `;
        modalContent.appendChild(uploadContainer);
        
        // 创建文件上传输入
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';
        fileInput.style.display = 'none';
        fileInput.id = 'model-file-upload';
        uploadContainer.appendChild(fileInput);
        
        // 创建上传按钮
        const uploadButton = document.createElement('button');
        uploadButton.textContent = '上传模型文件';
        uploadButton.style.cssText = `
            padding: 6px 12px;
            background-color: #4C97FF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
        `;
        
        // 添加上传图标
        uploadButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" style="margin-right: 6px;" viewBox="0 0 16 16">
                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
            </svg>
            上传模型文件
        `;
        
        uploadButton.addEventListener('click', () => {
            fileInput.click();
        });
        uploadContainer.appendChild(uploadButton);
        
        // 处理文件上传
        fileInput.addEventListener('change', async (event) => {
            if (event.target.files.length === 0) return;
            
            const file = event.target.files[0];
            
            try {
                // 显示加载中
                showLoading();
                
                // 读取文件内容
                const fileContent = await readFileAsText(file);
                const modelJson = JSON.parse(fileContent);
                
                // 验证模型格式
                if (!modelJson.modelTopology || !modelJson.weightSpecs || !modelJson.weightData) {
                    throw new Error('无效的模型文件格式');
                }
                
                // 自动设置模型名称和标签，不再弹出输入框
                const modelName = file.name.replace('.json', '');
                
                // 尝试从模型中提取类别信息
                let classLabels = [];
                try {
                    // 检查是否有类别信息在modelJson中
                    if (modelJson.metadata && modelJson.metadata.classLabels) {
                        classLabels = modelJson.metadata.classLabels;
                    } else if (modelJson.userMetadata && modelJson.userMetadata.classLabels) {
                        classLabels = modelJson.userMetadata.classLabels;
                    } else if (modelJson.userMetadata && modelJson.userMetadata.labels) {
                        classLabels = modelJson.userMetadata.labels;
                    } else {
                        // 从输出层尝试推断类别数量
                        const outputLayerInfo = modelJson.modelTopology.config.layers.find(layer => layer.name === 'dense_1' || layer.name === 'output');
                        if (outputLayerInfo && outputLayerInfo.config && outputLayerInfo.config.units) {
                            // 创建默认类别标签
                            const numClasses = outputLayerInfo.config.units;
                            classLabels = Array.from({length: numClasses}, (_, i) => `类别${i+1}`);
                        } else {
                            // 默认类别
                            classLabels = ['类别1', '类别2', '类别3'];
                        }
                    }
                } catch (error) {
                    console.warn('无法从模型中提取类别信息，使用默认类别:', error);
                    classLabels = ['类别1', '类别2', '类别3'];
                }
                
                // 从localStorage获取已保存的姿态模型列表，用于确定新的模型编号
                const savedModelsStr = localStorage.getItem('poseModels');
                const savedModels = savedModelsStr ? JSON.parse(savedModelsStr) : [];
                
                // 确定新模型的编号，从1开始递增
                let newModelNumber = 1;
                if (savedModels.length > 0) {
                    // 找出当前最大的模型编号
                    const maxNumber = Math.max(...savedModels.map(model => model.number || 0));
                    newModelNumber = maxNumber + 1;
                }
                
                // 创建模型数据对象（扁平结构）
                const modelData = {
                    number: newModelNumber,
                    name: modelName,
                    classLabels: classLabels,
                    modelType: 'pose',
                    timestamp: new Date().toISOString(),
                    totalSamples: modelJson.trainingData ? Object.keys(modelJson.trainingData).reduce((sum, key) => sum + (modelJson.trainingData[key]?.length || 0), 0) : 0,
                    version: '1.0',
                    modelTopology: modelJson.modelTopology,
                    weightSpecs: modelJson.weightSpecs,
                    weightData: modelJson.weightData,
                    trainingData: modelJson.trainingData || null,
                    createdAt: new Date().toISOString(),
                    isUploaded: true
                };
                
                // 加载模型到内存，跳过加载动画
                const success = await this.loadModelByData(modelData, true);
                
                if (success) {
                    // 将模型保存到localStorage
                    savedModels.push(modelData);
                    localStorage.setItem('poseModels', JSON.stringify(savedModels));
                    
                    // 显示成功通知并关闭弹窗
                    notification.show(`模型"${modelName}"上传成功!`, 'success');
                    document.body.removeChild(modalContainer);
                } else {
                    throw new Error('模型加载失败');
                }
                
            } catch (error) {
                console.error('上传模型失败:', error);
                showError(`上传模型失败: ${error.message}`);
                
                // 清除文件输入
                fileInput.value = '';
            }
        });
        
        // 读取文件为文本的辅助函数
        const readFileAsText = (file) => {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsText(file);
            });
        };

        // 显示加载中
        const showLoading = () => {
            contentContainer.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                    <div style="text-align: center;">
                        <div style="border: 4px solid #f3f3f3; border-top: 4px solid #4766C2; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                        <p style="margin-top: 16px; color: #666;">加载中...</p>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
        };

        // 显示错误信息
        const showError = (message) => {
            contentContainer.innerHTML = `
                <div style="text-align: center; padding: 40px 20px;">
                    <p style="color: #d32f2f; margin-bottom: 16px;">加载失败</p>
                    <p style="color: #666;">${message}</p>
                </div>
            `;
        };

        // 获取API配置
        // const { API_URL } = require('../../../../config/config.js');
        
        // 从本地存储获取已保存的模型
        const loadLocalModels = () => {
            showLoading();
            
            try {
                // 获取本地缓存模型
                const savedModelsStr = localStorage.getItem('poseModels');
                let savedModels = [];
                if (savedModelsStr) {
                    savedModels = JSON.parse(savedModelsStr);
                }
                
                // 显示模型列表或无模型提示
                renderModelsList(savedModels, '本地');
                
                // 显示上传按钮
                uploadContainer.style.display = 'block';
            } catch (error) {
                console.error('加载本地模型失败:', error);
                showError('加载本地模型失败: ' + error.message);
            }
        };

        // 从服务器获取用户保存的模型
        const loadCloudModels = async () => {
            showLoading();
            
            // 隐藏上传按钮
            uploadContainer.style.display = 'none';
            
            try {
                // 获取token
                const token = localStorage.getItem('token');
                if (!token) {
                    showError('请先登录后再查看云端模型');
                    return;
                }
                
                // 从服务器获取模型列表 - 改用 workApi
                const result = await workApi.getPoseModelsList();
                
                if (!result || result.code !== 200 || !result.data) {
                    throw new Error(result.message || '获取云端模型失败');
                }
                
                // 渲染模型列表
                const cloudModels = result.data || [];
                renderModelsList(cloudModels, '云端');
                
            } catch (error) {
                console.error('加载云端模型失败:', error);
                showError('加载云端模型失败: ' + error.message);
            }
        };

        // 渲染模型列表
        const renderModelsList = (models, type) => {
            // 清空内容容器
            contentContainer.innerHTML = '';
            
            if (models.length === 0) {
                const noModels = document.createElement('p');
                noModels.textContent = `没有${type}保存的模型`;
                noModels.style.textAlign = 'center';
                noModels.style.padding = '40px 0';
                noModels.style.color = '#666';
                contentContainer.appendChild(noModels);
                return;
            }
            
            const modelsContainer = document.createElement('div');
            modelsContainer.style.cssText = `
                display: flex;
                flex-direction: column;
                gap: 12px;
            `;
            
            // 根据是本地还是云端模型，使用不同的渲染逻辑
            if (type === '本地') {
                // 本地模型渲染
                models.forEach(model => {
                    const modelItem = document.createElement('div');
                    modelItem.style.cssText = `
                        padding: 12px;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    `;
                    
                    const modelInfo = document.createElement('div');
                    
                    const modelName = document.createElement('div');
                    modelName.textContent = `${model.name} (序号: ${model.number})`;
                    modelName.style.fontWeight = 'bold';
                    modelInfo.appendChild(modelName);
                    
                    if (model.classLabels) {
                        const modelClasses = document.createElement('div');
                        modelClasses.textContent = `类别: ${model.classLabels.join(', ')}`;
                        modelClasses.style.cssText = 'font-size: 12px; color: #666;';
                        modelInfo.appendChild(modelClasses);
                    }
                    
                    const modelDate = document.createElement('div');
                    modelDate.textContent = `创建时间: ${new Date(model.createdAt).toLocaleString()}`;
                    modelDate.style.cssText = 'font-size: 12px; color: #666;';
                    modelInfo.appendChild(modelDate);
                    
                    modelItem.appendChild(modelInfo);
                    
                    const loadButton = document.createElement('button');
                    loadButton.textContent = '加载';
                    loadButton.style.cssText = `
                        padding: 6px 12px;
                        background-color: #4766C2;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    `;
                    
                    loadButton.addEventListener('click', () => {
                        // 加载选中的本地模型，跳过加载动画
                        this.loadModelByData(model, true);
                        // 关闭弹窗
                        document.body.removeChild(modalContainer);
                    });
                    
                    modelItem.appendChild(loadButton);
                    modelsContainer.appendChild(modelItem);
                });
            } else {
                // 云端模型渲染
                models.forEach(model => {
                    const modelItem = document.createElement('div');
                    modelItem.style.cssText = `
                        padding: 12px;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    `;
                    
                    const modelInfo = document.createElement('div');
                    
                    const modelName = document.createElement('div');
                    modelName.textContent = `${model.name} `;
                    modelName.style.fontWeight = 'bold';
                    modelInfo.appendChild(modelName);
                    
                    if (model.labels && Array.isArray(model.labels)) {
                        const modelClasses = document.createElement('div');
                        modelClasses.textContent = `类别: ${model.labels.join(', ')}`;
                        modelClasses.style.cssText = 'font-size: 12px; color: #666;';
                        modelInfo.appendChild(modelClasses);
                    }
                    
                    const modelDate = document.createElement('div');
                    modelDate.textContent = `创建时间: ${new Date(model.createTime).toLocaleString()}`;
                    modelDate.style.cssText = 'font-size: 12px; color: #666;';
                    modelInfo.appendChild(modelDate);
                    
                    modelItem.appendChild(modelInfo);
                    
                    const loadButton = document.createElement('button');
                    loadButton.textContent = '加载';
                    loadButton.style.cssText = `
                        padding: 6px 12px;
                        background-color: #4766C2;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    `;
                    
                    loadButton.addEventListener('click', async () => {
                        try {
                            // 显示加载中
                            loadButton.textContent = '加载中...';
                            loadButton.disabled = true;
                            
                            // 从云端获取模型数据，跳过加载动画
                            const modelData = await this.loadModelFromCloud(model.id, model.fileUrl, true);

                            //console.log('[index] 加载云端模型成功', modelData);
                            
                            // 关闭弹窗
                            document.body.removeChild(modalContainer);
                        } catch (error) {
                            console.error('加载云端模型失败:', error);
                            loadButton.textContent = '加载失败';
                            setTimeout(() => {
                                loadButton.textContent = '加载';
                                loadButton.disabled = false;
                            }, 1500);
                        }
                    });
                    
                    modelItem.appendChild(loadButton);
                    modelsContainer.appendChild(modelItem);
                });
            }
            
            contentContainer.appendChild(modelsContainer);
        };

        // 标签切换事件
        localTab.addEventListener('click', () => {
            localTab.style.fontWeight = 'bold';
            localTab.style.color = '#4766C2';
            localTab.style.borderBottom = '2px solid #4766C2';
            
            cloudTab.style.fontWeight = 'normal';
            cloudTab.style.color = '#666';
            cloudTab.style.borderBottom = 'none';
            
            loadLocalModels();
        });
        
        cloudTab.addEventListener('click', () => {
            cloudTab.style.fontWeight = 'bold';
            cloudTab.style.color = '#4766C2';
            cloudTab.style.borderBottom = '2px solid #4766C2';
            
            localTab.style.fontWeight = 'normal';
            localTab.style.color = '#666';
            localTab.style.borderBottom = 'none';
            
            loadCloudModels();
        });
        
        // 添加关闭按钮
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        `;
        
        const closeButton = document.createElement('button');
        closeButton.textContent = '关闭';
        closeButton.style.cssText = `
            padding: 8px 16px;
            background-color: #f5f5f5;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        `;
        
        closeButton.addEventListener('click', () => {
            document.body.removeChild(modalContainer);
        });
        
        buttonContainer.appendChild(closeButton);
        modalContent.appendChild(buttonContainer);
        
        // 添加到DOM
        modalContainer.appendChild(modalContent);
        document.body.appendChild(modalContainer);
        
        // 默认加载本地模型
        loadLocalModels();
        
        notification.show('已打开模型列表', 'success');
        return ;
    }

    // 从云端加载模型
    async loadModelFromCloud(modelId, fileUrl, skipAnimation = false) {
        try {
            // 创建加载动画（如果不跳过）
            let loadingAnimation = null;
            if (!skipAnimation) {
                loadingAnimation = createLoadingAnimation(`正在从云端加载姿态模型...`);
                updateLoadingProgress('获取模型数据...');
            }
            
            // 获取token
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('请先登录后再加载云端模型');
            }
            
            // 获取模型详情 - 改用 workApi
            const detailResult = await workApi.getPoseModelDetail(modelId);
            console.log('[index] 获取模型详情', detailResult);

            if (!detailResult || detailResult.code !== 200 || !detailResult.data) {
                throw new Error(detailResult.message || '获取模型详情失败');
            }
            
            const modelDetail = detailResult.data;
            console.log('[index] 模型详情', modelDetail);
            
            if (!skipAnimation) {
                updateLoadingProgress('下载模型文件...');
            }
            
            // 下载模型文件
            const modelResponse = await fetch(fileUrl);
            if (!modelResponse.ok) {
                throw new Error('下载模型文件失败: ' + modelResponse.status);
            }
            
            const modelJson = await modelResponse.json();
            if (!skipAnimation) {
                updateLoadingProgress('解析模型数据...');
            }
            
            // 从localStorage获取已保存的姿态模型列表，用于确定新的模型编号
            const savedModelsStr = localStorage.getItem('poseModels');
            const savedModels = savedModelsStr ? JSON.parse(savedModelsStr) : [];
            
            // 确定新模型的编号，从1开始递增
            let newModelNumber = 1;
            if (savedModels.length > 0) {
                // 找出当前最大的模型编号
                const maxNumber = Math.max(...savedModels.map(model => model.number || 0));
                newModelNumber = maxNumber + 1;
            }
            
            // 确保模型名称存在
            const modelName = modelDetail.data.name || `姿态模型 ${newModelNumber}`;
            
            // 确保标签是数组格式
            let classLabels = [];
            if (modelDetail.data.labels) {
                // 如果labels已经是数组，直接使用
                if (Array.isArray(modelDetail.data.labels)) {
                    classLabels = modelDetail.data.labels;
                } 
                // 如果labels是字符串，尝试解析为JSON
                else if (typeof modelDetail.data.labels === 'string') {
                    try {
                        classLabels = JSON.parse(modelDetail.data.labels);
                    } catch (e) {
                        console.warn('解析labels失败，使用空数组', e);
                        classLabels = [];
                    }
                }
            }
            
            // 如果classLabels为空或不是数组，设置默认值
            if (!Array.isArray(classLabels) || classLabels.length === 0) {
                classLabels = ['类别1', '类别2'];
            }
            
            console.log('[index] 处理后的标签', classLabels);
            
            // 创建模型数据对象（扁平结构）
            const modelData = {
                number: newModelNumber,
                name: modelName,
                classLabels: classLabels,
                modelType: modelDetail.data.modelType || 'pose',
                timestamp: new Date().toISOString(),
                totalSamples: modelJson.trainingData ? Object.keys(modelJson.trainingData).reduce((sum, key) => sum + (modelJson.trainingData[key]?.length || 0), 0) : (modelDetail.data.sampleCount || 0),
                version: modelDetail.data.modelVersion || '1.0',
                modelTopology: modelJson.modelTopology,
                weightSpecs: modelJson.weightSpecs,
                weightData: modelJson.weightData,
                trainingData: modelJson.trainingData || null,
                createdAt: modelDetail.data.createTime || new Date().toISOString()
            };
            
            console.log('[index] 创建的模型数据', modelData);
            
            if (!skipAnimation) {
                updateLoadingProgress('加载模型到内存...');
            }
            
            // 加载模型到内存，传递 skipAnimation 参数
            const success = await this.loadModelByData(modelData, skipAnimation);
            
            if (success) {
                if (!skipAnimation) {
                    updateLoadingProgress('保存模型到本地缓存...');
                }
                
                try {
                    // 检查是否已存在相同ID的模型，如果存在则更新
                    const existingModelIndex = savedModels.findIndex(m => m.cloudId === modelDetail.id);
                    
                    if (existingModelIndex >= 0) {
                        // 更新现有模型
                        savedModels[existingModelIndex] = {
                            ...modelData,
                            cloudId: modelDetail.data.id,
                            fromCloud: true
                        };
                    } else {
                        // 添加新模型
                        savedModels.push({
                            ...modelData,
                            cloudId: modelDetail.data.id,
                            fromCloud: true
                        });
                    }
                    
                    // 保存更新后的模型列表到localStorage
                    localStorage.setItem('poseModels', JSON.stringify(savedModels));
                    
                    // 如果模型列表过长，保留最新的10个模型
                    if (savedModels.length > 10) {
                        const sortedModels = savedModels.sort((a, b) => 
                            new Date(b.createdAt) - new Date(a.createdAt)
                        ).slice(0, 10);
                        
                        localStorage.setItem('poseModels', JSON.stringify(sortedModels));
                    }
                    
                    if (!skipAnimation) {
                        updateLoadingProgress('模型已保存到本地缓存');
                    }
                } catch (cacheError) {
                    console.error('保存模型到本地缓存失败:', cacheError);
                    // 失败时继续，不阻止模型使用
                }
                
                if (!skipAnimation) {
                    updateLoadingProgress('模型加载成功！');
                    setTimeout(() => {
                        removeLoadingAnimation();
                    }, 1000);
                }
                
                return modelData;
            } else {
                throw new Error('模型加载失败');
            }
        } catch (error) {
            console.error('从云端加载模型失败:', error);
            removeLoadingAnimation();
            throw error;
        }
    }

    // 添加从数据加载模型的方法
    async loadModelByData(modelData, skipAnimation = false) {
        try {
            if (!skipAnimation) {
                updateLoadingProgress('解析模型数据...');
            }
            
            // 确保TensorFlow.js已加载
            if (!window.tf) {
                console.error('TensorFlow.js尚未加载，无法加载模型');
                notification.show('系统初始化未完成，请稍后再试', 'error');
                return false;
            }
            
            // 从模型数据中恢复模型
            const modelTopology = modelData.modelTopology;
            const weightSpecs = modelData.weightSpecs;
            
            // 将数组转回 ArrayBuffer
            const weightData = new Uint8Array(modelData.weightData).buffer;
            
            if (!skipAnimation) {
                updateLoadingProgress('加载模型到内存...');
            }
            
            // 加载模型
            this.loadedModel = await window.tf.loadLayersModel(window.tf.io.fromMemory({
                modelTopology,
                weightSpecs,
                weightData
            }));
            
            if (!skipAnimation) {
                updateLoadingProgress('模型加载完成，准备初始化...');
            }
            
            this.loadedModelNumber = modelData.number;
            this.modelMetadata = modelData;
            this.loadedModel.metadata = modelData;
            
            //console.log(`成功加载模型 ${modelData.number}`);
            
            // 开始检测
            if (!skipAnimation) {
                updateLoadingProgress('初始化姿态检测...');
            }
            this.startDetection();
            
            return true;
        } catch (error) {
            console.error('加载模型失败:', error);
            if (!skipAnimation) {
                updateLoadingProgress(`加载失败: ${error.message}`);
            }
            notification.show(`加载模型失败: ${error.message}`, 'error');
            return false;
        }
    }

    loadModelPoseByNumber(args, util) {
        // 真正的阻塞实现：使用Promise和util.yield()
        if (!this._loadingPromise) {
            this._loadingPromise = this._loadModelPoseByNumberAsync(args);
        }

        // 检查Promise状态
        if (this._loadingPromise && typeof this._loadingPromise.then === 'function') {
            // Promise还在进行中，让Scratch等待
            util.yield();
            return;
        } else {
            // Promise已完成，返回结果并清理
            const result = this._loadingPromise;
            this._loadingPromise = null;
            return result;
        }
    }

    _loadModelPoseByNumberAsync(args) {
        const modelNumber = parseInt(args.MODEL_NUMBER, 10);
        console.log('🐱 开始加载编号为', modelNumber, '的姿态模型');

        // 返回一个Promise，在完成时将结果存储到this._loadingPromise
        return new Promise(async (resolve, reject) => {
            try {
                // 从localStorage获取已保存的模型列表
                const savedModelsStr = localStorage.getItem('poseModels');
                if (!savedModelsStr) {
                    notification.show('没有找到保存的模型', 'error');
                    const result = '没有找到保存的模型';
                    this._loadingPromise = result;
                    resolve(result);
                    return;
                }

                const models = JSON.parse(savedModelsStr);
                const model = models.find(m => m.number === modelNumber);

                if (!model) {
                    notification.show(`没有找到序号为 ${modelNumber} 的模型`, 'error');
                    const result = `没有找到序号为 ${modelNumber} 的模型`;
                    this._loadingPromise = result;
                    resolve(result);
                    return;
                }

                // 设置等待状态
                this.waitingForPrediction = true;

                // 清除之前的超时
                if (this.predictionTimeout) {
                    clearTimeout(this.predictionTimeout);
                }

                // 设置新的超时，10秒后如果仍然没有预测结果，则重置等待状态
                this.predictionTimeout = setTimeout(() => {
                    this.waitingForPrediction = false;
                    this.predictionTimeout = null;
                }, 10000);

                console.log('🐱 开始异步加载模型数据...');

                // 异步加载模型，跳过加载动画
                const success = await this.loadModelByData(model, true);

                if (success) {
                    // 启用摄像头并显示在舞台上
                    const videoDesc = {
                        width: {min: 640, ideal: 1280},
                        height: {min: 480, ideal: 720}
                    };
                    this.runtime.ioDevices.video.enableVideo(videoDesc);
                    this.runtime.ioDevices.video.mirror = true;

                    // 显示成功通知
                    notification.show(`已加载姿态模型 "${model.name}"（编号：${modelNumber}）`, 'success');

                    console.log('🐱 姿态模型加载完成:', model.name);
                    const result = `已加载姿态模型 "${model.name}"（编号：${modelNumber}）`;
                    this._loadingPromise = result;
                    resolve(result);
                } else {
                    notification.show('加载模型失败', 'error');
                    console.log('🐱 姿态模型加载失败');
                    const result = '加载模型失败';
                    this._loadingPromise = result;
                    resolve(result);
                }
            } catch (error) {
                console.error('🐱 加载姿态模型失败:', error);
                notification.show('加载姿态模型失败: ' + error.message, 'error');
                const result = '加载姿态模型失败: ' + error.message;
                this._loadingPromise = result;
                resolve(result);
            }
        });
    }

    predictPose (args) {
        const property = args.PROPERTY;

        // 检查模型是否已加载
        if (!this.loadedModel) {
            return '';
        }

        // 检查是否正在加载模型（防止在加载过程中预测）
        if (this._loadingPromise && typeof this._loadingPromise.then === 'function') {
            console.log('🐱 模型正在加载中，等待加载完成');
            return '';
        }

        // 启用摄像头并显示在舞台上
        try {
            // 确保摄像头已启用
            if (!this.runtime.ioDevices.video.videoReady) {
                const videoDesc = {
                    width: {min: 640, ideal: 1280},
                    height: {min: 480, ideal: 720}
                };
                this.runtime.ioDevices.video.enableVideo(videoDesc);
                this.runtime.ioDevices.video.mirror = true;
                this.runtime.ioDevices.video.setPreviewGhost(0);
            }

            // 如果还没有开始检测，则开始检测
            if (!this.isDetecting && !this._startingDetection && this.loadedModel) {
                console.log('🐱 启动姿态检测用于预测');
                this.startDetection();

                // 设置等待状态
                this.waitingForPrediction = true;

                // 清除之前的超时
                if (this.predictionTimeout) {
                    clearTimeout(this.predictionTimeout);
                }

                // 设置新的超时，3秒后如果仍然没有预测结果，则重置等待状态
                this.predictionTimeout = setTimeout(() => {
                    this.waitingForPrediction = false;
                    this.predictionTimeout = null;
                }, 3000);
            }
        } catch (error) {
            console.error('🐱 启用摄像头失败:', error);
            notification.show('启用摄像头失败', 'error');
            return '';
        }
        
        // 如果正在等待预测结果，返回等待消息
        if (this.waitingForPrediction) {
            return;
        }
        
        if (!this.currentPrediction) {
            notification.show('未检测到姿态', 'error');
            return;
        }
        
        if (property === 'label') {
            return this.currentPrediction.className || '未知';
        } else if (property === 'confidence') {
            return Math.round(this.currentPrediction.confidence) || 0;
        } else if (property === 'both') {
            const className = this.currentPrediction.className || '未知';
            const confidence = Math.round(this.currentPrediction.confidence) || 0;
            return `${className} (${confidence}%)`;
        }
        
        return '未知属性';
    }

    async getPoseProperty (args) {
        const imageInfo = args.IMAGE;
        const property = args.PROPERTY || 'label';
        
        //console.log('[index] 获取姿态属性');
        
        if (!this.loadedModel || !this.detector) {
            // notification.show('请先加载模型', 'error');
            return '';
        }
        
        try {
            // 确保 TensorFlow.js 已加载
            if (!window.tf) {
                //console.log('TensorFlow.js尚未加载，无法进行预测');
                notification.show('请等待系统初始化完成', 'error');
                return;
            }
            
            // 引用 TensorFlow.js
            const tf = window.tf;
                    // 检查是否有效的输入
            if (!imageInfo || imageInfo === '图像数据') {
                notification.show('请检查是否正确放入图片数据', 'info');
                return;
            }

            // 处理图像数据
            let imgSrc = '';
            try {
                // 使用getImage获取URL格式
                imgSrc = await getImage(imageInfo, "base64");
            } catch (error) {
                notification.show(error.message, 'error');
                return;
            }
            
            // 创建图像元素
            const img = new Image();
            
            // 设置跨域属性
            img.crossOrigin = 'anonymous';
            
            // 添加加载事件监听器 (用于调试)
            img.addEventListener('load', () => {
                //console.log('图像加载成功', img.width, 'x', img.height);
            });
            
            img.addEventListener('error', (error) => {
                console.error('图像加载出错:', error);
            });
            
            // 等待图像加载，增加超时时间到30秒
            await new Promise((resolve, reject) => {
                let timeoutId;
                
                const onLoad = () => {
                    //console.log('图像加载完成');
                    if (timeoutId) clearTimeout(timeoutId);
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    resolve();
                };
                
                const onError = (error) => {
                    console.error('图像加载失败:', error);
                    if (timeoutId) clearTimeout(timeoutId);
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    reject(new Error('图像加载失败: ' + (error ? error.message : '未知错误')));
                };
                
                // 设置超时时间为30秒
                timeoutId = setTimeout(() => {
                    console.error('图像加载超时 (30秒)');
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    reject(new Error('图像加载超时，请检查网络连接或图像URL是否有效'));
                }, 30000);
                
                img.addEventListener('load', onLoad);
                img.addEventListener('error', onError);
                
                // 设置图像源
                try {
                    img.src = imgSrc;
                    //console.log('已设置图像源，等待加载...');
                } catch (e) {
                    console.error('设置图像源时出错:', e);
                    clearTimeout(timeoutId);
                    reject(new Error('无法设置图像源: ' + e.message));
                }
            });
            
            //console.log('创建canvas并绘制图像');
            
            // 创建 canvas 并绘制图像
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            
            //console.log('开始姿态检测');
            
            // 使用 canvas 进行姿态检测
            try {
                const poses = await this.detector.estimatePoses(canvas);
                
                if (poses.length === 0) {
                    //console.log('未检测到姿态');
                    notification.show('未检测到姿态', 'error');
                    return;
                }
                
                //console.log('检测到姿态，关键点数量:', poses[0].keypoints.length);
                const pose = poses[0];
                // 处理镜像问题 - 由于设置了 this.runtime.ioDevices.video.mirror = true
                // 需要水平翻转关键点坐标
                const videoWidth = this.video.videoWidth;
                const keypointsToPredict = pose.keypoints.map(kp => ({ 
                    ...kp, 
                    x: videoWidth - kp.x 
                }));
                const poseToPredict = { ...pose, keypoints: keypointsToPredict };
                
                // 归一化姿态数据
                const normalizedPose = this.normalizePose(poseToPredict);
                if (!normalizedPose) {
                    //console.log('姿态数据不完整');
                    notification.show('姿态数据不完整', 'error');
                    return;
                }
                
                //console.log('开始使用模型预测姿态');
                
                // 预测姿态 - 使用 try-catch 包装 TensorFlow 操作
                try {
                    const input = tf.tensor2d([normalizedPose], [1, 34], 'float32');
                    let prediction;
                    let probabilities;
                    
                    try {
                        // 使用模型预测
                        prediction = this.loadedModel.predict(input);
                        probabilities = await prediction.data();
                        //console.log('预测结果概率:', [...probabilities].map(p => (p * 100).toFixed(2) + '%').join(', '));
                    } catch (tfError) {
                        console.error('TensorFlow 预测错误:', tfError);
                        // 清理资源
                        if (input) input.dispose();
                        if (prediction) prediction.dispose();
                        throw new Error('模型预测失败: ' + tfError.message);
                    }
                    
                    // 获取最高概率的类别
                    const maxIndex = probabilities.indexOf(Math.max(...probabilities));
                    const maxProbability = probabilities[maxIndex];
                    //console.log('最高概率类别索引:', maxIndex, '概率:', (maxProbability * 100).toFixed(2) + '%');
                    
                    // 设置置信度阈值
                    const confidenceThreshold = 0.6;
                    
                    let result;
                    if ( this.modelMetadata && this.modelMetadata.classLabels) {
                        const className = this.modelMetadata.classLabels[maxIndex] || '未知';
                        const confidence = maxProbability * 100;
                        //console.log('识别结果:', className, '置信度:', confidence.toFixed(2) + '%');
                        
                        if (property === 'label') {
                            result = className;
                        } else if (property === 'confidence') {
                            result = Math.round(confidence);
                        } else if (property === 'both') {
                            result = `${className} (${Math.round(confidence)}%)`;
                        } else {
                            result = className;
                        }
                    } else {
                        //console.log('置信度低于阈值或无类别标签');
                        if (property === 'confidence') {
                            result = 0;
                        } else if (property === 'both') {
                            result = `未知姿态 (${Math.round(maxProbability * 100)}%)`;
                        } else {
                            result = '未知姿态';
                        }
                    }
                    
                    // 清理张量
                    input.dispose();
                    prediction.dispose();
                    
                    //console.log('预测完成，返回结果:', result);
                    return result;
                } catch (tensorError) {
                    console.error('张量处理错误:', tensorError);
                    throw new Error('张量处理失败: ' + tensorError.message);
                }
            } catch (poseError) {
                console.error('姿态检测错误:', poseError);
                throw new Error('姿态检测失败: ' + poseError.message);
            }
            
        } catch (error) {
            console.error('预测图像失败:', error);
            notification.show(error.message, 'error');
            return;
        }
    }

    closeCamera () {
        //console.log('[index] 关闭摄像头');
        this.stopDetection();
        notification.show('已关闭摄像头', 'success');
        return ;
    }

    getModelNumber () {
        //console.log('[index] 获取模型编号');
        if (!this.loadedModelNumber) {
            notification.show('未加载模型', 'error');
            return '';
        }
        return this.loadedModelNumber.toString();
    }   

    // 获取缓存的姿态模型数据
    getCachedModelsData() {
        const result = {};
        try {
            console.log('🐱 Claude 4.0 sonnet: 开始获取姿态模型缓存数据...');

            // 优先从新的缓存系统读取（包含训练数据）
            const cacheIndex = localStorage.getItem('logicleap_pose_model_cache_index');
            if (cacheIndex) {
                console.log('🐱 找到新的姿态模型缓存索引');
                const modelCache = JSON.parse(cacheIndex);

                // 遍历所有模型索引，从 localStorage 读取每个模型的数据
                Object.keys(modelCache).forEach(modelNumber => {
                    try {
                        const modelData = localStorage.getItem(`logicleap_pose_model_data_${modelNumber}`);
                        if (modelData) {
                            result[modelNumber] = {
                                ...modelCache[modelNumber],
                                data: modelData // 这里包含完整的训练数据
                            };
                            console.log(`🐱 成功读取姿态模型 ${modelNumber} 的训练数据`);
                        }
                    } catch (err) {
                        console.error(`🐱 读取姿态模型 ${modelNumber} 数据失败:`, err);
                    }
                });
            }

            // 如果新缓存系统没有数据，回退到旧的 poseModels 系统（但这个不包含训练数据）
            if (Object.keys(result).length === 0) {
                console.log('🐱 新缓存系统无数据，尝试从旧系统读取...');
                const savedModelsStr = localStorage.getItem('poseModels');

                if (savedModelsStr) {
                    const savedModels = JSON.parse(savedModelsStr);

                    // 如果 savedModels 是数组格式
                    if (Array.isArray(savedModels)) {
                        savedModels.forEach(model => {
                            console.log(`🐱 getCachedModelsData: 处理模型 ${model.number}:`, {
                                hasTrainingData: !!model.trainingData,
                                hasMetadata: !!model.metadata,
                                classLabels: model.classLabels || (model.metadata && model.metadata.classLabels),
                                trainingDataKeys: model.trainingData ? Object.keys(model.trainingData) : [],
                                metadataTrainingDataKeys: (model.metadata && model.metadata.trainingData) ? Object.keys(model.metadata.trainingData) : []
                            });

                            if (model.number && model.modelTopology && model.weightData) {
                                // 使用扁平结构，保持与新系统一致
                                const modelData = {
                                    modelTopology: model.modelTopology,
                                    weightSpecs: model.weightSpecs,
                                    weightData: model.weightData,
                                    // 扁平结构字段
                                    classLabels: model.classLabels || (model.metadata && model.metadata.classLabels) || [],
                                    modelType: model.modelType || (model.metadata && model.metadata.modelType) || 'pose',
                                    timestamp: model.timestamp || (model.metadata && model.metadata.timestamp),
                                    totalSamples: model.totalSamples || (model.metadata && model.metadata.totalSamples) || 0,
                                    version: model.version || (model.metadata && model.metadata.version) || '1.0',
                                    trainingData: model.trainingData || (model.metadata && model.metadata.trainingData),
                                    number: model.number,
                                    name: model.name,
                                    createdAt: model.createdAt
                                };

                                result[model.number] = {
                                    name: model.name || `姿态模型${model.number}`,
                                    description: model.name || `姿态模型${model.number}`,
                                    data: modelData,
                                    type: 'pose',
                                    modelType: 'pose',
                                    number: model.number
                                };

                                console.log(`🐱 getCachedModelsData: 构建的modelData:`, {
                                    hasTrainingData: !!modelData.trainingData,
                                    classLabels: modelData.classLabels,
                                    totalSamples: modelData.totalSamples,
                                    trainingDataKeys: modelData.trainingData ? Object.keys(modelData.trainingData) : []
                                });
                            }
                        });
                    } else {
                        // 如果 savedModels 是对象格式
                        Object.keys(savedModels).forEach(modelNumber => {
                            if (savedModels[modelNumber].data) {
                                result[modelNumber] = {
                                    ...savedModels[modelNumber],
                                    data: savedModels[modelNumber].data
                                };
                                console.log(`🐱 从旧系统读取姿态模型 ${modelNumber}（无训练数据）`);
                            }
                        });
                    }
                }
            }

            console.log('🐱 姿态模型缓存数据获取完成，共找到', Object.keys(result).length, '个模型');
            return result;
        } catch (error) {
            console.error('🐱 获取姿态模型缓存数据失败:', error);
            return {};
        }
    }
    
    // 添加初始化检测器方法
    async initDetector() {
        try {
            // 确保 TensorFlow.js 和 PoseDetection 已加载
            if (!window.tf) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = '/static/utils/pose_train/tf.min.js';
                    script.crossOrigin = 'anonymous';
                    script.onload = () => resolve();
                    script.onerror = (error) => {
                        console.error('TensorFlow.js 加载失败:', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                });
            }
            
            if (!window.poseDetection) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = '/static/utils/pose_train/pose-detection.min.js';
                    script.crossOrigin = 'anonymous';
                    script.onload = () => resolve();
                    script.onerror = (error) => {
                        console.error('PoseNet 加载失败:', error);
                        reject(error);
                    };
                    document.head.appendChild(script);
                });
            }

            // 定义检测器配置
            const detectorConfig = {
                modelType: window.poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
                enableSmoothing: true
            };

            // 尝试使用默认（WebGL）后端初始化
            try {
                await window.tf.ready();
                this.detector = await window.poseDetection.createDetector(
                    window.poseDetection.SupportedModels.MoveNet,
                    {
                        modelType: window.poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
                        modelUrl: '/static/utils/pose_train/model.json', // 指向本地模型
                    }
                );
                console.log('默认姿态检测器初始化成功（使用 WebGL）。');
            } catch (webglError) {
                console.warn('使用 WebGL 后端初始化检测器失败，正在回退到 CPU。错误:', webglError);
                notification.show('图形加速兼容性不佳，正在切换到备用模式...', 'info');

                // 如果 WebGL 失败，尝试回退到 CPU 后端
                try {
                    await window.tf.setBackend('cpu');
                    console.log('TensorFlow.js 后端已成功设置为 CPU。');
                    this.detector = await window.poseDetection.createDetector(
                        window.poseDetection.SupportedModels.MoveNet,
                        {
                            modelType: window.poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
                            modelUrl: '/static/utils/pose_train/model.json',
                        }
                    );
                    console.log('姿态检测器在回退到 CPU 后初始化成功。');
                } catch (cpuError) {
                    console.error('CPU 后端回退失败:', cpuError);
                    // 如果 CPU 也失败，则抛出原始的 WebGL 错误
                    throw webglError;
                }
            }
        } catch (error) {
            console.error('初始化检测器失败:', error);
            notification.show('初始化姿态检测器失败，请刷新页面重试', 'error');
        }
    }

    // 添加加载脚本方法
    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`加载脚本失败: ${src}`));
            document.head.appendChild(script);
        });
    }

    // 添加开始检测方法
    async startDetection() {
        // 防止重复启动检测
        if (this.isDetecting || !this.loadedModel || !this.detector) {
            console.log('🐱 检测已在运行或条件不满足，跳过启动');
            return;
        }

        // 添加启动锁，防止并发启动
        if (this._startingDetection) {
            console.log('🐱 检测正在启动中，等待完成');
            return;
        }

        this._startingDetection = true;

        try {
            console.log('🐱 开始启动姿态检测...');

            // 确保TensorFlow已正确加载
            if (!window.tf) {
                console.error('TensorFlow.js尚未加载，无法开始姿态检测');
                notification.show('系统初始化未完成，请稍后再试', 'error');
                return;
            }

            // 停止之前的检测（如果有）
            if (this.isDetecting) {
                console.log('🐱 停止之前的检测');
                this.stopDetection();
                // 等待一下确保完全停止
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // 确保摄像头已启用，但不重复创建视频流
            if (!this.runtime.ioDevices.video.videoReady) {
                console.log('🐱 启用摄像头...');
                const videoDesc = {
                    width: {min: 640, ideal: 1280},
                    height: {min: 480, ideal: 720}
                };
                this.runtime.ioDevices.video.enableVideo(videoDesc);
                this.runtime.ioDevices.video.mirror = true;
                this.runtime.ioDevices.video.setPreviewGhost(0);

                // 等待摄像头准备好
                await new Promise(resolve => {
                    const checkVideo = () => {
                        if (this.runtime.ioDevices.video.videoReady) {
                            console.log('🐱 摄像头已准备就绪');
                            resolve();
                        } else {
                            setTimeout(checkVideo, 100);
                        }
                    };
                    checkVideo();
                });
            }

            // 使用Scratch的视频流，而不是创建新的
            console.log('🐱 使用Scratch视频流进行检测');
            this.video = this.runtime.ioDevices.video.provider.video;

            if (!this.video) {
                throw new Error('无法获取视频流');
            }

            console.log('🐱 开始姿态检测循环');

            // 开始检测
            this.isDetecting = true;
            this.detectPose();

            console.log('🐱 姿态检测已启动');

        } catch (error) {
            console.error('🐱 启动检测失败:', error);
            notification.show(`启动检测失败: ${error.message}`, 'error');
        } finally {
            this._startingDetection = false;
        }
    }

    // 添加停止检测方法
    stopDetection() {
        console.log('🐱 停止姿态检测');
        this.isDetecting = false;
        this._startingDetection = false;

        // 不要关闭Scratch的摄像头，只停止我们的检测循环
        // 因为其他功能可能还在使用摄像头

        // 如果我们创建了自己的视频元素，才需要清理
        if (this.video && this.video !== this.runtime.ioDevices.video.provider?.video) {
            if (this.video.srcObject) {
                const tracks = this.video.srcObject.getTracks();
                tracks.forEach(track => track.stop());
            }

            if (this.video.parentNode) {
                this.video.parentNode.removeChild(this.video);
            }
        }

        // 重置视频引用，但不销毁Scratch的视频流
        this.video = null;
        this.currentPrediction = null;

        console.log('🐱 姿态检测已停止');
    }

    // 添加检测姿态方法
    async detectPose() {
        if (!this.isDetecting || !this.video || !this.detector || !this.loadedModel) return;
        
        try {
            // 检测姿态
            const poses = await this.detector.estimatePoses(this.video);
            
            if (poses.length > 0) {
                const pose = poses[0];
                
                // 归一化姿态数据
                const normalizedPose = this.normalizePose(pose);
                
                if (normalizedPose) {
                    // 预测姿态
                    await this.predictPoseWithModel(normalizedPose);
                }
            }
        } catch (error) {
            console.error('姿态检测错误:', error);
        }
        
        // 继续检测
        if (this.isDetecting) {
            requestAnimationFrame(() => this.detectPose());
        }
    }

    // 添加归一化姿态数据方法
    normalizePose(pose) {
        if (!pose || !pose.keypoints || pose.keypoints.length === 0) return null;
        
        // 提取关键点
        const keypoints = pose.keypoints;
        
        // 检查关键点得分
        const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
        if (validKeypoints.length < 5) return null; // 至少需要5个有效关键点
        
        // 计算身体中心点
        let centerX = 0;
        let centerY = 0;
        let totalWeight = 0;
        
        validKeypoints.forEach(kp => {
            centerX += kp.x * kp.score;
            centerY += kp.y * kp.score;
            totalWeight += kp.score;
        });
        
        centerX /= totalWeight;
        centerY /= totalWeight;
        
        // 计算缩放因子
        let maxDist = 0;
        validKeypoints.forEach(kp => {
            const dist = Math.sqrt(
                Math.pow(kp.x - centerX, 2) + 
                Math.pow(kp.y - centerY, 2)
            );
            maxDist = Math.max(maxDist, dist);
        });
        
        // 防止除以零
        const scale = maxDist > 0 ? 1.0 / maxDist : 1.0;
        
        // 归一化所有关键点
        const normalizedData = [];
        
        // 确保所有17个关键点都有值
        for (let i = 0; i < 17; i++) {
            const keypoint = keypoints.find(kp => kp.name === this.getKeypointName(i));
            
            if (keypoint && keypoint.score > 0.3) {
                // 归一化坐标
                const x = (keypoint.x - centerX) * scale;
                const y = (keypoint.y - centerY) * scale;
                normalizedData.push(x, y);
            } else {
                // 对于缺失或低置信度的关键点，使用0值
                normalizedData.push(0, 0);
            }
        }
        
        return normalizedData;
    }

    // 添加获取关键点名称方法
    getKeypointName(index) {
        const keypointNames = [
            'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
            'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
        ];
        return keypointNames[index] || 'nose';
    }

    // 添加使用模型预测姿态方法
    async predictPoseWithModel(normalizedPose) {
        if (!this.loadedModel) return;
        
        try {
            // 确保TensorFlow.js已经加载
            if (!window.tf) {
                console.error('TensorFlow.js尚未加载，无法预测姿态');
                return;
            }
            
            // 使用window.tf而不是直接使用tf
            const tf = window.tf;
            
            // 转换为张量
            const input = tf.tensor2d([normalizedPose], [1, 34], 'float32');
            
            // 预测
            const prediction = this.loadedModel.predict(input);
            const probabilities = await prediction.data();
            
            // 获取最高概率的类别
            const maxIndex = probabilities.indexOf(Math.max(...probabilities));
            const maxProbability = probabilities[maxIndex];
            
            // 设置置信度阈值
            const confidenceThreshold = 0.6;
            
            if (maxProbability >= confidenceThreshold && this.modelMetadata && this.modelMetadata.classLabels) {
                this.currentPrediction = {
                    className: this.modelMetadata.classLabels[maxIndex] || '未知',
                    confidence: maxProbability * 100
                };
            } else {
                this.currentPrediction = {
                    className: '未知姿态',
                    confidence: maxProbability * 100
                };
            }
            
            // 重置等待状态
            this.waitingForPrediction = false;
            
            // 清除超时
            if (this.predictionTimeout) {
                clearTimeout(this.predictionTimeout);
                this.predictionTimeout = null;
            }
            
            // 清理张量
            input.dispose();
            prediction.dispose();
            
        } catch (error) {
            console.error('预测错误:', error);
            this.waitingForPrediction = false;
            if (this.predictionTimeout) {
                clearTimeout(this.predictionTimeout);
                this.predictionTimeout = null;
            }
        }
    }

    // 添加获取工作ID方法
    async getWorkId() {
        try {
            // 从localStorage获取workId
            const workId = localStorage.getItem('modelWorkId');
            if (workId) {
                return workId;
            } else {
                localStorage.removeItem('modelWorkId');
            }

            // 如果localStorage中没有,尝试从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('workId');
        } catch (error) {
            console.error('获取作品ID失败:', error);
            return null;
        }
    }

    // 添加初始化模型数据方法
    async initializeModelData() {
        try {
            console.log('🐱 initializeModelData: 开始初始化模型数据...');
            const workId = await this.getWorkId();
            console.log('🐱 initializeModelData: 获取到的作品ID:', workId);

            if (!workId || workId === 'null') {
                console.warn('🐱 initializeModelData: 未能获取作品ID，跳过模型数据初始化');
                
                // 即使没有作品ID，也要初始化本地缓存的模型
                try {
                    const savedModelsStr = localStorage.getItem('poseModels');
                    if (savedModelsStr) {
                        const savedModels = JSON.parse(savedModelsStr);
                        // 按创建时间排序
                        const sortedModels = savedModels.sort((a, b) => 
                            new Date(b.createdAt) - new Date(a.createdAt)
                        );
                        
                        // 如果有缓存的模型，更新模型计数
                        if (sortedModels.length > 0) {
                            this.modelCount = sortedModels.length;
                            // 不再自动加载模型
                        }
                        
                        // 保存回localStorage，确保排序后的顺序
                        localStorage.setItem('poseModels', JSON.stringify(sortedModels));
                    }
                } catch (cacheError) {
                    console.error('初始化本地缓存模型失败:', cacheError);
                }
                
                return;
            }
            
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('未登录状态，跳过从服务器获取模型');
                return;
            }
            
            
            
            // 从服务器获取当前作品的姿态模型 - 改用 workApi
            const result = await workApi.getWorkModels(workId);

            //console.log('[initializeModelData] 服务器响应:', response); // 不再需要
            
            if (result && result.code === 200 && result.data) {
                // 只获取姿态类型的模型
                // 注意：workApi.getWorkModels 返回的是 WorkModelInfo[]，需要根据 modelType 过滤
                const poseModels = result.data.filter(model =>
                    model.modelType === 'pose'
                );
                //console.log('[initializeModelData] 找到姿态模型:', poseModels);
                // 初始化模型数据
                this.modelData = poseModels;
                
                // 如果有模型数据，开始下载和处理每个模型
                if (poseModels.length > 0) {
                    //console.log(`找到${poseModels.length}个姿态模型数据`);
                    
                    // 显示加载通知
                    notification.show(`正在加载${poseModels.length}个姿态模型数据...`, 'success');
                    
                    // 获取现有的本地缓存模型
                    let savedModelsStr = localStorage.getItem('poseModels');
                    let savedModels = savedModelsStr ? JSON.parse(savedModelsStr) : [];
                    
                    // 确定新模型的起始编号
                    let nextModelNumber = 1;
                    if (savedModels.length > 0) {
                        // 找出当前最大的模型编号
                        const maxNumber = Math.max(...savedModels.map(model => model.number || 0));
                        nextModelNumber = maxNumber + 1;
                    }
                    
                    // 处理每个模型
                    for (let i = 0; i < poseModels.length; i++) {
                        const model = poseModels[i];
                        
                        try {
                            // 检查本地是否已有该云端模型的缓存
                            const existingModelIndex = savedModels.findIndex(m => m.cloudId === model.id);

                            console.log('🐱 initializeModelData: 检查模型缓存:', {
                                modelId: model.id,
                                modelName: model.name || model.description,
                                existingModelIndex,
                                hasExistingModel: existingModelIndex >= 0,
                                localCreatedAt: existingModelIndex >= 0 ? savedModels[existingModelIndex].createdAt : null,
                                serverCreatedAt: model.createTime,
                                timeMatch: existingModelIndex >= 0 ?
                                    new Date(savedModels[existingModelIndex].createdAt).getTime() === new Date(model.createTime).getTime() : false,
                                localHasTrainingData: existingModelIndex >= 0 ? !!(savedModels[existingModelIndex].trainingData) : false
                            });

                            // 如果本地已有缓存且创建时间相同，则跳过
                            if (existingModelIndex >= 0 &&
                                new Date(savedModels[existingModelIndex].createdAt).getTime() ===
                                new Date(model.createTime).getTime()) {
                                console.log(`🐱 模型 ${model.name || model.description} 已存在于本地缓存，跳过下载`);
                                continue;
                            }
                            
                            // 从URL下载模型文件 - 使用modelUrl而不是fileUrl
                            //console.log('[initializeModelData] 开始下载模型:', model.modelUrl);
                            const modelResponse = await fetch(model.modelUrl);
                            if (!modelResponse.ok) {
                                throw new Error(`下载模型失败: ${modelResponse.statusText}`);
                            }
                            
                            // 解析模型JSON
                            const modelJson = await modelResponse.json();

                            console.log('🐱 initializeModelData: 从服务器获取的原始模型JSON:', JSON.stringify({
                                modelId: model.id,
                                modelUrl: model.modelUrl,
                                hasModelJson: !!modelJson,
                                modelJsonKeys: modelJson ? Object.keys(modelJson) : [],
                                modelJsonSample: modelJson ? {
                                    modelType: modelJson.modelType,
                                    classLabels: modelJson.classLabels,
                                    trainingData: modelJson.trainingData ? 'exists' : 'missing',
                                    trainParams: modelJson.trainParams ? 'exists' : 'missing',
                                    totalSamples: modelJson.totalSamples,
                                    modelVersion: modelJson.modelVersion || modelJson.version
                                } : null
                            }, null, 2));

                            // 创建模型数据对象
                            const modelNumber = existingModelIndex >= 0 ?
                                savedModels[existingModelIndex].number : model.modelNumber || nextModelNumber++;

                            console.log('🐱 initializeModelData: 处理作品模型数据:', {
                                modelId: model.id,
                                hasModelJson: !!modelJson,
                                modelJsonKeys: modelJson ? Object.keys(modelJson) : [],
                                hasTrainingData: !!(modelJson && modelJson.trainingData),
                                hasClassLabels: !!(modelJson && modelJson.classLabels),
                                trainingDataKeys: (modelJson && modelJson.trainingData) ? Object.keys(modelJson.trainingData) : []
                            });

                            const modelData = {
                                number: modelNumber,
                                name: model.description || `姿态模型${modelNumber}`,
                                // 优先使用 modelJson 中的数据，回退到 model.labels
                                classLabels: (modelJson && modelJson.classLabels) || model.labels || [],
                                modelType: (modelJson && modelJson.modelType) || model.modelType,
                                timestamp: (modelJson && modelJson.timestamp) || new Date().toISOString(),
                                // 计算真实的样本数量
                                totalSamples: (modelJson && modelJson.trainingData) ?
                                    Object.keys(modelJson.trainingData).reduce((sum, key) =>
                                        sum + (modelJson.trainingData[key]?.length || 0), 0) : 0,
                                version: (modelJson && modelJson.modelVersion) || model.modelVersion || '1.0',
                                modelTopology: modelJson.modelTopology,
                                weightSpecs: modelJson.weightSpecs,
                                weightData: modelJson.weightData,
                                // 重要：包含训练数据
                                trainingData: (modelJson && modelJson.trainingData) || null,
                                // 包含训练参数
                                trainParams: (modelJson && modelJson.trainParams) || null,
                                createdAt: model.createdAt || new Date().toISOString(),
                                cloudId: model.id,
                                fromCloud: true
                            };

                            console.log('🐱 initializeModelData: 构建的模型数据:', {
                                hasTrainingData: !!modelData.trainingData,
                                classLabels: modelData.classLabels,
                                totalSamples: modelData.totalSamples,
                                trainingDataKeys: modelData.trainingData ? Object.keys(modelData.trainingData) : []
                            });
                            
                            // 如果已存在相同ID的模型，则更新
                            if (existingModelIndex >= 0) {
                                savedModels[existingModelIndex] = modelData;
                            } else {
                                // 否则添加新模型
                                savedModels.push(modelData);
                            }
                            
                        } catch (modelError) {
                            console.error(`处理模型 ${model.name} 失败:`, modelError);
                            continue;
                        }
                    }
                    
                    // 按创建时间排序模型
                    savedModels.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                    
                    // 如果模型数量过多，只保留最新的10个
                    if (savedModels.length > 10) {
                        savedModels = savedModels.slice(0, 10);
                    }
                    
                    // 更新模型计数
                    this.modelCount = savedModels.length;
                    
                    // 保存到localStorage
                    localStorage.setItem('poseModels', JSON.stringify(savedModels));
                    
                    // 显示成功通知
                    notification.show(`成功加载并缓存了 ${savedModels.length} 个姿态模型`, 'success');
                    
                    //console.log(`成功加载并缓存了 ${savedModels.length} 个姿态模型`);
                }
            } else {
                console.warn('获取模型数据失败:', result.message);
            }
        } catch (error) {
            console.error('初始化模型数据时发生错误:', error);
        }
    }

    // 编辑姿态模型方法
    editModelPose() {
        console.log('🐱 Claude 4.0 sonnet: 编辑姿态模型方法被调用了！');
        console.log('🐱 当前时间:', new Date().toISOString());
        console.log('🐱 开始编辑姿态模型...');

        // 创建弹窗容器
        const modalContainer = document.createElement('div');
        modalContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;

        // 创建弹窗内容
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        `;

        // 添加标题
        const title = document.createElement('h2');
        title.textContent = '编辑姿态模型';
        title.style.marginTop = '0';
        modalContent.appendChild(title);

        // 创建标签页容器
        const tabsContainer = document.createElement('div');
        tabsContainer.style.cssText = `
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 16px;
        `;

        // 创建本地模型标签
        const localTab = document.createElement('div');
        localTab.textContent = '本地模型';
        localTab.style.cssText = `
            padding: 8px 16px;
            cursor: pointer;
            font-weight: bold;
            color: #FF6B6B;
            border-bottom: 2px solid #FF6B6B;
        `;

        // 创建云端模型标签
        const cloudTab = document.createElement('div');
        cloudTab.textContent = '云端模型';
        cloudTab.style.cssText = `
            padding: 8px 16px;
            cursor: pointer;
            color: #666;
        `;

        tabsContainer.appendChild(localTab);
        tabsContainer.appendChild(cloudTab);
        modalContent.appendChild(tabsContainer);

        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            position: relative;
            min-height: 200px;
        `;
        modalContent.appendChild(contentContainer);

        // 创建上传按钮容器（在左下角）
        const uploadContainer = document.createElement('div');
        uploadContainer.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            display: none;
        `;
        modalContent.appendChild(uploadContainer);

        // 创建文件上传输入
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';
        fileInput.style.display = 'none';
        fileInput.id = 'edit-model-file-upload';
        uploadContainer.appendChild(fileInput);

        // 创建上传按钮
        const uploadButton = document.createElement('button');
        uploadButton.style.cssText = `
            padding: 6px 12px;
            background-color: #FF6B6B;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
        `;

        // 添加上传图标
        uploadButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" style="margin-right: 6px;" viewBox="0 0 16 16">
                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
            </svg>
            上传并编辑模型文件
        `;

        uploadButton.addEventListener('click', () => {
            fileInput.click();
        });
        uploadContainer.appendChild(uploadButton);

        // 处理文件上传
        fileInput.addEventListener('change', async (event) => {
            if (event.target.files.length === 0) return;

            const file = event.target.files[0];

            try {
                // 读取文件内容
                const fileContent = await this.readFileAsText(file);

                // 验证并处理模型数据
                await this.handleEditModelFile(fileContent, modalContainer);

            } catch (error) {
                console.error('🐱 处理编辑模型文件失败:', error);
                notification.error('处理模型文件失败: ' + error.message);

                // 清除文件输入
                fileInput.value = '';
            }
        });

        // 显示加载中
        const showLoading = () => {
            contentContainer.innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                    <div style="text-align: center;">
                        <div style="border: 4px solid #f3f3f3; border-top: 4px solid #FF6B6B; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
                        <p style="margin-top: 16px; color: #666;">加载中...</p>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
        };

        // 显示错误信息
        const showError = (message) => {
            contentContainer.innerHTML = `
                <div style="text-align: center; padding: 40px 20px;">
                    <p style="color: #d32f2f; margin-bottom: 16px;">加载失败</p>
                    <p style="color: #666;">${message}</p>
                </div>
            `;
        };

        // 从本地存储获取已保存的模型
        const loadLocalModels = () => {
            console.log('🐱 开始加载本地模型...');
            showLoading();

            try {
                // 获取本地缓存模型
                const savedModelsStr = localStorage.getItem('poseModels');
                let savedModels = [];
                if (savedModelsStr) {
                    savedModels = JSON.parse(savedModelsStr);
                }

                console.log('🐱 本地模型数量:', savedModels.length);
                console.log('🐱 本地模型详细信息:', savedModels);

                // 过滤出有训练数据的模型
                const editableModels = savedModels.filter(model => {
                    // 检查是否有训练数据（扁平结构）
                    const hasTrainingData = model.trainingData;
                    console.log(`🐱 模型 ${model.name} 数据结构:`, {
                        hasTrainingData: !!hasTrainingData,
                        topLevelKeys: Object.keys(model),
                        trainingDataType: typeof model.trainingData,
                        trainingDataKeys: model.trainingData ? Object.keys(model.trainingData) : []
                    });

                    // 对于旧格式的模型，如果没有训练数据，也显示出来让用户知道
                    // 但标记为不可编辑
                    return true; // 暂时显示所有模型，在点击时再检查
                });

                console.log('🐱 可编辑的本地模型数量:', editableModels.length);

                // 显示模型列表或无模型提示
                this.renderEditModelsList(editableModels, '本地', contentContainer, modalContainer);

                // 显示上传按钮
                uploadContainer.style.display = 'block';
                console.log('🐱 本地模型加载完成');
            } catch (error) {
                console.error('🐱 加载本地模型失败:', error);
                showError('加载本地模型失败: ' + error.message);
            }
        };

        // 从服务器获取用户保存的模型
        const loadCloudModels = async () => {
            console.log('🐱 开始加载云端模型...');
            showLoading();

            // 隐藏上传按钮
            uploadContainer.style.display = 'none';

            try {
                // 获取token
                const token = localStorage.getItem('token');
                if (!token) {
                    console.log('🐱 用户未登录');
                    showError('请先登录后再查看云端模型');
                    return;
                }

                console.log('🐱 开始调用 workApi.getPoseModelsList()...');
                // 从服务器获取模型列表
                const result = await workApi.getPoseModelsList();
                console.log('🐱 云端模型API调用结果:', result);

                if (!result || result.code !== 200 || !result.data) {
                    throw new Error(result?.message || '获取云端模型失败');
                }

                // 云端模型不预先过滤，因为训练数据可能在模型文件中
                const cloudModels = result.data || [];
                console.log('🐱 云端模型数量:', cloudModels.length);

                // 对于云端模型，我们显示所有模型，在下载时再检查是否有训练数据
                console.log('🐱 显示所有云端模型，将在下载时检查训练数据');

                // 渲染模型列表
                this.renderEditModelsList(cloudModels, '云端', contentContainer, modalContainer);
                console.log('🐱 云端模型加载完成');

            } catch (error) {
                console.error('🐱 加载云端模型失败:', error);
                showError('加载云端模型失败: ' + error.message);
            }
        };

        // 标签页切换事件
        localTab.addEventListener('click', () => {
            // 更新标签样式
            localTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                font-weight: bold;
                color: #FF6B6B;
                border-bottom: 2px solid #FF6B6B;
            `;
            cloudTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: #666;
            `;

            // 加载本地模型
            loadLocalModels();
        });

        cloudTab.addEventListener('click', () => {
            // 更新标签样式
            cloudTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                font-weight: bold;
                color: #FF6B6B;
                border-bottom: 2px solid #FF6B6B;
            `;
            localTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: #666;
            `;

            // 加载云端模型
            loadCloudModels();
        });

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.textContent = '取消';
        closeButton.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            padding: 8px 16px;
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        `;

        closeButton.addEventListener('click', () => {
            document.body.removeChild(modalContainer);
        });

        modalContent.appendChild(closeButton);
        modalContainer.appendChild(modalContent);
        document.body.appendChild(modalContainer);

        // 默认加载本地模型
        loadLocalModels();

        // 点击背景关闭弹窗
        modalContainer.addEventListener('click', (e) => {
            if (e.target === modalContainer) {
                document.body.removeChild(modalContainer);
            }
        });
    }

    // 读取文件为文本的辅助函数
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsText(file);
        });
    }

    // 处理编辑模型文件
    async handleEditModelFile(fileContent, modalContainer) {
        try {
            // 检查是否为有效的JSON
            let modelData;
            try {
                modelData = JSON.parse(fileContent);
                console.log('🐱 JSON解析成功，姿态模型数据:', modelData);
            } catch (parseError) {
                console.error('🐱 JSON解析失败:', parseError);
                throw new Error('模型文件格式错误，无法解析JSON数据');
            }

            // 验证是否为姿态模型文件（支持扁平结构和旧的metadata结构）
            const modelType = modelData.modelType || (modelData.metadata && modelData.metadata.modelType);
            if (!modelType || modelType !== 'pose') {
                console.error('🐱 不支持的模型类型:', {type: modelType});
                throw new Error('不支持的模型类型，请选择姿态训练模型');
            }

            // 检查是否有训练数据
            if (!modelData.trainingData) {
                console.error('🐱 模型没有训练数据');
                throw new Error('该模型没有训练数据，无法编辑。请选择包含训练数据的模型文件。');
            }

            // 设置待导入的模型数据
            window._pendingPoseModelDataForEdit = fileContent;
            console.log('🐱 设置待导入的姿态模型数据，长度:', window._pendingPoseModelDataForEdit?.length);

            // 关闭弹窗
            document.body.removeChild(modalContainer);

            // 打开训练界面
            console.log('🐱 打开姿态训练界面');
            this.showTrainDialogPose();

            notification.show('模型文件已选择，正在打开训练界面...', 'success');

        } catch (error) {
            console.error('🐱 处理编辑模型文件失败:', error);
            throw error;
        }
    }

    // 渲染编辑模型列表
    renderEditModelsList(models, type, contentContainer, modalContainer) {
        // 清空内容容器
        contentContainer.innerHTML = '';

        if (models.length === 0) {
            const noModels = document.createElement('p');
            noModels.textContent = `暂无保存的${type}模型`;
            noModels.style.textAlign = 'center';
            noModels.style.padding = '40px 0';
            noModels.style.color = '#666';
            contentContainer.appendChild(noModels);
            return;
        }

        const modelsContainer = document.createElement('div');
        modelsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 12px;
        `;

        // 根据是本地还是云端模型，使用不同的渲染逻辑
        if (type === '本地') {
            // 本地模型渲染
            models.forEach(model => {
                const modelItem = document.createElement('div');
                modelItem.style.cssText = `
                    padding: 12px;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;
                    transition: background-color 0.2s;
                `;

                // 添加悬停效果
                modelItem.addEventListener('mouseenter', () => {
                    modelItem.style.backgroundColor = '#f5f5f5';
                });
                modelItem.addEventListener('mouseleave', () => {
                    modelItem.style.backgroundColor = 'white';
                });

                const modelInfo = document.createElement('div');

                const modelName = document.createElement('div');
                modelName.textContent = `${model.name} (序号: ${model.number})`;
                modelName.style.fontWeight = 'bold';
                modelInfo.appendChild(modelName);

                if (model.classLabels) {
                    const modelClasses = document.createElement('div');
                    modelClasses.textContent = `类别: ${model.classLabels.join(', ')}`;
                    modelClasses.style.cssText = 'font-size: 12px; color: #666;';
                    modelInfo.appendChild(modelClasses);
                }

                const modelDate = document.createElement('div');
                modelDate.textContent = `创建时间: ${new Date(model.createdAt).toLocaleString()}`;
                modelDate.style.cssText = 'font-size: 12px; color: #666;';
                modelInfo.appendChild(modelDate);

                modelItem.appendChild(modelInfo);

                const editButton = document.createElement('button');
                editButton.textContent = '编辑';
                editButton.style.cssText = `
                    padding: 6px 12px;
                    background-color: #FF6B6B;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                `;

                editButton.addEventListener('click', async (e) => {
                    e.stopPropagation();

                    try {
                        // 检查模型是否有训练数据
                        if (!model.trainingData && (!model.metadata || !model.metadata.trainingData)) {
                            notification.error('该模型没有训练数据，无法编辑');
                            return;
                        }

                        // 构造模型数据字符串
                        const modelDataString = JSON.stringify(model);

                        // 处理编辑模型
                        await this.handleEditModelFile(modelDataString, modalContainer);

                    } catch (error) {
                        console.error('编辑本地模型失败:', error);
                        notification.error('编辑模型失败: ' + error.message);
                    }
                });

                modelItem.appendChild(editButton);
                modelsContainer.appendChild(modelItem);
            });
        } else {
            // 云端模型渲染
            models.forEach(model => {
                const modelItem = document.createElement('div');
                modelItem.style.cssText = `
                    padding: 12px;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;
                    transition: background-color 0.2s;
                `;

                // 添加悬停效果
                modelItem.addEventListener('mouseenter', () => {
                    modelItem.style.backgroundColor = '#f5f5f5';
                });
                modelItem.addEventListener('mouseleave', () => {
                    modelItem.style.backgroundColor = 'white';
                });

                const modelInfo = document.createElement('div');

                const modelName = document.createElement('div');
                modelName.textContent = model.modelName || model.name || '未命名模型';
                modelName.style.fontWeight = 'bold';
                modelInfo.appendChild(modelName);

                if (model.classLabels && model.classLabels.length > 0) {
                    const modelClasses = document.createElement('div');
                    modelClasses.textContent = `类别: ${model.classLabels.join(', ')}`;
                    modelClasses.style.cssText = 'font-size: 12px; color: #666;';
                    modelInfo.appendChild(modelClasses);
                }

                const modelDate = document.createElement('div');
                modelDate.textContent = `创建时间: ${new Date(model.createdAt).toLocaleString()}`;
                modelDate.style.cssText = 'font-size: 12px; color: #666;';
                modelInfo.appendChild(modelDate);

                modelItem.appendChild(modelInfo);

                const editButton = document.createElement('button');
                editButton.textContent = '编辑';
                editButton.style.cssText = `
                    padding: 6px 12px;
                    background-color: #FF6B6B;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                `;

                editButton.addEventListener('click', async (e) => {
                    e.stopPropagation();

                    try {
                        // 从云端下载模型数据
                        const modelDataString = await this.downloadCloudModelForEdit(model);

                        if (modelDataString) {
                            // 处理编辑模型
                            await this.handleEditModelFile(modelDataString, modalContainer);
                        }

                    } catch (error) {
                        console.error('编辑云端模型失败:', error);
                        notification.error('编辑云端模型失败: ' + error.message);
                    }
                });

                modelItem.appendChild(editButton);
                modelsContainer.appendChild(modelItem);
            });
        }

        contentContainer.appendChild(modelsContainer);
    }

    // 下载云端模型用于编辑
    async downloadCloudModelForEdit(model) {
        try {
            console.log('🐱 开始下载云端模型用于编辑:', model);

            // 检查模型是否有文件URL
            if (!model.fileUrl) {
                throw new Error('云端模型缺少文件URL');
            }

            // 显示加载提示
            notification.show('正在下载云端模型...', 'info');

            // 下载模型文件
            const response = await fetch(model.fileUrl);
            if (!response.ok) {
                throw new Error(`下载失败: ${response.status} ${response.statusText}`);
            }

            const modelDataString = await response.text();

            // 验证下载的数据
            let modelData;
            try {
                modelData = JSON.parse(modelDataString);
            } catch (parseError) {
                throw new Error('下载的模型文件格式错误');
            }

            // 检查是否有训练数据
            if (!modelData.trainingData) {
                throw new Error('该云端模型没有训练数据，无法编辑');
            }

            console.log('🐱 云端模型下载成功，数据长度:', modelDataString.length);
            return modelDataString;

        } catch (error) {
            console.error('🐱 下载云端模型失败:', error);
            throw error;
        }
    }
}

// eslint-disable-next-line import/no-commonjs
module.exports = function (runtime) {
    return new LogicLeapPoseTrainBlocks(runtime);
};
