// 开发环境配置
const DEV_CONFIG = {
    API_HOST: '127.0.0.1',
    API_PORT: '8601',
    BASE_URL: 'http://127.0.0.1:8601',
    SERVER_URL: 'http://127.0.0.1:8602',
    API_URL: 'http://127.0.0.1:8003',
    WS_URL: 'ws://127.0.0.1:8001/ws',
    WS_SERVER_URL: 'ws://127.0.0.1:8602/server/ws'
};
  
// 生产环境配置
const PROD_CONFIG = {
    API_HOST: 'www.logicleapai.cn', 
    API_PORT: '8601',
    BASE_URL: '${API_HOST}:${API_PORT}',
    SERVER_URL: 'https://www.logicleapai.cn/server',
    API_URL: 'https://www.logicleapai.cn/api',
    WS_URL: 'wss://logicleapai.cn/ws',
    WS_SERVER_URL: 'wss://logicleapai.cn/server/ws'
};
  
// 根据环境选择配置
const CONFIG = process.env.NODE_ENV === 'production' ? PROD_CONFIG : DEV_CONFIG;
  
// 导出配置项
export const API_HOST = CONFIG.API_HOST;
export const API_PORT = CONFIG.API_PORT;
export const BASE_URL = CONFIG.BASE_URL;
export const SERVER_URL = CONFIG.SERVER_URL;
export const API_URL = CONFIG.API_URL;
export const WS_URL = CONFIG.WS_URL;
export const WS_SERVER_URL = CONFIG.WS_SERVER_URL;


// 也可以导出完整配置对象
export const config = CONFIG;