'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Select, Avatar, message, Form, Input, Button } from 'antd';
import { X, Eye, EyeOff, AlertCircle } from 'lucide-react';
import auth from '../lib/auth';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, setUser } from '../lib/store';
import pcaData from '../public/pca.json';
import { schoolApi } from '../lib/api/school';
import userApi from '@/lib/api/user';
import request from '../lib/request';
import { store } from '../lib/store';
import { fetchSchools } from '@/app/teacher-space/utils/school';
import { error } from 'console';
import ForgotPasswordModal from './forgot-password-modal';
import SlideCaptcha from './slide-captcha';
import LoginDialog from "logic-common/dist/components/LoginDialog"
import { GetNotification } from 'logic-common/dist/components/Notification';
import BindPhoneModal from './bind-phone-modal';
import { createRoot } from 'react-dom/client';
import RoleSelectionModalForNewUser from '../app/components/userAuth/RoleSelectionModalForNewUser';
import { resolve } from 'path';

interface LoginDialogWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}
// 引入抽取出的组件
import ChooseAccountModal from '@/app/components/userAuth/ChooseAccountModal';

// 定义密码设置模态框组件
interface SetPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  phone: string;
}

const SetPasswordModal: React.FC<SetPasswordModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  phone
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const notification = GetNotification();

  // 处理表单提交
  const handleSubmit = async (values: { password: string; confirmPassword: string }) => {
    setLoading(true);
    try {
      const response = await auth.setPassword(phone, values.password);
      if (response.data && response.data.code === 200) {
        notification.success('密码设置成功');
        onSuccess();
      } else {
        notification.error((response.data && response.data.message) || '密码设置失败，请稍后重试');
      }
    } catch (error) {
      console.error('密码设置失败:', error);
      notification.error('密码设置失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消操作
  const handleCancel = async () => {
    // 如果用户点击右上角关闭按钮或取消，则自动设置默认密码为123456
    setLoading(true);
    try {
      const response = await auth.setPassword(phone, '123456');
      if (response.data && response.data.code === 200) {
        notification.success('已为您设置默认密码: 123456');
        onSuccess();
      } else {
        notification.error((response.data && response.data.message) || '默认密码设置失败');
      }
    } catch (error) {
      console.error('默认密码设置失败:', error);
      notification.error('默认密码设置失败，请稍后重试');
    } finally {
      setLoading(false);
      onClose();
    }
  };

  return (
    <Modal
      title="设置密码"
      open={isOpen}
      footer={null}
      onCancel={handleCancel}
      width={400}
      centered
      closeIcon={true}
    >
      <div className="p-4">
        <p className="mb-4 text-gray-600">请为您的账号设置一个密码，方便下次登录</p>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="password"
            label={<>
              <span className="text-red-500">*</span> 密码
            </>}
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度不能少于6位' }
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            label={<>
              <span className="text-red-500">*</span> 确认密码
            </>}
            rules={[
              { required: true, message: '请再次输入密码' },
              { min: 6, message: '密码长度不能少于6位' }
            ]}
          >
            <Input.Password placeholder="请再次输入密码" />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
            >
              确认
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

/**
 * 登录对话框组件包装器
 * 这个组件是对common库中LoginDialog组件的包装，提供了所需的API调用
 */
const LoginDialogWrapper: React.FC<LoginDialogWrapperProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const dispatch = useDispatch();
  const [loginType, setLoginType] = useState<'normal' | 'student'>('normal');
  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [district, setDistrict] = useState('');
  const [schoolName, setSchoolName] = useState('');
  const [studentNumber, setStudentNumber] = useState('');
  const [cities, setCities] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [showTerms, setShowTerms] = useState<'service' | 'privacy' | null>(null);
  const [termsContent, setTermsContent] = useState('');
  const [schools, setSchools] = useState<{
    id: number;
    schoolName: string;
  }[]>([]);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [schoolSearchText, setSchoolSearchText] = useState('');
  const [filteredSchools, setFilteredSchools] = useState<{
    id: number;
    schoolName: string;
    province: string;
    city: string;
    district: string;
  }[]>([

  ]);
  const [loginMode, setLoginMode] = useState<'password' | 'verify'>('verify');
  const [verifyCode, setVerifyCode] = useState('');
  const [roleList, setRoleList] = useState<any[]>([]);
  const [showRoleSelection, setShowRoleSelection] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [showSetPassword, setShowSetPassword] = useState(false);
  const [sessionId, setSessionId] = useState('');
  // 添加绑定手机号相关状态
  const [showBindPhoneModal, setShowBindPhoneModal] = useState(false);
  const [showNewUserRoleSelection, setShowNewUserRoleSelection] = useState(false);
  const [showSetPasswordModal, setShowSetPasswordModal] = useState(false);
  const [tempUserPhone, setTempUserPhone] = useState(''); // 存储临时用户手机号用于设置密码

  // 添加角色选择Promise解析器引用
  const roleSelectResolverRef = React.useRef<((value: any) => void) | null>(null);

  const userId = useSelector((state: RootState) => state.user.userState.userId)
  // 判断是否是直辖市
  const isMunicipality = (provinceName: string) => {
    return ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName);
  };

  // 添加useEffect来监视状态变化
  useEffect(() => {
    console.log("状态变化 - showRoleSelection:", showRoleSelection);
    console.log("状态变化 - roleList:", roleList);
  }, [showRoleSelection, roleList]);

  // 监听token变化，处理扫码登录
  useEffect(() => {
    console.log('LoginDialogWrapper组件挂载，开始监听登录状态');

    // 添加调试函数，检查状态变化
    const checkLoginState = () => {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      const userId = localStorage.getItem('userId');
      console.log(`登录状态检查: token存在=${!!token}, user存在=${!!user}, userId=${userId || '无'}`);

      // 修改条件：只要有token就尝试获取用户信息，无论user是否存在
      if (token) {
        // 检查Redux中是否已有用户信息
        const currentState = store.getState();
        const isLoggedInRedux = currentState?.user?.userState?.isLoggedIn || false;

        console.log(`Redux状态: isLoggedIn=${isLoggedInRedux}`);

        // 如果Redux中没有登录状态，但有token，需要处理
        if (!isLoggedInRedux) {
          try {
            // 尝试获取用户ID
            const storedUserId = localStorage.getItem('userId');

            if (storedUserId) {
              console.log(`尝试通过userId=${storedUserId}获取用户信息`);

              // 有用户ID，使用getUserInfo获取用户信息
              userApi.getUserInfo(Number(storedUserId))
                .then(response => {
                  console.log('获取用户信息响应:', response);

                  if (response && response.code === 200 && response.data) {
                    const userInfo = response.data;
                    localStorage.setItem('userId', userInfo.id?.toString() || '');

                    // 构建完整的用户数据对象
                    const userData = {
                      userId: userInfo.id,
                      nickName: userInfo.nickName,
                      avatarUrl: userInfo.avatarUrl,
                      gender: userInfo.gender,
                      phone: userInfo.phone,
                      isLoggedIn: true,
                      roleId: userInfo.roleId,
                      // 通过安全的方式获取register_type
                      registerType: (userInfo as any).register_type || ''
                    };
                    console.log('处理扫码登录用户信息', userData);
                    // 更新Redux状态 - 这里是关键，确保与密码登录一致
                    dispatch(setUser(userData));
                    // 更新localStorage
                    localStorage.setItem('user', JSON.stringify(userData));

                    // 触发用户状态更新事件，确保Navbar等组件能感知到登录状态变化
                    const updateEvent = new CustomEvent('userStateUpdate', {
                      detail: { isLoggedIn: true, userData }
                    });
                    window.dispatchEvent(updateEvent);

                    // 只有在不显示角色选择框时才调用onSuccess
                    if (onSuccess && !showRoleSelection) {
                      console.log("调用onSuccess关闭登录框");
                      onSuccess();
                    } else if (showRoleSelection) {
                      console.log("检测到正在选择角色，不关闭登录框");
                    }
                    // 添加一个小延迟后强制刷新页面，确保所有组件都能获取最新状态
                    setTimeout(() => {
                      console.log('刷新页面应用新状态');
                      window.location.reload();
                    }, 300);
                  }
                })
                .catch(err => {
                  console.error('获取用户信息失败:', err);
                });
            } else if (token) {
              console.log('没有userId但有token，尝试解析token或刷新页面');

              // 尝试通过其他方式处理，比如解析token
              try {
                // 如果token看起来有效，尝试刷新页面
                if (token.length > 20) {
                  console.log('有效token但无法获取用户信息，尝试刷新页面');
                  localStorage.removeItem('user'); // 确保user为空，强制下次检查
                  // setTimeout(() => window.location.reload(), 300);
                }
              } catch (e) {
                console.error('处理token失败:', e);
              }
            }
          } catch (err) {
            console.error('处理扫码登录失败:', err);
          }
        }
      }
    };

    // 立即执行一次检查
    checkLoginState();

    // 设置定时器定期检查，但在显示角色选择框时暂停检查
    const checkInterval = setInterval(() => {
      if (!showRoleSelection) {
        console.log("定时检查登录状态");
        checkLoginState();
      } else {
        console.log("角色选择框显示中，暂停登录状态检查");
      }
    }, 2000);

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' || e.key === 'userId') {
        console.log(`检测到${e.key}变化，触发状态检查`);
        // 只在不显示角色选择框时检查
        if (!showRoleSelection) {
          checkLoginState();
        } else {
          console.log("角色选择框显示中，忽略token变化");
        }
      }
    };

    // 添加全局storage事件监听(这个只在其他页面修改storage时触发)
    window.addEventListener('storage', handleStorageChange);

    return () => {
      console.log('LoginDialogWrapper组件卸载，清除监听器');
      clearInterval(checkInterval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [dispatch, onSuccess, onClose]);


  // 登录处理函数
  const handleLogin = async (values: any) => {
    try {
      // 使用any类型
      let response: any;

      if (values.type === 'normal') {
        if (values.mode === 'verify') {
          // 验证码登录
          response = await auth.verifyCodeLogin(values.data.phone, values.data.verifyCode);
          // 检查是否返回了角色列表
          if (response.code === 200 && response.data?.roleList && response.data.roleList.length > 0) {
            console.log("检测到多角色列表:", response.data.roleList);
            console.log("角色数量:", response.data.roleList.length);

            // 保存sessionId
            if (response.data.sessionId) {
              console.log("保存sessionId:", response.data.sessionId);
              setSessionId(response.data.sessionId);
            }

            // 先设置角色列表
            setRoleList(response.data.roleList);

            // 确保列表已设置后再显示弹窗
            setTimeout(() => {
              console.log("设置showRoleSelection为true");
              setShowRoleSelection(true);
            }, 100);

            // 创建一个新的Promise，并将其解析器保存到ref中
            return new Promise((resolve) => {
              console.log("创建等待角色选择的Promise");
              roleSelectResolverRef.current = resolve;
            });


          }
          if (response.code === 200) {
            try {
              if (!response.data?.userInfo) {
                throw new Error('获取用户信息失败');
              }

              const data = response.data;
              const userInfo = data.userInfo;
              localStorage.setItem('token', data.token);
              localStorage.setItem('userId', userInfo?.id?.toString() || '');
              if (userInfo) {
                const userData = {
                  userId: userInfo?.id,
                  nickName: userInfo?.nickName,
                  avatarUrl: userInfo?.avatarUrl,
                  gender: userInfo?.gender,
                  phone: userInfo?.phone,
                  isLoggedIn: true,
                  roleId: userInfo?.roleId,
                  // 通过安全的方式获取register_type
                  registerType: (userInfo as any).register_type || ''
                };

                dispatch(setUser(userData));
                localStorage.setItem('user', JSON.stringify(userData));

                // 检查是否为新用户，如果是新用户则先显示密码设置弹窗
                if (data.isNewUser) {
                  // 保存手机号用于设置密码
                  setTempUserPhone(values.data.phone);
                  // 显示密码设置模态框
                  setShowSetPasswordModal(true);
                  return response; // 返回响应，不关闭登录框
                }

                // 检查是否绑定了手机号
                if (!userInfo.phone) {
                  setShowBindPhoneModal(true);
                }
              } else {
                throw new Error('获取用户信息失败');
              }
            } catch (error) {
              localStorage.removeItem('token');
              throw error;
            }
          } else {
            throw new Error(response.message || '验证码登录失败');
          }
        } else {
          // 密码登录
          response = await auth.login(values.data.phone, values.data.password);
          console.log("登录响应:", JSON.stringify(response));

          // 检查是否返回了角色列表
          if (response.code === 200 && response.data?.roleList && response.data.roleList.length > 0) {
            console.log("检测到多角色列表:", response.data.roleList);
            console.log("角色数量:", response.data.roleList.length);

            // 保存sessionId
            if (response.data.sessionId) {
              console.log("保存sessionId:", response.data.sessionId);
              setSessionId(response.data.sessionId);
            }

            // 先设置角色列表
            setRoleList(response.data.roleList);

            // 确保列表已设置后再显示弹窗
            setTimeout(() => {
              console.log("设置showRoleSelection为true");
              setShowRoleSelection(true);
            }, 100);

            // 创建一个新的Promise，并将其解析器保存到ref中
            return new Promise((resolve) => {
              console.log("创建等待角色选择的Promise");
              roleSelectResolverRef.current = resolve;
            });
          }
          console.log("未检测到多角色列表或处理后继续执行");

          if (response.code === 200) {
            if (!response.data?.userInfo) {
              throw new Error('获取用户信息失败');
            }

            const data = response.data;
            const userInfo = data.userInfo;
            localStorage.setItem('token', data.token);
            localStorage.setItem('userId', userInfo?.id?.toString() || '');

            const userData = {
              userId: userInfo?.id,
              nickName: userInfo?.nickName,
              avatarUrl: userInfo?.avatarUrl,
              gender: userInfo?.gender,
              phone: userInfo?.phone,
              isLoggedIn: true,
              roleId: userInfo?.roleId,
              // 通过安全的方式获取register_type
              registerType: (userInfo as any).register_type || ''
            };

            dispatch(setUser(userData));
            localStorage.setItem('user', JSON.stringify(userData));

            // 检查是否为新用户，如果是新用户则显示角色选择弹窗
            if (data.isNewUser) {
              setShowNewUserRoleSelection(true);
              return response; // 返回响应，不关闭登录框
            }

            // 检查是否绑定了手机号
            if (!userInfo.phone) {
              setShowBindPhoneModal(true);
            }
          } else {
            throw new Error('账号或密码错误');
          }
        }
      }

      return response;
    } catch (error: any) {
      console.error('登录失败:', error);
      localStorage.removeItem('token');
      throw error;
    }
  };

  const handleStudentLogin = async (values: any) => {
    const response = await auth.studentLogin(values.data)
    if (response.code === 200) {
      try {
        if (!response.data?.userInfo) {
          throw new Error('获取用户信息失败');
        }

        const data = response.data;
        const userInfo = data.userInfo;
        localStorage.setItem('token', data.token);
        localStorage.setItem('userId', userInfo?.id?.toString() || '');
        if (userInfo) {
          const userData = {
            userId: userInfo?.id,
            nickName: userInfo?.nickName,
            avatarUrl: userInfo?.avatarUrl,
            gender: userInfo?.gender,
            phone: userInfo?.phone,
            isLoggedIn: true,
            roleId: userInfo?.roleId,
            // 通过安全的方式获取register_type
            registerType: (userInfo as any).register_type || ''
          };
          dispatch(setUser(userData));
          localStorage.setItem('user', JSON.stringify(userData));

          // 检查是否为新用户，如果是新用户则显示角色选择弹窗
          if (data.isNewUser) {
            setShowNewUserRoleSelection(true);
            return response; // 返回响应，不关闭登录框
          }

        } else {
          throw new Error('获取用户信息失败');
        }
      } catch (error) {
        localStorage.removeItem('token');
        throw error;
      }
    }
  };

  // 发送验证码
  const handleSendCode = async (phone: string) => {
    try {
      const { data: response } = await userApi.sendVerifyCode(phone);
      return response;
    } catch (error) {
      console.error('发送验证码失败:', error);
      throw error;
    }
  };

  // 验证码验证
  const handleVerifyCode = async (phone: string, code: string) => {
    try {
      const response = await auth.verifyCodeLogin(phone, code);
      return response;
    } catch (error) {
      console.error('验证码验证失败:', error);
      throw error;
    }
  };

  // 设置密码
  const handleSetPassword = async (phone: string, password: string) => {
    try {
      const response = await auth.setPassword(phone, password);
      return response;
    } catch (error) {
      console.error('密码设置失败:', error);
      throw error;
    }
  };

  // 使用验证码修改密码
  const handleResetPasswordByCode = async (phone: string, code: string, newPassword: string) => {
    try {
      const response = await auth.resetPasswordByCode({ phone, code, newPassword });
      return response;
    } catch (error) {
      console.error('密码设置失败:', error);
      throw error;
    }
  };

  // 获取学校列表
  const handleGetSchools = async (params: any) => {
    try {
      const response = await schoolApi.getList(params);
      return response;
    } catch (error) {
      console.error('获取学校列表失败:', error);
      throw error;
    }
  };

  // 绑定手机号成功的回调
  const handleBindPhoneSuccess = () => {
    // 更新用户信息
    if (userId) {
      userApi.getUserInfo(userId).then(response => {
        if (response.code === 200 && response.data) {
          const userInfo = response.data;
          // 更新Redux状态
          const userData = {
            userId: userInfo.id,
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            gender: userInfo.gender,
            phone: userInfo.phone,
            isLoggedIn: true,
            roleId: userInfo.roleId,
            // 通过安全的方式获取register_type
            registerType: (userInfo as any).register_type || ''
          };
          dispatch(setUser(userData));
          localStorage.setItem('user', JSON.stringify(userData));
        }
      });
    }

    // 如果有onSuccess回调，则调用
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      {showRoleSelection ? (
        <ChooseAccountModal
          isOpen={true}
          onClose={() => {
            console.log("关闭角色选择弹窗");
            setShowRoleSelection(false);
          }}
          roleList={roleList}
          sessionId={sessionId}
          setShowRoleSelection={setShowRoleSelection}
          roleSelectResolverRef={roleSelectResolverRef}
        />
      ) : showBindPhoneModal ? (
        <BindPhoneModal
          isOpen={true}
          onClose={() => {
            setShowBindPhoneModal(false);
            if (onSuccess) {
              onSuccess();
            }
          }}
          onSuccess={handleBindPhoneSuccess}
        />
      ) : showSetPasswordModal ? (
        <SetPasswordModal
          isOpen={true}
          phone={tempUserPhone}
          onClose={() => {
            setShowSetPasswordModal(false);
            if (onSuccess) {
              onSuccess();
            }
          }}
          onSuccess={() => {
            // 密码设置成功后，显示角色选择弹窗
            setShowSetPasswordModal(false);
            setShowNewUserRoleSelection(true);
          }}
        />
      ) : showNewUserRoleSelection ? (
        <RoleSelectionModalForNewUser
          onSuccess={async () => {
            setShowNewUserRoleSelection(false);
            localStorage.removeItem('isNewUser');
            if (onSuccess) {
              onSuccess();
            }
            // 强制关闭登录窗口
            if (onClose) {
              onClose();
            }
          }}
          handleCloseRsmfnModal={() => {
            setShowNewUserRoleSelection(false);
            localStorage.removeItem('isNewUser');
            if (onSuccess) {
              onSuccess();
            }
            // 强制关闭登录窗口
            if (onClose) {
              onClose();
            }
          }}
        />
      ) : (
        <LoginDialog
          isOpen={isOpen}
          onClose={onClose}
          onSuccess={onSuccess}
          onLogin={handleLogin}
          onStudentLogin={handleStudentLogin}
          onSendCode={handleSendCode}
          onVerifyCode={handleVerifyCode}
          onSetPassword={handleSetPassword}
          onResetPassword={handleResetPasswordByCode}
          onGetSchools={handleGetSchools}
          pcaData={pcaData}
          verifyCodeCountdown={60}
          // apiBaseUrl='http://7uz4301cb969.vicp.fun'
          apiBaseUrl='http://10xh9vd648325.vicp.fun'
        />
      )}
    </>
  );
};

export default LoginDialogWrapper; 