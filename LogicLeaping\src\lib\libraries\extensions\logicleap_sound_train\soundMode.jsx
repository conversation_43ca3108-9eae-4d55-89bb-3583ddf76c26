import React, { useState, useEffect, useRef, useCallback ,useImperativeHandle} from 'react';
import J<PERSON><PERSON><PERSON> from 'jszip';
import * as tf from '@tensorflow/tfjs';
import styles from './soundStyles';
import { saveModel, loadModel, validateModelFile, saveModelToCloud } from './modelUtils';
// 导入加载动画
import { createSquareLoadingAnimation } from '../utils/loadingAnimation';
import TrainNotification from '../utils/trainNotification';
import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
import TrainMain from '../utils/trainUtil/trainMain/trainMain.jsx';
import ClassCrad from './ClassCrad.jsx';
import Preview from './Preview.jsx';
// 在文件顶部添加


// 修改动态加载方式
const loadSpeechCommands = async () => {
  try {
   
    // 先检查是否已经加载
    if (window.speechCommands) {
     
      return window.speechCommands;
    }

    // 如果没有加载，先加载 TensorFlow.js
    if (!window.tf) {
      
      
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/static/utils/sound_train/tf.min.js';
        script.crossOrigin = 'anonymous';
        
        script.onload = () => {
         
          resolve();
        };
        
        script.onerror = (error) => {
          console.error('TensorFlow.js 加载失败:', error);
          console.error('加载失败的文件路径:', script.src);
          reject(error);
        };
        
        document.head.appendChild(script);
       
      });
    }

    // 然后加载 speech-commands
   
    
    await new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/static/utils/sound_train/speech-commands.min.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
       
        resolve();
      };
      
      script.onerror = (error) => {
        console.error('speech-commands 加载失败:', error);
        console.error('加载失败的文件路径:', script.src);
        reject(error);
      };
      
      document.head.appendChild(script);
     
    });

    if (window.speechCommands) {
     
      return window.speechCommands;
    } else {
      throw new Error('语音命令模型加载后未找到 window.speechCommands 对象');
    }
    
  } catch (error) {
    console.error('加载语音命令模型时发生错误:', error);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
};

// 根据传入的标签初始化类别节点
const getInitialClassNodes = (labels) => {
    const initialNodes = [
        { id: 0, name: '背景噪音', samples: 0, showMenu: false, isLoaded: false }
    ];

    if (labels && Array.isArray(labels) && labels.length > 0) {
        // 从第二个节点开始，使用传入的标签
        labels.forEach((label, index) => {
            initialNodes.push({
                id: index + 1,
                name: label || `类别${index + 2}`,
                samples: 0,
                showMenu: false,
                isLoaded: false
            });
        });
        // 如果只有一个标签，则添加一个默认的"类别3"
        if (labels.length === 1) {
            initialNodes.push({ id: 2, name: '类别3', samples: 0, showMenu: false, isLoaded: false });
        }
    } else {
        // 默认情况
        initialNodes.push({ id: 1, name: '类别2', samples: 0, showMenu: false, isLoaded: false });
    }
    return initialNodes;
};

// 修改组件声明以接收 ModeRef 属性
const SoundMode = ({ modeRef, modeString, initialClassLabels }) => {
  // 状态管理
  const [classNodes, setClassNodes] = useState(() => getInitialClassNodes(initialClassLabels));
  const [modelTrained, setModelTrained] = useState(false);
  
  const [predicting, setPredicting] = useState(false);
  
  const [samples, setSamples] = useState({});

  //训练参数
  const [isTraining, setIsTraining] = useState(false);
  const [training, setTraining] = useState(-1);
  const [advancedExpanded, setAdvancedExpanded] = useState(false);
  const [DefaultTrainParams] = useState({
    epochs: 5,
    batchSize: 8,
    learningRate: 0.005
  });
  const [trainParams, setTrainParams] = useState(DefaultTrainParams);
  //训练参数

  //trainMain里面的ref,用于调用里面的方法
  const trainMainFunctionRef = useRef(null);

  const [prediction, setPrediction] = useState(null);
  const [confidences, setConfidences] = useState([]);
  const [currentClass, setCurrentClass] = useState(-1);
  const [isCollecting, setIsCollecting] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [modelExported, setModelExported] = useState(false);

  //判断是否可以训练
  const canTrainRef = useRef(false);
  
  // 添加状态控制录制面板是否展开但不录制
  const [isRecordingPanelExpanded, setIsRecordingPanelExpanded] = useState(false);
  
  // 添加是否显示设置面板的状态
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  
  // 添加录制设置状态，以类别ID为键
  const [recordingSettings, setRecordingSettings] = useState({});
  
  // 添加声波数据状态
  const [audioData, setAudioData] = useState([]);
  const audioDataRef = useRef([]);
  // 添加临时声波数据状态，用于收集样本时的声波显示
  const [tempAudioData, setTempAudioData] = useState([]);
  const tempAudioDataRef = useRef([]);
  
  // Refs
  const recognizerRef = useRef(null);
  const modelRef = useRef(null);
  const recordStartTimeRef = useRef(0);
  const samplesRef = useRef([]);
  const sampleBatchRef = useRef([]);

  // 添加文件上传输入框的ref
  const fileInputRef = useRef(null);
  
  // 添加滚动和动画帧的引用
  
  // 常量
  const NUM_FRAMES = 3;
  const INPUT_SHAPE = [NUM_FRAMES, 232, 1];
  
  // 添加核心的音频处理和训练功能
  const [recognizer, setRecognizer] = useState(null);
  const [model, setModel] = useState(null);

  
  // 添加WAV文件预测相关的状态
  const [wavPredicting, setWavPredicting] = useState(false);
  const wavFileInputRef = useRef(null);
  
  // 添加状态用于存储类别映射关系
  const [classIdMapping, setClassIdMapping] = useState({});
  // 添加状态用于跟踪模型训练的原始类别
  const [originalModelClasses, setOriginalModelClasses] = useState([]);
  
  // 添加状态用于跟踪类别面板的滚动位置
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScrollHeight, setMaxScrollHeight] = useState(0);
  const classesPanelContentRef = useRef(null);
  const containerRef = useRef(null);
  // 添加scrollAnimationFrame的引用
  const scrollAnimationFrame = useRef(null);
  const [overlapFactor, setOverlapFactor] = useState(0.5); // 添加重叠系数状态

  // 为设置面板添加临时设置状态
  const [tempSettings, setTempSettings] = useState({
    delayTime: 0,
    recordingTime: 10
  });

  // 添加倒计时状态
  const [recordingCountdown, setRecordingCountdown] = useState(0);
  const countdownIntervalRef = useRef(null);

  // 添加状态用于存储完整音频数据
  const [fullAudioData, setFullAudioData] = useState({});
  const fullAudioDataRef = useRef({
    fullAudioUrl: null,
    fullAudioDuration: 0,
    classAudioUrls: {} // 添加按类别存储音频URL的映射
  });
  
  // 添加状态用于控制播放和提取样本
  const [isPlaying, setIsPlaying] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  
  // 添加音频录制相关引用
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef({}); // 改为对象，按节点ID存储
  const audioContextRef = useRef(null);
  const audioSourceRef = useRef(null);
  const audioBufferRef = useRef(null);
  
  // 添加音频时间相关状态
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioTimerRef = useRef(null);
  const isStoppingRef = useRef(false); // 添加正在停止状态的引用

  // 在其他状态变量后添加训练记录状态
  const [trainingHistory, setTrainingHistory] = useState({
    epochs: [],
    trainAccuracy: [],
    trainLoss: [],
    valAccuracy: [],
    valLoss: []
  });

  // 添加图表显示控制状态
  const [showDataChart, setShowDataChart] = useState(false);
  
  // 将图表的canvas引用移到顶层
  const accChartRef = useRef(null);
  const lossChartRef = useRef(null);

  // 在组件顶部添加全局参考，用于存储每个类别的频谱数据
  const allClassesTimeFreqDataRef = useRef({}); // 用来存储所有类别的频谱数据

  // 添加样本播放相关状态 - 与主播放器隔离
  const [samplePlayingState, setSamplePlayingState] = useState({
    isPlaying: false,
    currentSampleId: null,
    audioElement: null
  });

  useImperativeHandle(modeRef, () => ({
    // 暴露导入模型方法
    handleImportModel: () => {
      handleImportModel();
    },
    handleExportToLocal: () => {
      handleExportToLocal();
    },
    handleExportToCloud: () => {
      handleExportToCloud();
    }    
  }));
  
  // 将图表绘制相关的useEffect移到顶层
  useEffect(() => {
    if (showDataChart) {
      const drawChart = () => {
        const drawAccuracyChart = () => {
          const canvas = accChartRef.current;
          if (!canvas) return;
          
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // 设置画布尺寸
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          
          // 增加顶部填充，为图例留出更多空间
          const padding = { top: 30, right: 20, bottom: 30, left: 40 };
          const chartWidth = canvas.width - padding.left - padding.right;
          const chartHeight = canvas.height - padding.top - padding.bottom;
          
          // 绘制网格背景
          ctx.fillStyle = '#f9f9f9';
          ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
          
          // 绘制网格线
          ctx.beginPath();
          ctx.strokeStyle = '#e0e0e0';
          ctx.lineWidth = 1;
          
          // 水平网格线
          for (let i = 0; i <= 10; i++) {
            const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(canvas.width - padding.right, y);
          }
          
          // 垂直网格线 - 根据数据点数量决定
          const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
          if (trainingHistory.epochs.length > 0) {
            for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
              if (i <= trainingHistory.epochs.length) {
                const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                ctx.moveTo(x, padding.top);
                ctx.lineTo(x, canvas.height - padding.bottom);
              }
            }
          }
          
          ctx.stroke();
          
          // 绘制坐标轴
          ctx.beginPath();
          ctx.strokeStyle = '#999';
          ctx.lineWidth = 1.5;
          
          // X轴
          ctx.moveTo(padding.left, canvas.height - padding.bottom);
          ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
          
          // Y轴
          ctx.moveTo(padding.left, padding.top);
          ctx.lineTo(padding.left, canvas.height - padding.bottom);
          ctx.stroke();
          
          // 如果没有数据，则不绘制
          if (trainingHistory.epochs.length === 0) return;
          
          // 计算比例尺
          const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
          
          // 绘制图例 - 移到顶部标题位置上方，避免挡住数据
          ctx.font = '12px Arial';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          
          // 训练准确率图例
          ctx.fillStyle = '#4f7df9';
          ctx.fillRect(padding.left, padding.top - 20, 15, 6);
          ctx.fillText('acc', padding.left + 20, padding.top - 20);
          
          // 验证准确率图例
          if (trainingHistory.valAccuracy.some(v => v > 0)) {
            ctx.fillStyle = '#f97d4f';
            ctx.fillRect(padding.left + 120, padding.top - 20, 15, 6);
            ctx.fillText('test acc', padding.left + 140, padding.top - 20);
          }
          
          // 绘制训练准确率
          if (trainingHistory.trainAccuracy.length > 0) {
            ctx.beginPath();
            ctx.strokeStyle = '#4f7df9';
            ctx.lineWidth = 2;
            
            trainingHistory.trainAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            trainingHistory.trainAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              ctx.beginPath();
              ctx.fillStyle = '#4f7df9';
              ctx.arc(x, y, 3, 0, Math.PI * 2);
              ctx.fill();
            });
          }
          
          // 绘制验证准确率
          if (trainingHistory.valAccuracy.length > 0 && trainingHistory.valAccuracy.some(v => v > 0)) {
            ctx.beginPath();
            ctx.strokeStyle = '#f97d4f';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 3]);
            
            trainingHistory.valAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制数据点
            trainingHistory.valAccuracy.forEach((acc, i) => {
              if (acc > 0) {
                const x = padding.left + i * xScale;
                const y = canvas.height - padding.bottom - (acc * chartHeight);
                
                ctx.beginPath();
                ctx.fillStyle = '#f97d4f';
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
              }
            });
          }
          
          // 绘制Y轴刻度
          ctx.fillStyle = '#666';
          ctx.textAlign = 'right';
          ctx.textBaseline = 'middle';
          
          for (let i = 0; i <= 10; i++) {
            const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
            ctx.fillText((i / 10).toFixed(1), padding.left - 5, y);
          }
          
          // 绘制X轴刻度 - 每4个周期显示一个刻度
          const totalEpochs = trainingHistory.epochs.length;
          const labelStep = Math.max(1, Math.floor(totalEpochs / Math.min(10, totalEpochs)));
          
          if (totalEpochs > 0) {
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            
            // 绘制起点
            ctx.fillText(trainingHistory.epochs[0], padding.left, canvas.height - padding.bottom + 5);
            
            // 绘制中间点
            for (let i = labelStep; i < totalEpochs - 1; i += labelStep) {
              const x = padding.left + i * xScale;
              ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
            }
            
            // 绘制终点
            if (totalEpochs > 1) {
              ctx.fillText(
                trainingHistory.epochs[totalEpochs - 1], 
                padding.left + (totalEpochs - 1) * xScale, 
                canvas.height - padding.bottom + 5
              );
            }
          }
        };
        
        const drawLossChart = () => {
          const canvas = lossChartRef.current;
          if (!canvas) return;
          
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // 设置画布尺寸
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          
          // 增加顶部填充，为图例留出更多空间
          const padding = { top: 30, right: 20, bottom: 30, left: 40 };
          const chartWidth = canvas.width - padding.left - padding.right;
          const chartHeight = canvas.height - padding.top - padding.bottom;
          
          // 绘制网格背景
          ctx.fillStyle = '#f9f9f9';
          ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
          
          // 绘制网格线
          ctx.beginPath();
          ctx.strokeStyle = '#e0e0e0';
          ctx.lineWidth = 1;
          
          // 水平网格线
          const steps = 5;
          for (let i = 0; i <= steps; i++) {
            const y = canvas.height - padding.bottom - (i / steps) * chartHeight;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(canvas.width - padding.right, y);
          }
          
          // 垂直网格线 - 根据数据点数量决定
          const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
          if (trainingHistory.epochs.length > 0) {
            for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
              if (i <= trainingHistory.epochs.length) {
                const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                ctx.moveTo(x, padding.top);
                ctx.lineTo(x, canvas.height - padding.bottom);
              }
            }
          }
          
          ctx.stroke();
          
          // 绘制坐标轴
          ctx.beginPath();
          ctx.strokeStyle = '#999';
          ctx.lineWidth = 1.5;
          
          // X轴
          ctx.moveTo(padding.left, canvas.height - padding.bottom);
          ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
          
          // Y轴
          ctx.moveTo(padding.left, padding.top);
          ctx.lineTo(padding.left, canvas.height - padding.bottom);
          ctx.stroke();
          
          // 如果没有数据，则不绘制
          if (trainingHistory.epochs.length === 0) return;
          
          // 计算比例尺
          const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
          
          // 找出损失值的最大值，用于缩放
          const maxLoss = Math.max(
            ...trainingHistory.trainLoss,
            ...trainingHistory.valLoss.filter(l => l > 0)
          );
          
          // 绘制图例 - 移到顶部标题位置上方，避免挡住数据
          ctx.font = '12px Arial';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          
          // 训练损失图例
          ctx.fillStyle = '#4fb14f';
          ctx.fillRect(padding.left, padding.top - 20, 15, 6);
          ctx.fillText('loss', padding.left + 20, padding.top - 20);
          
          // 验证损失图例
          if (trainingHistory.valLoss.some(v => v > 0)) {
            ctx.fillStyle = '#b14f4f';
            ctx.fillRect(padding.left + 100, padding.top - 20, 15, 6);
            ctx.fillText('test loss', padding.left + 120, padding.top - 20);
          }
          
          // 绘制训练损失
          if (trainingHistory.trainLoss.length > 0) {
            ctx.beginPath();
            ctx.strokeStyle = '#4fb14f';
            ctx.lineWidth = 2;
            
            trainingHistory.trainLoss.forEach((loss, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            trainingHistory.trainLoss.forEach((loss, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
              
              ctx.beginPath();
              ctx.fillStyle = '#4fb14f';
              ctx.arc(x, y, 3, 0, Math.PI * 2);
              ctx.fill();
            });
          }
          
          // 绘制验证损失
          if (trainingHistory.valLoss.length > 0 && trainingHistory.valLoss.some(v => v > 0)) {
            ctx.beginPath();
            ctx.strokeStyle = '#b14f4f';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 3]);
            
            trainingHistory.valLoss.forEach((loss, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制数据点
            trainingHistory.valLoss.forEach((loss, i) => {
              if (loss > 0) {
                const x = padding.left + i * xScale;
                const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                
                ctx.beginPath();
                ctx.fillStyle = '#b14f4f';
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
              }
            });
          }
          
          // 绘制Y轴刻度
          ctx.fillStyle = '#666';
          ctx.textAlign = 'right';
          ctx.textBaseline = 'middle';
          
          for (let i = 0; i <= steps; i++) {
            const y = canvas.height - padding.bottom - (i / steps) * chartHeight;
            const value = (i / steps * maxLoss).toFixed(2);
            ctx.fillText(value, padding.left - 5, y);
          }
          
          // 绘制X轴刻度 - 每4个周期显示一个刻度
          const totalEpochs = trainingHistory.epochs.length;
          const labelStep = Math.max(1, Math.floor(totalEpochs / Math.min(10, totalEpochs)));
          
          if (totalEpochs > 0) {
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            
            // 绘制起点
            ctx.fillText(trainingHistory.epochs[0], padding.left, canvas.height - padding.bottom + 5);
            
            // 绘制中间点
            for (let i = labelStep; i < totalEpochs - 1; i += labelStep) {
              const x = padding.left + i * xScale;
              ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
            }
            
            // 绘制终点
            if (totalEpochs > 1) {
              ctx.fillText(
                trainingHistory.epochs[totalEpochs - 1], 
                padding.left + (totalEpochs - 1) * xScale, 
                canvas.height - padding.bottom + 5
              );
            }
          }
        };
        
        drawAccuracyChart();
        drawLossChart();
      };
      
      drawChart();
      window.addEventListener('resize', drawChart);
      
      return () => {
        window.removeEventListener('resize', drawChart);
      };
    }
  }, [showDataChart, trainingHistory]);


  // 添加一个useEffect来监听样本和类别节点的变化，并更新canTrainRef的值
  useEffect(() => {
    const updateCanTrain = () => {
      // 检查是否有至少两个类别节点
      const hasEnoughClasses = classNodes.length >= 2;
      
      // 检查每个类别是否都有足够的样本（至少1个）
      const allClassesHaveSamples = classNodes.every(node => {
        if(node.isLoaded){
          return true;
        }
        if(node.id === 0){
          return samples[node.id] && Object.keys(samples[node.id]).length >= 20;
        }else{
          return samples[node.id] && Object.keys(samples[node.id]).length >= 8;
        }
      });
      
      // 更新canTrainRef的值
      canTrainRef.current = hasEnoughClasses && allClassesHaveSamples;
      
      // 输出当前训练条件状态到控制台，帮助调试
      console.log('训练条件检查:', {
        classNodes,
        hasEnoughClasses,
        allClassesHaveSamples,
        canTrain: canTrainRef.current,
        classCount: classNodes.length,
        samplesState: Object.keys(samples).map(id => ({ 
          classId: id, 
          sampleCount: samples[id] ? Object.keys(samples[id]).length : 0 
        }))
      });
    };
  
  // 执行更新
  updateCanTrain();
  
}, [classNodes, samples]); // 依赖于类别节点和samples状态的变化
  
  // 显示消息
  const showMessage = (text, type = 'info') => {
    TrainNotification[type](text);
  };
  
  // 修改expandRecordingPanel函数，确保切换节点时停止播放
  const expandRecordingPanel = (classId) => {
    // 如果已经在录制状态，不做任何操作
    if (isCollecting) return;
    
    // 如果当前正在播放，停止播放
    if (isPlaying && audioSourceRef.current) {
      audioSourceRef.current.pause();
      audioSourceRef.current.currentTime = 0;
      audioSourceRef.current = null;
      
      // 清除时间更新定时器
      if (audioTimerRef.current) {
        clearInterval(audioTimerRef.current);
        audioTimerRef.current = null;
      }
      setIsPlaying(false);
      setCurrentTime(0);
    }
    
    // 设置当前类别并展开面板
    setCurrentClass(classId);
    setIsRecordingPanelExpanded(true);
    
    // 初始化临时声波数据
    tempAudioDataRef.current = Array(100).fill(0);
    setTempAudioData(Array(100).fill(0));
    
    // 重置设置面板状态
    setShowSettingsPanel(false);
    
    // 重置播放状态
    setCurrentTime(0);
    
    // 重新计算持续时间，如果有录制数据
    if (audioChunksRef.current[classId] && audioChunksRef.current[classId].length > 0) {
      // 尝试从已有数据中获取时长
      const actualDuration = fullAudioData[classId] && fullAudioData[classId].length > 0 ? 
        (fullAudioData[classId][fullAudioData[classId].length - 1].time / 1000) : 
        getClassRecordingSettings(classId).recordingTime;
      
      setDuration(actualDuration);
      console.log(`节点 ${classId} 的录制时长：${actualDuration}秒`);
      
      // 创建临时音频对象获取准确时长
      const audioBlob = new Blob(audioChunksRef.current[classId], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      const tempAudio = new Audio(audioUrl);
      
      tempAudio.onloadedmetadata = () => {
        if (tempAudio.duration && !isNaN(tempAudio.duration) && isFinite(tempAudio.duration)) {
          console.log(`节点 ${classId} 的音频元素时长：${tempAudio.duration}秒`);
          setDuration(tempAudio.duration);
        }
        URL.revokeObjectURL(audioUrl);
      };
    } else {
      setDuration(0);
    }
  };
  
  // 修改closeRecordingPanel函数，确保关闭面板时停止播放
  const closeRecordingPanel = () => {
    // 如果正在录制，先停止录制
    if (isCollecting) {
      stopCollecting();
    }
    
    // 如果正在播放，停止播放
    if (isPlaying && audioSourceRef.current) {
      audioSourceRef.current.pause();
      audioSourceRef.current.currentTime = 0;
      audioSourceRef.current = null;
      
      // 清除时间更新定时器
      if (audioTimerRef.current) {
        clearInterval(audioTimerRef.current);
        audioTimerRef.current = null;
      }
      setIsPlaying(false);
      setCurrentTime(0);
    }
    
    // 重置面板展开状态
    setIsRecordingPanelExpanded(false);
    
    // 只有在关闭面板时才重置当前类别
    setCurrentClass(-1);
    
    // 重置设置面板状态
    setShowSettingsPanel(false);
  };
  
  // 切换设置面板
  const toggleSettingsPanel = () => {
    setShowSettingsPanel(!showSettingsPanel);
  };
  
  // 打开设置面板时，加载当前类别的设置到临时设置中
  const openSettingsPanel = () => {
    const currentSettings = getClassRecordingSettings(currentClass);
    setTempSettings(currentSettings);
    setShowSettingsPanel(true);
  };
  
  // 取消设置，关闭面板，不保存更改
  const cancelSettings = () => {
    setShowSettingsPanel(false);
  };
  
  // 保存设置并关闭面板
  const saveSettings = () => {
    updateClassRecordingSettings(currentClass, tempSettings);
    setShowSettingsPanel(false);
  };
  
  // 在设置面板中更新临时设置
  const updateTempSettings = (key, value) => {
    setTempSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // 获取类别的录制设置
  const getClassRecordingSettings = (classId) => {
    // 如果没有为该类别设置过，返回默认值
    if (!recordingSettings[classId]) {
      if(classId === 0){
        return {
          delayTime: 0, // 默认无延迟，单位：秒
          recordingTime: 20 // 默认录制10秒
        }
      }
      return {
        delayTime: 0, // 默认无延迟，单位：秒
        recordingTime: 3 // 默认录制3秒
      };
    }
    return recordingSettings[classId];
  };
  
  // 更新类别的录制设置
  const updateClassRecordingSettings = (classId, newSettings) => {
    setRecordingSettings(prev => ({
      ...prev,
      [classId]: {
        ...getClassRecordingSettings(classId),
        ...newSettings
      }
    }));
  };
  
  // 修改初始化函数
  useEffect(() => {
    const setupRecognizer = async () => {
      // 创建加载动画
      const loadingAnimation = createSquareLoadingAnimation({
        message: '正在初始化语音模型...',
        backgroundColor: '#4766C2',
        boxColor: '#fff',
        textColor: '#fff'
      });
      
      try {
        // 更新加载进度
        loadingAnimation.updateProgress('初始化TensorFlow.js...',15);
        
        // 首先初始化 TensorFlow.js 后端
        await tf.ready();
       
        loadingAnimation.updateProgress('设置WebGL后端...',30);
        await tf.setBackend('webgl');
       
        loadingAnimation.updateProgress('加载语音命令模型...',45);
        const speechCommands = await loadSpeechCommands();
        if (!speechCommands) {
          throw new Error('语音命令模型加载失败');
        }
       
        loadingAnimation.updateProgress('创建语音识别器...',60);
        const modelURL = `${window.location.origin}/static/utils/sound_train/model.json`;
        const metadataURL = `${window.location.origin}/static/utils/sound_train/metadata.json`;
        const recognizer = await speechCommands.create(
            'BROWSER_FFT',
            null,
            modelURL,
            metadataURL
        );
       
        loadingAnimation.updateProgress('加载基础模型...',75);
        await recognizer.ensureModelLoaded();
        
        recognizerRef.current = recognizer;
        
        // 构建自定义模型
        loadingAnimation.updateProgress('构建自定义模型...',85);
        const model = tf.sequential();
       
        model.add(tf.layers.depthwiseConv2d({
          depthMultiplier: 8,
          kernelSize: [NUM_FRAMES, 3],
          activation: 'relu',
          inputShape: INPUT_SHAPE
        }));
        
        model.add(tf.layers.maxPooling2d({poolSize: [1, 2], strides: [2, 2]}));
       
        model.add(tf.layers.flatten());
        
        model.add(tf.layers.dense({units: 2, activation: 'softmax'}));
       
        const optimizer = tf.train.adam(0.01);
        model.compile({
          optimizer,
          loss: 'categoricalCrossentropy',
          metrics: ['accuracy']
        });
        
        modelRef.current = model;
        loadingAnimation.updateProgress('模型初始化完成',100);
        loadingAnimation.updateMessage('模型初始化完成！');
        // 移除加载动画
        loadingAnimation.remove();
      } catch (error) {
        // 出错时也要移除加载动画
        loadingAnimation.remove(() => {
        console.error('初始化语音命令模型失败:', error);
        console.error('错误堆栈:', error.stack);
        showMessage('初始化语音命令模型失败', 'error');
        });
      }
    };
    
    setupRecognizer();
  }, []);
  
  // 添加事件监听器，处理导入和导出模型的自定义事件
  useEffect(() => {
    // 处理导出模型到积木事件
    const handleExportToBlocks = () => {
      try {
        exportModelToBlocks();
        TrainNotification.success('模型已成功导出到积木');
      } catch (error) {
        TrainNotification.error('导出到积木失败: ' + error.message);
      }
    };
    
    // 添加事件监听器
    window.addEventListener('logicleap_sound_export_model', handleExportToBlocks);
    
    // 清理函数
    return () => {
      window.removeEventListener('logicleap_sound_export_model', handleExportToBlocks);
    };
  }, [classNodes, modelTrained, trainParams]);
  
    // 处理导入模型事件
    const handleImportModel = () => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    };
    
    // 处理导出模型到本地事件
    const handleExportToLocal = async () => {
      try {
        if (!modelRef.current || !modelTrained) {
          showMessage('请先训练模型', 'error');
          return;
        }
        
        await saveModel(modelRef.current, {
          userMetadata: {
            classes: classNodes.map(node => node.name),
            trainParams
          }
        });
        TrainNotification.success('模型已成功导出');
      } catch (error) {
        TrainNotification.error('导出模型失败: ' + error.message);
      }
    };
    
    // 处理导出模型到云端事件
    const handleExportToCloud = async () => {
      if (!modelRef.current || !modelTrained) {
        showMessage('请先训练模型', 'error');
        return;
      }
      
      try {
        // 准备元数据
        const metadata = {
          name: `音频分类模型-${new Date().toLocaleDateString()}`,
          description: `包含${classNodes.length}个类别的音频分类模型`,
          classes: classNodes.map(node => node.name),
          sampleRate: 16000, // 定义默认采样率
          audioFormat: 'wav',
          maxAudioLength: 10, // 最大音频长度（秒）
          trainParams,
          timestamp: new Date().toISOString()
        };

        // 调用保存到云端的方法
        await saveModelToCloud(modelRef.current, metadata, {
          isPublic: true
        });
        TrainNotification.success('模型已成功保存到云端');
      } catch (error) {
        TrainNotification.error('保存到云端失败: ' + error.message);
      }
    };

  // 添加一个创建新模型的函数
  const createNewModel = (numClasses) => {
    // 确保numClasses是实际类别数量，而不是最大ID+1
    const actualNumClasses = classNodes.length;
    
    const model = tf.sequential();
    
    model.add(tf.layers.depthwiseConv2d({
      depthMultiplier: 8,
      kernelSize: [NUM_FRAMES, 3],
      activation: 'relu',
      inputShape: INPUT_SHAPE
    }));
    
    model.add(tf.layers.maxPooling2d({
      poolSize: [1, 2],
      strides: [2, 2]
    }));
    
    model.add(tf.layers.flatten());
    
    model.add(tf.layers.dense({
      units: actualNumClasses,
      activation: 'softmax'
    }));

    model.compile({
      optimizer: tf.train.adam(trainParams.learningRate),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  };
  
  // 修改添加类别的函数
  const addClassNode = useCallback(() => {
    // 获取当前类别数量
    const currentClassCount = classNodes.length;
    
    // 限制最多只能添加到十个类别
    if (currentClassCount >= 10) {
      showMessage('最多只能添加10个类别', 'error');
      return;
    }
    
    // 为新类别分配一个ID，确保不与现有类别冲突
    // 找出当前最大的类别ID，新ID为最大ID+1
    const maxId = classNodes.length > 0 ? Math.max(...classNodes.map(node => node.id)) : -1;
    const newId = maxId + 1;
    
    // 更新类别ID映射
    setClassIdMapping(prev => {
      const newMapping = {...prev};
      // 新类别的UI ID映射到模型的新类别索引
      newMapping[newId] = newId;
      ////console.log(`添加新类别，ID ${newId} 映射到模型类别索引 ${newId}`);
      return newMapping;
    });
    
    const newNode = {
      id: newId,
      name: `类别 ${newId + 1}`,
      samples: 0,
      position: { x: 100, y: 100 + currentClassCount * 150 },
      showMenu: false,
      isLoaded: false, // 标记为新添加的类别
      originalIndex: newId // 保存原始索引，对于新类别，与ID相同
    };
    
    setClassNodes(prev => {
      const newNodes = [...prev, newNode];
      // 如果已经有训练好的模型，提示用户需要重新训练
      if (modelTrained) {
        showMessage('已添加新类别，需要重新训练模型', 'info');
      }
      return newNodes;
    });
    
    // 使用setTimeout确保在DOM更新后再调用updateConnections和滚动
    setTimeout(() => {
      // 检查trainMainFunctionRef是否存在且有updateConnections方法
      if (trainMainFunctionRef && trainMainFunctionRef.current && typeof trainMainFunctionRef.current.updateConnections === 'function') {
        try {
        trainMainFunctionRef.current.updateConnections();
        } catch (err) {
          console.error('更新连接线失败:', err);
        }
      }
    }, 300); // 延迟更长时间确保DOM完全更新


    // 添加后自动滚动到容器底部
    setTimeout(() => {
      if (classesPanelContentRef.current) {
        classesPanelContentRef.current.scrollTop = classesPanelContentRef.current.scrollHeight;
      }
    }, 100);
  }, [classNodes, modelTrained, showMessage]);
  
  // 删除类别
  const deleteClass = useCallback((id) => {
    // 防止删除背景噪声类别
    if (id === 0) {
      showMessage('背景噪声类别不能删除', 'error');
      return;
    }
    
    // 删除相关样本
    samplesRef.current = samplesRef.current.filter(s => s.label !== id);
    
    // 更新类别节点
    setClassNodes(prev => prev.filter(node => node.id !== id));
    
    // 更新样本显示
    setSamples(prev => {
      const newSamples = {...prev};
      delete newSamples[id];
      return newSamples;
    });
    
    // 使用setTimeout确保在DOM更新后再调用updateConnections
    setTimeout(() => {
      // 检查trainMainFunctionRef是否存在且有updateConnections方法
      if (trainMainFunctionRef && trainMainFunctionRef.current && typeof trainMainFunctionRef.current.updateConnections === 'function') {
        try {
    trainMainFunctionRef.current.updateConnections();
        } catch (err) {
          console.error('更新连接线失败:', err);
        }
      }
    }, 0);
  }, [showMessage]);
  
  // 重命名类别
  const renameClass = (id, newName) => {
    setClassNodes(prev => 
      prev.map(node => 
        node.id === id ? {...node, name: newName} : node
      )
    );
  };
  
  // 清空指定类别的样本
  const clearSamples = useCallback((id) => {
    // 删除相关样本
    samplesRef.current = samplesRef.current.filter(s => s.label !== id);
    
    // 更新样本显示
    setSamples(prev => {
      const newSamples = {...prev};
      delete newSamples[id];
      return newSamples;
    });
    
    showMessage(`已清空"${classNodes.find(node => node.id === id)?.name || `类别 ${id+1}`}"的所有样本`, 'info');
  }, [classNodes, showMessage]);
  
  
  // 将一个范围的值映射到另一个范围
  const mapRange = (value, inMin, inMax, outMin, outMax) => {
    return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
  };

  // 直接修改重叠系数的方法，不重启预览
  const updateOverlapFactor = (newOverlapFactor) => {
    if (recognizerRef.current && recognizerRef.current.isListening()) {
      // 如果recognizer有内部的_streaming对象（通过代码分析得知）
      if (recognizerRef.current._streaming) {
        console.log("直接修改重叠系数为:", newOverlapFactor);
        // 直接修改内部属性
        recognizerRef.current._streaming.overlapFactor = newOverlapFactor;
        return true;
      }
    }
    return false;
  };

    // 监听重叠系数变化
    useEffect(() => {
      // 只有在预览状态下且值改变时才更新
      if (predicting && recognizerRef.current) {
        console.log("重叠系数已更改为:", overlapFactor);
        
        // 尝试直接修改重叠系数
        const updated = updateOverlapFactor(overlapFactor);
        
        // 如果直接修改失败，才重启预览
        if (!updated) {
          console.log("直接修改失败，需要重启预览");
          // 停止当前预览
          if (recognizerRef.current.isListening()) {
            recognizerRef.current.stopListening().then(() => {
              // 重新启动预览
              const mappedValue = overlapFactor;
              console.log("映射后的重叠系数:", mappedValue);
              
              recognizerRef.current.listen(({spectrogram: {frameSize, data}}) => {
                const vals = normalize(data.subarray(-frameSize * NUM_FRAMES));
                
                // 更新声波数据
                const newAudioData = [...audioDataRef.current.slice(1), 
                  Array.from(data.subarray(-frameSize, -1)).reduce((sum, val) => sum + Math.abs(val + 100) / 100, 0) / frameSize
                ];
                audioDataRef.current = newAudioData;
                setAudioData(newAudioData);
                
                // 确保分析器已存储到全局
                if (recognizerRef.current && recognizerRef.current._analyser && !window.activeAnalyser) {
                  window.activeAnalyser = recognizerRef.current._analyser;
                }
                
                // 转换为张量并进行预测
                const input = tf.tensor(vals).reshape([1, NUM_FRAMES, 232, 1]);
                const probs = modelRef.current.predict(input);
                
                // 获取预测结果
                const classIndex = probs.argMax(-1).dataSync()[0];
                const probValues = probs.dataSync();
                
                // 创建索引到类别ID的映射
                const indexToClassId = {};
                classNodes.forEach((node, index) => {
                  indexToClassId[index] = node.id;
                });
                
                // 将模型输出的索引转换为类别ID
                const predictedClassId = indexToClassId[classIndex];
                
                // 查找对应的类别节点
                const predictedClass = classNodes.find(node => node.id === predictedClassId);
                
                // 更新预测结果
                setPrediction({
                  className: predictedClass?.name || `类别 ${predictedClassId + 1}`,
                  confidence: probValues[classIndex]
                });
                
                // 更新所有类别的置信度
                setConfidences(classNodes.map((node, idx) => ({
                  id: node.id,
                  name: node.name,
                  confidence: probValues[idx]
                })));
                
                // 清理张量
                input.dispose();
                probs.dispose();
              }, {
                overlapFactor: mappedValue,
                includeSpectrogram: true,
                probabilityThreshold: 0.75,
                invokeCallbackOnNoiseAndUnknown: true
              });
            });
          }
        }
      }
    }, [overlapFactor]);
    
  
  // 修改预览函数，处理类别映射
  const startPreview = async () => {
    if (isCollecting) {
      stopCollecting();
    }

    if (recognizerRef.current && recognizerRef.current.isListening()) {
      recognizerRef.current.stopListening();
    }

    try {
      setPredicting(true);
      
      if (recognizerRef.current.isListening()) {
        recognizerRef.current.stopListening();
      }

      // 初始化声波数据
      audioDataRef.current = Array(100).fill(0);
      setAudioData(Array(100).fill(0));

      // 清理之前可能存在的分析器
      if (window.activeAnalyser) {
        delete window.activeAnalyser;
      }

      // 创建音频上下文和分析器
      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioCtx = new AudioContext();
        
        // 创建分析器节点
        const analyser = audioCtx.createAnalyser();
        analyser.fftSize = 2048;
        analyser.smoothingTimeConstant = 0.8;
        
        // 存储分析器到全局变量，供时频图使用
        window.activeAnalyser = analyser;
        console.log("创建了新的分析器节点:", analyser.fftSize);
        
        // 尝试获取麦克风输入
        navigator.mediaDevices.getUserMedia({ audio: true, video: false })
          .then(stream => {
            const source = audioCtx.createMediaStreamSource(stream);
            source.connect(analyser);
            console.log("成功连接麦克风到分析器");
            
            // 存储到全局对象，方便清理
            window.audioStream = stream;
            window.audioSource = source;
            window.audioContext = audioCtx;
          })
          .catch(err => {
            console.error("获取麦克风失败:", err);
            TrainNotification.warning("无法访问麦克风，将使用备用数据源");
          });
      } catch (e) {
        console.error("创建音频上下文失败:", e);
      }

      recognizerRef.current.listen(({spectrogram: {frameSize, data}}) => {
        const vals = normalize(data.subarray(-frameSize * NUM_FRAMES));
        
        // 更新声波数据 - 使用最新的频谱数据
        const newAudioData = [...audioDataRef.current.slice(1), 
          // 使用频谱数据的平均值作为声波振幅
          Array.from(data.subarray(-frameSize, -1)).reduce((sum, val) => sum + Math.abs(val + 100) / 100, 0) / frameSize
        ];
        audioDataRef.current = newAudioData;
        setAudioData(newAudioData);
        
        // 确保分析器已存储到全局
        if (recognizerRef.current && recognizerRef.current._analyser && !window.activeAnalyser) {
          window.activeAnalyser = recognizerRef.current._analyser;
          console.log("从recognizer获取分析器:", recognizerRef.current._analyser);
        }
        
        // 转换为张量并进行预测
        const input = tf.tensor(vals).reshape([1, NUM_FRAMES, 232, 1]);
        const probs = modelRef.current.predict(input);
        
        // 获取预测结果
        const classIndex = probs.argMax(-1).dataSync()[0];
        const probValues = probs.dataSync();
        
        // 创建索引到类别ID的映射
        const indexToClassId = {};
        classNodes.forEach((node, index) => {
          indexToClassId[index] = node.id;
        });
        
        // 将模型输出的索引转换为类别ID
        const predictedClassId = indexToClassId[classIndex];
        
        // 查找对应的类别节点
        const predictedClass = classNodes.find(node => node.id === predictedClassId);
        
        // 更新预测结果
        setPrediction({
          className: predictedClass?.name || `类别 ${predictedClassId + 1}`,
          confidence: probValues[classIndex]
        });
        
        // 更新所有类别的置信度
        setConfidences(classNodes.map((node, idx) => ({
          id: node.id,
          name: node.name,
          confidence: probValues[idx]
        })));
        
        // 清理张量
        input.dispose();
        probs.dispose();
      }, {
        overlapFactor: overlapFactor, // 将UI的值域映射到模型需要的值域
        includeSpectrogram: true,
        probabilityThreshold: 0.75,
        invokeCallbackOnNoiseAndUnknown: true
      });
      
      TrainNotification.info('开始预览');
    } catch (error) {
      TrainNotification.error('启动预览失败: ' + error.message);
      setPredicting(false);
    }
  };
  
  // 修改 stopPreview 函数，清理音频资源
  const stopPreview = () => {
    if (!recognizerRef.current) return;
    
    try {
      // 停止监听
      if (recognizerRef.current.isListening()) {
        recognizerRef.current.stopListening();
      }
      
      // 清理音频资源
      if (window.audioStream) {
        window.audioStream.getTracks().forEach(track => track.stop());
      }
      
      if (window.audioSource) {
        window.audioSource.disconnect();
      }
      
      if (window.audioContext && window.audioContext.state !== 'closed') {
        window.audioContext.close().catch(e => console.error("关闭音频上下文出错:", e));
      }
      
      // 清理全局引用
      delete window.activeAnalyser;
      delete window.audioStream;
      delete window.audioSource;
      delete window.audioContext;
      
      // 更新状态
      setPredicting(false);
      setPrediction(null);
      setConfidences([]);
      setAudioData(Array(100).fill(0)); // 重置声波数据
      
      showMessage('预览已停止', 'success');
    } catch (error) {
      console.error('停止预览失败:', error);
      showMessage('停止预览失败', 'error');
    }
  };
  
  // 收集音频样本 - 仅在用户主动点击录制按钮时触发
  const collect = async (classId) => {
    if (!recognizerRef.current) {
      showMessage('请等待模型加载完成', 'error');
      return;
    }
    
    // 如果正在进行预览，先停止预览
    if (predicting) {
      stopPreview();
    }
    
    // 获取当前类别的录制设置
    const settings = getClassRecordingSettings(classId);
    const recordingTime = settings.recordingTime;
    const sampleRate = 16000; // 定义默认采样率

    // 设置初始倒计时
    setRecordingCountdown(recordingTime);

    // 开始倒计时
    countdownIntervalRef.current = setInterval(() => {
      setRecordingCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownIntervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // 开始新录制时，立即清空该类别的频谱数据
    // 重要：移到这里，确保在设置录制状态之前就清空数据
    allClassesTimeFreqDataRef.current[classId] = Array(64).fill().map(() => 
      new Uint8Array(100).fill(0)
    );

    // 原有的录制逻辑
    setCurrentClass(classId);
    setIsCollecting(true);
    isStoppingRef.current = false; // 重置停止状态
    setDuration(0); // 重置音频时长

    try {
      // 如果当前已经在录制这个类别，点击则停止录制
      if (isCollecting && currentClass === classId) {
        stopCollecting();
        return;
      }
      
      // 如果正在录制其他类别，先停止当前录制
      if (isCollecting) {
        stopCollecting();
      }
      
      // 清空该类别的部分已移到前面，确保始终执行
      
      // 重置recordStartTimeRef，确保每次录制都是从0开始计时
      recordStartTimeRef.current = Date.now();
      sampleBatchRef.current = [];
      
      // 初始化临时声波数据
      tempAudioDataRef.current = Array(100).fill(0);
      setTempAudioData(Array(100).fill(0));
      
      // 初始化音频数据 - 按节点ID存储
      audioChunksRef.current[classId] = [];
      
      // 创建音频上下文和分析器
      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        const audioCtx = new AudioContext({sampleRate: sampleRate});
        
        // 创建分析器节点
        const analyser = audioCtx.createAnalyser();
        analyser.fftSize = 2048;
        analyser.smoothingTimeConstant = 0.8;
        
        // 存储分析器到全局变量，供时频图使用
        window.activeAnalyser = analyser;
        console.log("创建了新的分析器节点:", analyser.fftSize);
      
        // 获取麦克风权限并创建MediaRecorder
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          mediaRecorderRef.current = new MediaRecorder(stream);
          
          // 连接麦克风到分析器
          const source = audioCtx.createMediaStreamSource(stream);
          source.connect(analyser);
          console.log("成功连接麦克风到分析器");
          
          // 存储到全局对象，方便清理
          window.audioStream = stream;
          window.audioSource = source;
          window.audioContext = audioCtx;
        } catch (micError) {
          console.error("麦克风访问失败:", micError);
          TrainNotification.error('无法访问麦克风，请检查设备权限设置。');
          setIsCollecting(false);
          setCurrentClass(-1);
          setIsListening(false);
          return; // 提前退出函数，避免后续代码执行
        }
      } catch (e) {
        console.error("创建音频上下文失败:", e);
        TrainNotification.error('无法初始化音频系统，请确认浏览器支持音频录制功能。');
        setIsCollecting(false);
        setCurrentClass(-1);
        setIsListening(false);
        return; // 提前退出函数，避免后续代码执行
      }
      
      // 只有在成功创建 mediaRecorderRef.current 后才会执行这里
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          // 确保当前节点的数组已初始化
          if (!audioChunksRef.current[classId]) {
            audioChunksRef.current[classId] = [];
          }
          audioChunksRef.current[classId].push(event.data);
        }
      };
      
      // 录制完成事件
      mediaRecorderRef.current.onstop = () => {
        // 创建临时的音频元素来获取准确的时长
        if (audioChunksRef.current[classId] && audioChunksRef.current[classId].length > 0) {
          const audioBlob = new Blob(audioChunksRef.current[classId], { type: 'audio/wav' });
          const audioUrl = URL.createObjectURL(audioBlob);
          const tempAudio = new Audio(audioUrl);
          
          tempAudio.onloadedmetadata = () => {
            if (tempAudio.duration && !isNaN(tempAudio.duration) && isFinite(tempAudio.duration)) {
              // 设置准确的时长
              setDuration(tempAudio.duration);
            }
            URL.revokeObjectURL(audioUrl);
          };
        }
      };
      
      // 开始录制，指定每秒获取一次数据，确保可以按秒切分
      mediaRecorderRef.current.start(1000);
      
      // 记录当前秒数
      let currentSecond = -1;
      
      // 同时开始收集频谱数据用于显示波形
      await recognizerRef.current.listen(({spectrogram: {frameSize, data}}) => {
        const now = Date.now();
        const elapsedTime = now - recordStartTimeRef.current;
        const second = Math.floor(elapsedTime / 1000);
        
        const vals = normalize(data.subarray(-frameSize * NUM_FRAMES));
        
        // 更新临时声波数据
        const newAudioData = [...tempAudioDataRef.current.slice(1), 
          Array.from(data.subarray(-frameSize, -1)).reduce((sum, val) => sum + Math.abs(val + 100) / 100, 0) / frameSize
        ];
        tempAudioDataRef.current = newAudioData;
        setTempAudioData(newAudioData);
        
        // 存储频谱数据用于后续处理
        fullAudioDataRef.current[classId] = fullAudioDataRef.current[classId] || [];
        fullAudioDataRef.current[classId].push({
          vals,
          time: elapsedTime,
          second,
          waveform: [...newAudioData]
        });
        
        // 更新当前秒数
        if (second > currentSecond) {
          currentSecond = second;
          setCurrentTime(second);
          
          // 如果已经请求停止并且已经完成当前秒的数据收集，则停止
          if (isStoppingRef.current) {
            stopCollecting(true);
            return;
          }
          
          // 检查是否已达到或超过录制时间
          if (second >= settings.recordingTime) {
            isStoppingRef.current = true;
            stopCollecting(true);
            return;
          }
        }
        
        // 根据设置的录制时间自动停止
        if (elapsedTime >= settings.recordingTime * 1000) {
          isStoppingRef.current = true; // 标记为请求停止，但等待当前秒完成
        }
      }, {
        overlapFactor: 0.999,
        includeSpectrogram: true,
        invokeCallbackOnNoiseAndUnknown: true
      });
      
      setIsListening(true);
      TrainNotification.info('开始采集样本');
    } catch (error) {
      TrainNotification.error('开始收集样本失败: ' + error.message);
      setIsCollecting(false);
      setCurrentClass(-1);
      setIsListening(false);
    }
  };
  
  // 停止收集
  const stopCollecting = async (forceStop = false) => {
    // 如果不是强制停止，且没有请求停止，则标记为正在停止
    if (!forceStop && !isStoppingRef.current) {
      isStoppingRef.current = true;
      // 添加一个超时机制，确保不会等待太久
      setTimeout(() => {
        if (isStoppingRef.current && isCollecting) {
          console.log('停止录制超时，强制停止');
          stopCollecting(true);
        }
      }, 300); // 300毫秒后如果还在停止状态，则强制停止
      return; // 让录制继续直到当前秒完成或超时
    }
    
    // 清除倒计时
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
      setRecordingCountdown(0);
    }

    if (!recognizerRef.current) return;
    
    try {
      // 停止频谱数据收集
      if (recognizerRef.current.isListening()) {
        await recognizerRef.current.stopListening();
      }
      
      // 计算实际录制时长（毫秒）
      const actualDuration = Date.now() - recordStartTimeRef.current;
      // 转换为秒并设置时长（保留两位小数）
      setDuration(actualDuration / 1000);
      
      // 停止音频录制
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      }
      
      // 清理音频上下文和分析器
      if (window.audioContext) {
        try {
          window.audioContext.close();
        } catch (e) {
          console.error('关闭音频上下文失败:', e);
        }
      }
      
      // 清理音频流
      if (window.audioStream) {
        window.audioStream.getTracks().forEach(track => track.stop());
      }
      
      // 清理全局分析器引用
      if (window.activeAnalyser) {
        console.log('清理全局分析器引用');
        delete window.activeAnalyser;
      }
      
      if (window.audioSource) {
        delete window.audioSource;
      }
      
      if (window.audioContext) {
        delete window.audioContext;
      }
      
      if (window.audioStream) {
        delete window.audioStream;
      }
      
      // 更新完整音频数据状态
      setFullAudioData(prev => ({
        ...prev,
        [currentClass]: fullAudioDataRef.current[currentClass] || []
      }));
      
      // 修改这里：设置为非录制状态，但不改变currentClass
      // 这样可以保持面板展开状态和保留当前激活的类别
      setIsCollecting(false);
      setIsListening(false);
      
      // 保留画布内容，不做其他清理，让画布保持最后一帧的状态
      
      TrainNotification.success('样本录制完成');
    } catch (error) {
      console.error('停止录制失败:', error);
      TrainNotification.error('停止录制失败: ' + error.message);
      
      // 即使出错，也要尝试清理状态，但仍然保持面板展开
      setIsCollecting(false);
      setIsListening(false);
      // 不重置currentClass，保持面板展开
    }
  };
  
  // 完全重写训练函数 
  const train = async () => {
    if (isTraining) return; // 如果正在训练，直接返回
    
    try {
      setIsTraining(true); // 开始训练，设置状态为true
      TrainNotification.info('开始训练模型...');
      
      if (samplesRef.current.length === 0) {
        showMessage('没有足够的样本进行训练', 'error');
        return;
      }

      // 检查并停止所有可能的监听状态
      if (isCollecting) {
        await stopCollecting();
      }

      if (predicting) {
        stopPreview();
      }

      // 重置训练历史记录
      setTrainingHistory({
        epochs: [],
        trainAccuracy: [],
        trainLoss: [],
        valAccuracy: [],
        valLoss: []
      });
      // 确保recognizer不在监听状态
      if (recognizerRef.current && recognizerRef.current.isListening()) {
        try {
          await recognizerRef.current.stopListening();
        } catch (error) {
          console.error('停止监听失败:', error);
          // 继续训练流程，不要让错误阻止训练
        }
      }

      setTraining(0);

      // 获取当前所有类别节点
      const currentClassNodes = classNodes;
      const numClasses = currentClassNodes.length;
      
      // 创建类别ID到索引的映射
      const classIdToIndex = {};
      currentClassNodes.forEach((node, index) => {
        classIdToIndex[node.id] = index;
      });
      
      ////console.log('当前类别节点:', currentClassNodes.map(n => `${n.id}:${n.name}(${n.isLoaded ? '已导入' : '新类别'})`));
      ////console.log('类别ID映射:', classIdMapping);
      ////console.log('类别ID到索引映射:', classIdToIndex);
      
      // 区分导入的类别和新添加的类别
      const importedClasses = currentClassNodes.filter(node => node.isLoaded);
      const newClasses = currentClassNodes.filter(node => !node.isLoaded);
      
      ////console.log('导入的类别:', importedClasses.map(c => `${c.id}:${c.name}`));
      ////console.log('新添加的类别:', newClasses.map(c => `${c.id}:${c.name}`));
      
      // 检查是否存在导入的模型和类别
      const hasImportedModel = modelRef.current !== null && importedClasses.length > 0;
      
      if (hasImportedModel && newClasses.length > 0) {
        // ===== 迁移学习场景：有导入模型且添加了新类别 =====
        ////console.log('检测到迁移学习场景：导入模型 + 新类别');
        
        // 1. 准备训练样本，处理类别ID映射
        
        // 处理新收集的样本
        const rawSamples = samplesRef.current;
        ////console.log(`读取到 ${rawSamples.length} 个原始样本`);
        
        // 获取新类别ID列表
        const newClassIds = newClasses.map(node => node.id);
        ////console.log('新类别ID列表:', newClassIds);
        
        // 筛选出新类别的样本
        const newClassSamples = rawSamples.filter(s => newClassIds.includes(s.label));
        ////console.log(`新类别样本数量: ${newClassSamples.length}`);
        
        if (newClassSamples.length === 0) {
          showMessage('需要为新类别添加样本', 'error');
          setTraining(-1);
          setIsTraining(false);
          return;
        }
        
        // 2. 准备创建新模型
        
        // 获取原模型引用
        const oldModel = modelRef.current;
        const oldNumClasses = importedClasses.length;
        
        ////console.log('原模型类别数:', oldNumClasses);
        ////console.log('新模型总类别数:', numClasses);
        
        // 3. 创建新模型 - 完全重建
        
        // 创建一个新的模型，类别数量为所有类别的总数
        let newModel = tf.sequential();
        
        // 复制原模型的结构（除了输出层）
        newModel.add(tf.layers.depthwiseConv2d({
          depthMultiplier: 8,
          kernelSize: [NUM_FRAMES, 3],
          activation: 'relu',
          inputShape: [NUM_FRAMES, 232, 1],
          name: 'conv_layer'
        }));
        
        newModel.add(tf.layers.maxPooling2d({
          poolSize: [1, 2],
          strides: [2, 2],
          name: 'pooling_layer'
        }));
        
        newModel.add(tf.layers.flatten({
          name: 'flatten_layer'
        }));
        
        // 添加新的输出层，输出单元数量为总类别数
        newModel.add(tf.layers.dense({
          units: numClasses,
          activation: 'softmax',
          name: 'new_output_layer'
        }));
        
        // 4. 复制原模型权重到新模型
        
        try {
          // 获取原模型权重和新模型权重，但先不释放它们
          const oldWeights = oldModel.getWeights();
          const newWeights = newModel.getWeights();
          
          ////console.log('原模型权重数量:', oldWeights.length);
          ////console.log('新模型权重数量:', newWeights.length);
          
          // 创建一个数组来存储新的权重张量
          const updatedWeights = new Array(newWeights.length);
          
          // 首先复制所有非输出层权重
          ////console.log('开始复制非输出层权重...');
          
          // 复制卷积层和池化层权重
          for (let i = 0; i < oldWeights.length - 2; i++) {
            if (i < newWeights.length - 2) {
              const oldShape = oldWeights[i].shape;
              const newShape = newWeights[i].shape;
              
              ////console.log(`处理第 ${i} 层权重, 旧形状: [${oldShape}], 新形状: [${newShape}]`);
              
              if (oldShape.length === newShape.length && 
                  oldShape.every((dim, idx) => dim === newShape[idx])) {
                // 形状匹配，创建权重副本
                updatedWeights[i] = tf.clone(oldWeights[i]);
                ////console.log(`- 已复制第 ${i} 层权重`);
              } else {
                // 形状不匹配，使用新权重
                updatedWeights[i] = tf.clone(newWeights[i]);
                ////console.log(`- 使用新初始化的第 ${i} 层权重`);
              }
            }
          }
          
          // 然后处理输出层权重
          ////console.log('开始处理输出层权重...');
          
          // 获取输出层的形状
          const [inputSize, outputSize] = newWeights[newWeights.length - 2].shape;
          ////console.log(`输出层形状: [${inputSize}, ${outputSize}]`);
          
          // 创建新的输出层权重
          const newOutputWeights = tf.buffer([inputSize, outputSize]);
          const newOutputBias = tf.buffer([outputSize]);
          
          // 获取旧权重数据
          const oldOutputWeights = oldWeights[oldWeights.length - 2];
          const oldOutputBias = oldWeights[oldWeights.length - 1];
          
          // 将旧权重数据转换为数组（一次性操作）
          const oldOutputWeightsData = oldOutputWeights.arraySync();
          const oldOutputBiasData = oldOutputBias.arraySync();
          
          // 复制导入类别的权重
          for (const importedClass of importedClasses) {
            const oldIndex = importedClass.originalIndex;
            const newIndex = importedClass.id;
            
            ////console.log(`复制类别 ${importedClass.name} 的权重: ${oldIndex} -> ${newIndex}`);
            
            if (oldIndex < oldOutputWeightsData.length && newIndex < outputSize) {
              // 复制权重
              for (let i = 0; i < inputSize; i++) {
                newOutputWeights.set(oldOutputWeightsData[i][oldIndex], i, newIndex);
              }
              
              // 复制偏置
              if (oldIndex < oldOutputBiasData.length) {
                newOutputBias.set(oldOutputBiasData[oldIndex], newIndex);
              }
            }
          }
          
          // 创建最终的输出层权重张量
          updatedWeights[newWeights.length - 2] = newOutputWeights.toTensor();
          updatedWeights[newWeights.length - 1] = newOutputBias.toTensor();
          
          ////console.log('成功创建所有新权重张量');
          
          // 在设置新权重之前，确保所有权重都已创建
          if (updatedWeights.every(w => w !== undefined)) {
            // 释放旧权重
            ////console.log('释放旧权重...');
            newWeights.forEach(w => {
              if (w && !w.isDisposed) {
                w.dispose();
              }
            });
            
            // 设置新权重
            ////console.log('设置新权重到模型...');
            await newModel.setWeights(updatedWeights);
            
            // 释放原始权重
            ////console.log('释放原始权重...');
            oldWeights.forEach(w => {
              if (w && !w.isDisposed) {
                w.dispose();
              }
            });
            
            ////console.log('成功完成权重转移');
          } else {
            throw new Error('某些权重未能成功创建');
          }
          
        } catch (error) {
          console.error('权重处理过程中出错:', error);
          console.error('错误堆栈:', error.stack);
          
          // 如果出错，创建一个全新的模型
          ////console.log('创建新的模型...');
          
          // 释放失败的模型
          if (newModel) {
            try {
              newModel.dispose();
            } catch (e) {
              console.warn('释放失败的模型时出错:', e);
            }
          }
          
          // 创建新模型
          newModel = tf.sequential();
          
          newModel.add(tf.layers.depthwiseConv2d({
            depthMultiplier: 8,
            kernelSize: [NUM_FRAMES, 3],
            activation: 'relu',
            inputShape: [NUM_FRAMES, 232, 1],
            name: 'conv_layer'
          }));
          
          newModel.add(tf.layers.maxPooling2d({
            poolSize: [1, 2],
            strides: [2, 2],
            name: 'pooling_layer'
          }));
          
          newModel.add(tf.layers.flatten({
            name: 'flatten_layer'
          }));
          
          newModel.add(tf.layers.dense({
            units: numClasses,
            activation: 'softmax',
            name: 'new_output_layer'
          }));
          
          ////console.log('新模型创建完成');
        }
        
        // 5. 编译新模型
        ////console.log('编译模型...');
        newModel.compile({
          optimizer: tf.train.adam(trainParams.learningRate),
          loss: 'categoricalCrossentropy',
          metrics: ['accuracy']
        });
        
        // 6. 创建记忆样本，帮助模型记住原有类别
        
        // 准备生成记忆样本
        const memoryData = [];
        
        // 使用更可靠的方法生成记忆样本
        ////console.log('为原有类别生成记忆样本...');
        
        try {
          // 对每个导入的类别，生成记忆样本
          for (const importedClass of importedClasses) {
            const classId = importedClass.id;
            ////console.log(`为类别 ${importedClass.name} (ID: ${classId}) 生成记忆样本`);
            
            // 为每个类别创建多个记忆样本
            const samplesPerClass = 60;  // 进一步增加记忆样本数量
            
            // 创建一个简单的随机特征生成函数
            const generateFeatures = () => {
              const features = [];
              for (let j = 0; j < NUM_FRAMES * 232; j++) {
                // 使用更精细的随机特征生成
                features.push((Math.random() - 0.5) * 0.05); // 减小随机范围提高稳定性
              }
              return features;
            };
            
            // 生成样本并添加到记忆数据
            for (let i = 0; i < samplesPerClass; i++) {
              const features = generateFeatures();
              
              memoryData.push({
                vals: features,
                label: classId,
                isMemory: true
              });
            }
          }
          
          ////console.log(`成功生成 ${memoryData.length} 个记忆样本`);
          
        } catch (memoryError) {
          console.error('生成记忆样本时出错:', memoryError);
          ////console.log('将继续不使用记忆样本进行训练');
        }
        
        // 7. 合并记忆样本和新类别样本
        
        // 为平衡训练数据，我们调整样本数量
        let combinedSamples = [];
        
        // 如果有记忆样本，进行合并
        if (memoryData.length > 0) {
          // 为增强原有类别的学习效果，我们将为记忆样本设置合理数量
          // 先添加所有记忆样本
          combinedSamples = [...memoryData];
          
          // 计算新类别样本的重复次数，使得新类别样本总数约为记忆样本的一半
          // 这有助于保持原类别知识，同时学习新类别
          const repeatCount = Math.max(1, Math.min(5, Math.floor(memoryData.length / (2 * newClassSamples.length))));
          
          ////console.log(`新类别样本重复次数: ${repeatCount}`);
          
          // 添加新类别样本，并根据计算重复
          for (const sample of newClassSamples) {
            for (let i = 0; i < repeatCount; i++) {
              combinedSamples.push(sample);
            }
          }
        } else {
          // 如果没有成功生成记忆样本，只使用新类别样本
          combinedSamples = [...newClassSamples];
        }
        
        // 随机打乱样本顺序，提升训练效果
        combinedSamples = shuffleArray(combinedSamples);
        
        ////console.log(`训练样本总数: ${combinedSamples.length}`);
        ////console.log(`- 记忆样本: ${memoryData.length}`);
        ////console.log(`- 新类别样本: ${newClassSamples.length * (memoryData.length > 0 ? Math.floor(memoryData.length / (2 * newClassSamples.length)) : 1)}`);
        
        // 8. 创建训练数据张量
        
        // 创建输入数据
        const xs = tf.tensor(combinedSamples.map(s => s.vals))
          .reshape([combinedSamples.length, NUM_FRAMES, 232, 1]);
          
        // 创建标签数据 - 使用classIdToIndex映射将类别ID转换为连续索引
        const ys = tf.oneHot(
          tf.tensor1d(combinedSamples.map(s => classIdToIndex[s.label]), 'int32'),
          numClasses
        );
        
        // 9. 训练模型 - 两阶段训练
        
        // 监控训练开始前的张量数量
        logTensorCount('训练开始前');
        
        // 第一阶段：冻结特征提取层，仅训练输出层
        ////console.log('训练第1阶段: 冻结特征提取层，仅训练输出层');
        
        // 冻结除了输出层之外的所有层
        for (let i = 0; i < newModel.layers.length - 1; i++) {
          newModel.layers[i].trainable = false;
        }
        
        // 重新编译模型
        newModel.compile({
          optimizer: tf.train.adam(trainParams.learningRate),
          loss: 'categoricalCrossentropy',
          metrics: ['accuracy']
        });
        
        try {
          // 训练第一阶段
          const epochs1 = Math.max(40, trainParams.epochs * 2);
          await newModel.fit(xs, ys, {
            batchSize: Math.min(32, combinedSamples.length),
            epochs: epochs1,
            callbacks: {
              onEpochBegin: (epoch) => {
                if (epoch === 0 || epoch === epochs1-1) {
                  logTensorCount(`第1阶段 周期${epoch+1}开始前`);
                }
              },
              onEpochEnd: (epoch, logs) => {
                const progress = (epoch + 1) / (epochs1 + epochs1/2);
                setTraining(progress);
                
                // 存储训练数据，使用函数式更新确保使用最新状态
                setTrainingHistory(prev => ({
                  epochs: [...prev.epochs, `第1阶段-${epoch + 1}`],
                  trainAccuracy: [...prev.trainAccuracy, logs.acc],
                  trainLoss: [...prev.trainLoss, logs.loss],
                  valAccuracy: [...prev.valAccuracy, logs.val_acc || 0],
                  valLoss: [...prev.valLoss, logs.val_loss || 0]
                }));
                
                // 每10个周期输出一次日志
                if ((epoch + 1) % 10 === 0 || epoch === 0 || epoch === epochs1-1) {
                  ////console.log(`第1阶段训练周期 ${epoch + 1}/${epochs1}, 精确度: ${logs.acc}, 损失: ${logs.loss}`);
                  if (epoch === epochs1-1) {
                    logTensorCount(`第1阶段 周期${epoch+1}结束后`);
                  }
                }
              }
            },
            // 添加验证分割，以监控过拟合
            validationSplit: 0.1
          });
          
          ////console.log('第1阶段训练完成，准备第2阶段');
        } catch (phase1Error) {
          console.error('第1阶段训练失败:', phase1Error);
          // 即使第1阶段失败，我们也尝试第2阶段
          ////console.log('尝试继续第2阶段训练');
        }
        
        // 监控第一阶段后的张量数量
        logTensorCount('第1阶段后');
        
        try {
          // 第二阶段：解冻所有层，使用较小的学习率微调整个网络
          ////console.log('训练第2阶段: 解冻所有层，微调整个网络');
          
          // 解冻所有层
          for (let i = 0; i < newModel.layers.length; i++) {
            newModel.layers[i].trainable = true;
          }
          
          // 重新编译模型，使用较小的学习率
          newModel.compile({
            optimizer: tf.train.adam(trainParams.learningRate / 25),  // 使用更小的学习率
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
          });
          
          // 训练第二阶段 - 使用较少的周期
          const epochs2 = Math.round(40);
          await newModel.fit(xs, ys, {
            batchSize: Math.min(32, combinedSamples.length),
            epochs: epochs2,
            callbacks: {
              onEpochBegin: (epoch) => {
                if (epoch === 0 || epoch === epochs2-1) {
                  logTensorCount(`第2阶段 周期${epoch+1}开始前`);
                }
              },
              onEpochEnd: (epoch, logs) => {
                const progress = (40 + epoch + 1) / (40 + epochs2);
                setTraining(progress);
                
                // 存储训练数据，使用函数式更新确保使用最新状态
                setTrainingHistory(prev => ({
                  epochs: [...prev.epochs, `第2阶段-${epoch + 1}`],
                  trainAccuracy: [...prev.trainAccuracy, logs.acc],
                  trainLoss: [...prev.trainLoss, logs.loss],
                  valAccuracy: [...prev.valAccuracy, logs.val_acc || 0],
                  valLoss: [...prev.valLoss, logs.val_loss || 0]
                }));
                
                // 每5个周期输出一次日志
                if ((epoch + 1) % 5 === 0 || epoch === 0 || epoch === epochs2-1) {
                  ////console.log(`第2阶段训练周期 ${epoch + 1}/${epochs2}, 精确度: ${logs.acc}, 损失: ${logs.loss}`);
                  if (epoch === epochs2-1) {
                    logTensorCount(`第2阶段 周期${epoch+1}结束后`);
                  }
                }
              }
            },
            // 添加验证分割，以监控过拟合
            validationSplit: 0.1
          });
          
          ////console.log('第2阶段训练完成');
        } catch (phase2Error) {
          console.error('第2阶段训练失败:', phase2Error);
          ////console.log('将使用第1阶段的训练结果');
        }
        
        // 监控训练结束后的张量数量
        logTensorCount('训练结束后');
        
        // 清理张量
        try {
          if (xs) safeDispose(xs);
          if (ys) safeDispose(ys);
        } catch (disposeError) {
          console.warn('清理张量时发生错误:', disposeError);
        }
        
        // 10. 保存模型并更新状态
        modelRef.current = newModel;
        setModelTrained(true);
        
        ////console.log('迁移学习训练完成');
        TrainNotification.success('迁移学习训练完成，已保留原有类别知识');
        
        
        // 自动开启麦克风预测
        startPreview();
        
      } else {
        // ===== 标准训练场景：没有导入模型或没有新类别 =====
        
        // 准备训练数据
        const samples = samplesRef.current;
        ////console.log(`标准训练: ${samples.length} 个样本`);
        
        // 如果模型不存在或类别数量与当前不匹配，创建新模型
        if (!modelRef.current || modelRef.current.outputs[0].shape[1] !== numClasses) {
          ////console.log('创建新模型');
          
          // 创建新模型
          const model = tf.sequential();
          
          model.add(tf.layers.depthwiseConv2d({
            depthMultiplier: 8,
            kernelSize: [NUM_FRAMES, 3],
            activation: 'relu',
            inputShape: [NUM_FRAMES, 232, 1]
          }));
          
          model.add(tf.layers.maxPooling2d({
            poolSize: [1, 2],
            strides: [2, 2]
          }));
          
          model.add(tf.layers.flatten());
          
          model.add(tf.layers.dense({
            units: numClasses,
            activation: 'softmax'
          }));
          
          model.compile({
            optimizer: tf.train.adam(trainParams.learningRate),
            loss: 'categoricalCrossentropy',
            metrics: ['accuracy']
          });
          
          modelRef.current = model;
        }
        
        // 创建输入数据
        const xs = tf.tensor(samples.map(s => s.vals))
          .reshape([samples.length, NUM_FRAMES, 232, 1]);
          
        // 创建标签数据
        const ys = tf.oneHot(
          tf.tensor1d(samples.map(s => s.label), 'int32'),
          numClasses
        );
        
        // 训练模型
        await modelRef.current.fit(xs, ys, {
          batchSize: trainParams.batchSize,
          epochs: trainParams.epochs,
          callbacks: {
            onEpochEnd: (epoch, logs) => {
              setTraining((epoch + 1) / trainParams.epochs);
              
              // 存储训练数据，使用函数式更新确保使用最新状态
              setTrainingHistory(prev => ({
                epochs: [...prev.epochs, epoch + 1],
                trainAccuracy: [...prev.trainAccuracy, logs.acc],
                trainLoss: [...prev.trainLoss, logs.loss],
                valAccuracy: [...prev.valAccuracy, logs.val_acc || 0],
                valLoss: [...prev.valLoss, logs.val_loss || 0]
              }));
              
              // 每10个周期或最后一个周期输出一次日志
              if ((epoch + 1) % 10 === 0 || epoch === trainParams.epochs - 1) {
                ////console.log(`标准训练周期 ${epoch + 1}/${trainParams.epochs}, 精确度: ${logs.acc}, 损失: ${logs.loss}`);
              }
            }
          },
          validationSplit: 0.1
        });
        
        // 清理张量
        xs.dispose();
        ys.dispose();
        
        setModelTrained(true);
        TrainNotification.success('标准训练完成');
        
        
        // 自动开启麦克风预测
        startPreview();
      }
      
      setTraining(-1);
      
      TrainNotification.success('模型训练完成');
    } catch (error) {
      TrainNotification.error('训练失败: ' + error.message);
      console.error('训练失败:', error);
      console.error('错误堆栈:', error.stack);
      setTraining(-1);
    } finally {
      setIsTraining(false); // 无论成功失败，训练结束后设置状态为false
    }
  };
  
  
  // 修改WAV文件预测函数
  // const predictWavFile = async (file) => {
  //   if (!modelRef.current || !modelTrained) {
  //     showMessage('请先训练或加载模型', 'error');
  //     return;
  //   }

  //   try {
  //     setWavPredicting(true);
  //     setPredicting(true); // 设置为预测状态，以显示声波
  //     showMessage('正在处理音频文件...', 'info');

  //     // 初始化声波数据
  //     audioDataRef.current = Array(100).fill(0);
  //     setAudioData(Array(100).fill(0));

  //     // 清理之前可能存在的分析器
  //     if (window.activeAnalyser) {
  //       delete window.activeAnalyser;
  //     }

  //     // 创建音频上下文
  //     const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  //     const fileBuffer = await file.arrayBuffer();
  //     const audioBuffer = await audioContext.decodeAudioData(fileBuffer);
      
  //     // 创建音频源和分析器
  //     const source = audioContext.createBufferSource();
  //     const analyser = audioContext.createAnalyser();
  //     analyser.fftSize = 2048;
      
  //     // 将分析器存储到全局对象，以便于时频图使用
  //     window.activeAnalyser = analyser;
      
  //     // 连接节点
  //     source.buffer = audioBuffer;
  //     source.connect(analyser);
  //     analyser.connect(audioContext.destination);
      
  //     // 创建数据缓冲区
  //     const frameSize = 232;
  //     const frames = [];
  //     let animationFrameId = null;
      
  //     // 预测函数
  //     const predict = (timestamp) => {
  //       // 获取频谱数据
  //       const data = new Float32Array(frameSize);
  //       analyser.getFloatFrequencyData(data);
        
  //       // 更新声波数据
  //       const newAudioData = [...audioDataRef.current.slice(1), 
  //         // 使用频谱数据的平均值作为声波振幅
  //         Array.from(data).reduce((sum, val) => sum + Math.abs(val + 100) / 100, 0) / frameSize
  //       ];
  //       audioDataRef.current = newAudioData;
  //       setAudioData(newAudioData);
        
  //       // 更新帧缓冲区
  //       frames.push(data);
  //       if (frames.length > NUM_FRAMES) {
  //         frames.shift();
  //       }
        
  //       // 当收集到足够的帧数时进行预测
  //       if (frames.length === NUM_FRAMES) {
  //         // 准备数据进行预测
  //         const vals = normalize(flatten(frames));
  //         const input = tf.tensor(vals).reshape([1, NUM_FRAMES, 232, 1]);
  //         const probs = modelRef.current.predict(input);
          
  //         // 获取预测结果
  //         const classIndex = probs.argMax(-1).dataSync()[0];
  //         const probValues = probs.dataSync();
          
  //         // 创建索引到类别ID的映射
  //         const indexToClassId = {};
  //         classNodes.forEach((node, index) => {
  //           indexToClassId[index] = node.id;
  //         });
          
  //         // 将模型输出的索引转换为类别ID
  //         const predictedClassId = indexToClassId[classIndex];
          
  //         // 查找对应的类别节点
  //         const predictedClass = classNodes.find(node => node.id === predictedClassId);
          
  //         // 更新预测结果
  //         setPrediction({
  //           className: predictedClass?.name || `类别 ${predictedClassId + 1}`,
  //           confidence: probValues[classIndex]
  //         });
          
  //         // 更新所有类别的置信度
  //         setConfidences(classNodes.map((node, idx) => ({
  //           id: node.id,
  //           name: node.name,
  //           confidence: probValues[idx]
  //         })));
          
  //         // 清理张量
  //         input.dispose();
  //         probs.dispose();
  //       }
        
  //       // 如果音频还在播放，继续预测
  //       if (!source.playbackTime || source.playbackTime < audioBuffer.duration) {
  //         animationFrameId = requestAnimationFrame(predict);
  //       } else {
  //         // 清理资源
  //         cancelAnimationFrame(animationFrameId);
  //         if (window.activeAnalyser === analyser) {
  //           delete window.activeAnalyser;
  //         }
  //         audioContext.close();
  //         setWavPredicting(false);
  //         setPredicting(false); // 停止预测状态
  //         setAudioData(Array(100).fill(0)); // 重置声波数据
  //         showMessage('预测完成', 'success');
  //       }
  //     };
      
  //     // 开始播放和预测
  //     source.start(0);
  //     animationFrameId = requestAnimationFrame(predict);
      
  //     // 监听播放结束
  //     source.onended = () => {
  //       if (animationFrameId) {
  //         cancelAnimationFrame(animationFrameId);
  //       }
  //       if (window.activeAnalyser === analyser) {
  //         delete window.activeAnalyser;
  //       }
  //       audioContext.close();
  //       setWavPredicting(false);
  //       setPredicting(false); // 停止预测状态
  //       setAudioData(Array(100).fill(0)); // 重置声波数据
  //       showMessage('预测完成', 'success');
  //     };

  //   } catch (error) {
  //     console.error('预测WAV文件失败:', error);
  //     showMessage(error.message, 'error');
  //     if (window.activeAnalyser) {
  //       delete window.activeAnalyser;
  //     }
  //     setWavPredicting(false);
  //     setPredicting(false); // 停止预测状态
  //     setAudioData(Array(100).fill(0)); // 重置声波数据
  //   }
  // };
  
  // 辅助函数：归一化数据
  const normalize = (x) => {
    const mean = -100;
    const std = 10;
    return Array.from(x).map(val => (val - mean) / std);
  };
  
  // 展平张量
  const flatten = (tensors) => {
    const size = tensors[0].length;
    const result = new Float32Array(tensors.length * size);
    tensors.forEach((arr, i) => result.set(arr, i * size));
    return result;
  };
  
  // 删除样本
  const deleteSample = (classId, sampleId) => {
    try {
      // 更新样本显示
      setSamples(prev => {
        const classSamples = prev[classId] || [];
        return {
          ...prev,
          [classId]: classSamples.filter(s => (s.id || s.second) !== sampleId)
        };
      });
      
      // 更新样本数据
      if (sampleId.includes && sampleId.includes('-')) {
        // 使用ID删除样本
        samplesRef.current = samplesRef.current.filter(s => s.id !== sampleId);
      } else {
        // 兼容旧版本，使用秒数删除样本
        const second = sampleId;
        const startTime = (second - 1) * 1000;
        const endTime = second * 1000;
        samplesRef.current = samplesRef.current.filter(
          s => !(s.label === classId && s.time >= startTime && s.time <= endTime)
        );
      }
      
      // 更新类别节点的样本数量
      setClassNodes(prev => {
        return prev.map(node => {
          if (node.id === classId) {
            const count = samplesRef.current.filter(s => s.label === classId).length;
            return {...node, samples: count};
          }
          return node;
        });
      });
      TrainNotification.success('样本已删除');
    } catch (error) {
      TrainNotification.error('删除样本失败: ' + error.message);
    }
  };
  
  // 添加导出模型到积木的函数
  const exportModelToBlocks = async () => {
    try {
      // 确保存在模型且有分类
      if (!modelRef.current || !modelTrained || classNodes.length === 0) {
        showMessage('请先训练模型', 'error');
        return false;
      }

      // 准备全局模型数据结构
      const modelClasses = classNodes.map(node => node.name || `类别${node.id}`);
      // 使用当前模型的引用
      const model = modelRef.current;
      
      // 准备模型元数据
      const metadata = {
        classes: modelClasses,
        timestamp: new Date().toISOString(),
        numFrames: 3,
        modelInfo: {
          type: 'sound',
          version: '1.0',
          trainedWith: 'LogicLeap Sound Train',
          classCount: modelClasses.length
        }
      };
      
      // 确保父实例存在
      const parentInstance = window._logicleapSoundTrainInstance;
      if (!parentInstance) {
        showMessage('无法获取积木实例，无法将模型保存到积木', 'warning');
        return false;
      }
      
      // 显示加载中提示
      showMessage('正在将训练好的模型保存到积木...', 'info');
      
      try {
        // 更新模型计数
        parentInstance.modelCount++;
        const modelNumber = String(parentInstance.modelCount);
        
        // 为模型创建名称
        const modelName = `包含${modelClasses.length}个类别的声音模型`;
        
        // 将模型添加到缓存中
        parentInstance.modelCache[modelNumber] = {
          modelName: modelName,
          importTime: new Date().toISOString(),
          labels: [...modelClasses]
        };
        
        // 创建模型元数据（不包含模型实例，但包含必要信息）
        const modelMetadata = {
          metadata: { ...metadata },
          trained: true,
          trainedTime: new Date().toISOString(),
          modelType: 'sound'
        };
        
        // 尝试将模型直接保存到IndexedDB
        try {
          // 确保IndexedDB模型ID唯一
          const modelSavePath = `indexeddb://sound-model-${modelNumber}`;
          
          // 使用tf的save方法将模型权重保存到IndexedDB
          await model.save(modelSavePath);
          
          // 将模型元数据保存到localStorage
          try {
            localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(parentInstance.modelCache));
            localStorage.setItem(`logicleap_sound_model_data_${modelNumber}`, JSON.stringify(modelMetadata));
          } catch (storageError) {
            console.error('保存模型元数据到localStorage失败:', storageError);
            // 尝试清理缓存后再保存
            if (typeof parentInstance.cleanupModelCache === 'function') {
              await parentInstance.cleanupModelCache();
              localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(parentInstance.modelCache));
              localStorage.setItem(`logicleap_sound_model_data_${modelNumber}`, JSON.stringify(modelMetadata));
            }
          }
        } catch (saveError) {
          console.error('保存模型到IndexedDB失败:', saveError);
          showMessage('保存模型失败，但您仍可以使用当前模型', 'warning');
        }
        
        // 触发模型缓存更新事件（如果存在）
        if (typeof parentInstance.dispatchModelCacheUpdatedEvent === 'function') {
          parentInstance.dispatchModelCacheUpdatedEvent();
        }
        
        // 清理之前的保存状态
        setModelExported(true);
        
        // 显示成功消息
        showMessage(`模型已成功保存（编号：${modelNumber}），可以使用"加载编号为 ${modelNumber} 的模型"积木块来加载此模型`, 'success');
        
        // 自动关闭训练窗口
        setTimeout(() => {
          if (parentInstance && typeof parentInstance.closeTrainDialogSound === 'function') {
            parentInstance.closeTrainDialogSound();
          }
        }, 1500); // 添加延时，让用户看到成功消息
      
      return true;
      } catch (error) {
        console.error('保存模型到积木失败:', error);
        showMessage(`保存模型失败: ${error.message}`, 'error');
        return false;
      }
    } catch (error) {
      console.error('导出模型到积木时发生错误:', error);
      console.error('错误堆栈:', error.stack);
      showMessage(`导出模型失败: ${error.message}`, 'error');
      setModelExported(false);
      return false;
    }
  };
  
  // 添加辅助函数用于打乱数组
  const shuffleArray = (array) => {
    // 创建数组副本以避免修改原数组
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  // 添加辅助函数用于安全地释放张量
  const safeDispose = (tensor) => {
    if (tensor && tensor.dispose && !tensor.isDisposed) {
      try {
        tensor.dispose();
      } catch (e) {
        console.warn('安全释放张量失败:', e);
      }
    }
  };
  
  // 添加辅助函数用于监控内存泄漏
  const logTensorCount = (label) => {
    if (tf && tf.memory) {
      const memInfo = tf.memory();
      ////console.log(`[${label}] 当前张量数: ${memInfo.numTensors}, 已释放: ${memInfo.numTensorsDeleted}`);
    }
  };
  
  //这里下面存放重构过的代码

  // 渲染类别卡片
  const renderClassCard = (node) => {
    return (
    <ClassCrad
      node={node}
      isCollecting={isCollecting}
      currentClass={currentClass}
      isRecordingPanelExpanded={isRecordingPanelExpanded}
      samples={samples}
      expandRecordingPanel={expandRecordingPanel}
      allClassesTimeFreqDataRef={allClassesTimeFreqDataRef}
      showSettingsPanel={showSettingsPanel}
      audioChunksRef={audioChunksRef}
      collect={collect}
      recordingCountdown={recordingCountdown}
      tempAudioDataRef={tempAudioDataRef}
      stopCollecting={stopCollecting}
      isPlaying={isPlaying}
      formatTime={formatTime}
      currentTime={currentTime}
      duration={duration}
      setClassNodes={setClassNodes}
      playAudio={playAudio}
      extractSamples={extractSamples}
      closeRecordingPanel={closeRecordingPanel}
      openSettingsPanel={openSettingsPanel}
      cancelSettings={cancelSettings}
      saveSettings={saveSettings}
      updateTempSettings={updateTempSettings}
      getClassRecordingSettings={getClassRecordingSettings}
      renameClass={renameClass}
      deleteSample={deleteSample}
      clearSamples={clearSamples}
      deleteClass={deleteClass}
      tempSettings={tempSettings}
      isExtracting={isExtracting}
      playSample={playSample}
    />
  );
};

  //渲染预览面板具体内容,每个模块预览内容不一样的点，都存储到这里面
  const renderPreviewContainer = () => {
      return (
      <Preview
        overlapFactor={overlapFactor}
        predicting={predicting}
        audioData={audioData}
        setOverlapFactor={setOverlapFactor}
      />
    );
  };

  // 修改播放音频函数
  const playAudio = async (classId) => {
    // 检查当前节点是否与要播放的节点一致
    if (currentClass !== classId) {
      console.warn('当前节点与要播放的节点不一致，拒绝播放');
      return;
    }
    
    if (!audioChunksRef.current[classId] || audioChunksRef.current[classId].length === 0) {
      showMessage('没有可播放的音频数据', 'error');
      return;
    }

    try {
      if (isPlaying) {
        // 如果正在播放，则停止
        if (audioSourceRef.current) {
          audioSourceRef.current.pause();
          audioSourceRef.current.currentTime = 0;
          audioSourceRef.current = null;
          
          // 清除时间更新定时器
          if (audioTimerRef.current) {
            clearInterval(audioTimerRef.current);
            audioTimerRef.current = null;
          }
        }
        setIsPlaying(false);
        setCurrentTime(0);
        return;
      }

      // 创建音频Blob
      const audioBlob = new Blob(audioChunksRef.current[classId], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // 创建音频元素
      const audio = new Audio(audioUrl);
      
      // 使用设置好的时长，不再尝试从元数据获取
      // 注意：如果duration已经由stopCollecting设置，就不需要重新计算
      if (duration <= 0) {
        const actualDuration = fullAudioData[classId] && fullAudioData[classId].length > 0 ? 
          (fullAudioData[classId][fullAudioData[classId].length - 1].time / 1000) : 
          getClassRecordingSettings(classId).recordingTime;
        setDuration(actualDuration);
      }
      
      // 添加事件处理用于调试时长问题
      audio.addEventListener('durationchange', () => {
        console.log('Audio元素实际时长:', audio.duration);
      });
      
      // 添加加载完成事件
      audio.addEventListener('loadeddata', () => {
        console.log('音频加载完成，实际时长:', audio.duration);
        if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration) && 
            Math.abs(audio.duration - duration) > 1) {
          console.log('音频元素时长与计算时长不符，元素时长:', audio.duration, '计算时长:', duration);
        }
      });
      
      // 播放音频
      await audio.play();
      setIsPlaying(true);
      
      // 开始更频繁地更新当前时间（每50毫秒一次）以提高精度
      audioTimerRef.current = setInterval(() => {
        // 检查是否仍在播放当前节点的音频
        if (currentClass !== classId) {
          // 节点已切换，停止播放
          audio.pause();
          audio.currentTime = 0;
          clearInterval(audioTimerRef.current);
          audioTimerRef.current = null;
          setIsPlaying(false);
          setCurrentTime(0);
          console.log('节点已切换，停止播放');
          return;
        }
        
        if (audio && !audio.paused) {
          if (audio.currentTime && !isNaN(audio.currentTime) && isFinite(audio.currentTime)) {
            setCurrentTime(audio.currentTime);
          }
        }
      }, 50);
      
      // 播放结束后清理
      audio.onended = () => {
        console.log('音频播放结束');
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
        audioSourceRef.current = null;
        
        // 清除时间更新定时器
        if (audioTimerRef.current) {
          clearInterval(audioTimerRef.current);
          audioTimerRef.current = null;
        }
        
        setCurrentTime(0);
      };
      
      // 存储音频元素引用
      audioSourceRef.current = audio;
    } catch (error) {
      console.error('播放音频失败:', error);
      showMessage('播放音频失败', 'error');
      setIsPlaying(false);
      if (audioSourceRef.current) {
        audioSourceRef.current = null;
      }
      
      // 清除时间更新定时器
      if (audioTimerRef.current) {
        clearInterval(audioTimerRef.current);
        audioTimerRef.current = null;
      }
    }
  };

  // 添加提取样本函数
  const extractSamples = async (classId, startTime = 0, endTime = null) => {
    // 检查当前节点是否与要提取的节点一致
    if (currentClass !== classId) {
      console.warn('当前节点与要提取样本的节点不一致，拒绝提取');
      return;
    }
    
    if (!fullAudioData[classId] || fullAudioData[classId].length === 0) {
      showMessage('没有可提取的音频数据', 'error');
      return;
    }

    try {
      setIsExtracting(true);
      
      // 获取实际录制的时长（秒）
      const recordedDurationSec = Math.floor(duration);
      console.log('实际录制时长（秒）:', recordedDurationSec);
      
      // 如果未指定结束时间，则使用实际录制时长
      if (endTime === null) {
        endTime = recordedDurationSec;
      }
      
      console.log(`提取时间范围: ${startTime}s - ${endTime}s`);
      
      // 按秒分割音频数据
      const samplesBySecond = {};
      
      // 只处理选定时间范围内的帧
      fullAudioData[classId].forEach(frame => {
        // 将时间从毫秒转换为秒
        const frameSec = frame.time / 1000;
        // 只处理在选定时间范围内的秒数
        const second = Math.floor(frameSec);
        if (second >= startTime && second < endTime) {
          if (!samplesBySecond[second]) {
            samplesBySecond[second] = [];
          }
          samplesBySecond[second].push(frame);
        }
      });

      console.log(`按秒分割后有 ${Object.keys(samplesBySecond).length} 个秒样本`);

      // 保存整个录音数据以作备份，确保在播放时有完整音频作为后备
      try {
        if (audioChunksRef.current[classId]?.length > 0) {
          const fullAudioBlob = new Blob(audioChunksRef.current[classId], { type: 'audio/wav' });
          const fullAudioUrl = URL.createObjectURL(fullAudioBlob);
          
          // 保存完整录音的引用，用于需要时回退，并按类别存储
          fullAudioDataRef.current.fullAudioUrl = fullAudioUrl;
          fullAudioDataRef.current.fullAudioDuration = duration;
          // 按类别存储URL
          fullAudioDataRef.current.classAudioUrls[classId] = fullAudioUrl;
        }
      } catch (error) {
        console.error('保存完整录音失败:', error);
      }

      // 获取时频图数据
      const timeFreqData = allClassesTimeFreqDataRef.current[classId] || [];

      // 处理每一秒的数据
      const processPromises = Object.entries(samplesBySecond).map(async ([second, frames]) => {
        if (frames.length > 0) {
          // 为这一秒生成唯一ID
          const sampleId = `${classId}-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
          
          // 计算平均声波（用于备用，但主要使用时频图）
          let avgWaveform = Array(100).fill(0);
          frames.forEach(frame => {
            frame.waveform.forEach((val, i) => {
              avgWaveform[i] += val / frames.length;
            });
          });
          
          // 确保波形有足够的变化
          const maxAmplitude = Math.max(...avgWaveform.map(Math.abs));
          if (maxAmplitude < 0.1) {
            avgWaveform = avgWaveform.map(v => v * 2);
          }
          
          // 为这一秒提取单独的音频块
          // 找出这一秒的开始和结束时间范围
          const secondInt = parseInt(second, 10);
          const secondStartTime = secondInt * 1000; // 转换为毫秒
          const secondEndTime = (secondInt + 1) * 1000;
          
          // 只选取这一秒内的音频帧
          const secondFrames = frames.filter(frame => 
            frame.time >= secondStartTime && frame.time < secondEndTime
          );
          
          // 创建这一秒的音频Blob
          const audioBlob = await createSecondAudioBlob(secondFrames, secondInt, fullAudioDataRef.current.fullAudioUrl);
          const audioUrl = URL.createObjectURL(audioBlob);
          
          // 创建时频图图像替代波形图像
          let timeFreqImage = null;
          try {
            // 创建时频图图像
            const framesPerSecond = 25; // 每秒25帧
            const pixelsPerFrame = 2; // 每帧2px
            const startFrame = secondInt * framesPerSecond;
            const endFrame = startFrame + framesPerSecond;
            
            // 从时频数据中提取这一秒的数据
            if (timeFreqData.length > 0) {
              const canvas = document.createElement('canvas');
              canvas.width = framesPerSecond * pixelsPerFrame; // 1秒 = 25帧 * 2px = 50px宽
              canvas.height = 80; // 与显示高度一致
              
              const ctx = canvas.getContext('2d');
              
              // 绘制时频图 - 从左到右
              // 清空画布并填充背景色
              ctx.fillStyle = 'rgb(1,1,149)';
              ctx.fillRect(0, 0, canvas.width, canvas.height);
              
              const centerY = canvas.height / 2; // 中心线Y坐标
              const maxRowsHalf = timeFreqData.length / 2; // 一半的行数
              const rowHeight = (canvas.height / 2) / maxRowsHalf; // 每行高度
              
              // 绘制每个时频点 - 上半部分和下半部分（镜像）
              for (let frameOffset = 0; frameOffset < framesPerSecond; frameOffset++) {
                const frame = startFrame + frameOffset;
                for (let row = 0; row < maxRowsHalf; row++) {
                  // 使用相同的数据，但镜像绘制
                  const actualRow = Math.floor(row * 2); // 取样数据行
                  
                  // 确保数据存在
                  if (!timeFreqData[actualRow]) continue;
                  
                  const value = timeFreqData[actualRow][frame] || 0;
                  
                  if (value > 0) {
                    // 设置颜色 - 根据频谱能量
                    if (value > 200) {
                      ctx.fillStyle = `rgba(255, ${255 - Math.floor((255 - value) * 0.5)}, 0, 0.9)`;
                    } else if (value > 100) {
                      ctx.fillStyle = `rgba(${value}, 255, 0, 0.8)`;
                    } else {
                      ctx.fillStyle = `rgba(0, ${value * 2}, 255, 0.7)`;
                    }
                    
                    // 上半部分 - 从中心向上绘制
                    const yPosUp = centerY - (row + 1) * rowHeight;
                    ctx.fillRect(frameOffset * pixelsPerFrame, yPosUp, pixelsPerFrame, rowHeight);
                    
                    // 下半部分 - 从中心向下绘制（镜像）
                    const yPosDown = centerY + row * rowHeight;
                    ctx.fillRect(frameOffset * pixelsPerFrame, yPosDown, pixelsPerFrame, rowHeight);
                  }
                }
              }
              
              // 添加中心线
              ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
              ctx.fillRect(0, centerY - 1, canvas.width, 2);
              
              // 将canvas转换为数据URL
              timeFreqImage = canvas.toDataURL('image/png');
            }
          } catch (error) {
            console.error('创建时频图失败:', error);
            // 如果创建时频图失败，回退到波形图
            timeFreqImage = await createWaveformImage(avgWaveform);
          }
          
          // 如果时频图创建失败，使用波形图作为后备
          const waveformImage = timeFreqImage || await createWaveformImage(avgWaveform);
          
          // 更新UI显示 - 添加图像URL和音频URL及附加信息
          setSamples(prev => {
            const classSamples = prev[classId] || [];
            return {
              ...prev,
              [classId]: [...classSamples, {
                id: sampleId, 
                waveform: avgWaveform,
                waveformImage, // 添加时频图或波形图像
                audioUrl,      // 添加音频URL
                duration: 1,   // 每个样本固定1秒
                second: secondInt, // 保存这是第几秒
                startTime: secondStartTime,     // 保存开始时间
                endTime: secondEndTime        // 保存结束时间
              }]
            };
          });
          
          // 更新总样本集 - 加入波形图像和音频数据
          samplesRef.current = [...samplesRef.current, ...secondFrames.map(frame => ({
            ...frame,
            id: sampleId,
            label: classId,
            waveformImage, // 添加时频图或波形图像
            audioUrl,      // 添加音频URL
            second: secondInt,
            startTime: secondStartTime,
            endTime: secondEndTime
          }))];
          
          return {
            id: sampleId,
            waveformImage,
            audioUrl,
            second: secondInt
          };
        }
        return null;
      });
      
      // 等待所有样本处理完成
      await Promise.all(processPromises);
      
      const selectedRange = startTime === 0 && endTime === recordedDurationSec 
        ? '全部内容' 
        : `${startTime}s - ${endTime}s`;
        
    } catch (error) {
      console.error('提取样本失败:', error);
      showMessage('提取样本失败', 'error');
    } finally {
      setIsExtracting(false);
    }
  };
  
  // 为单个秒数创建音频Blob
  const createSecondAudioBlob = async (frames, second, fallbackAudioUrl = null) => {
    try {
      console.log(`创建第${second}秒的音频Blob，帧数: ${frames.length}`);
      
      // 如果没有足够的帧数据，可能会导致音频无法播放
      if (frames.length < 10) {
        console.warn(`第${second}秒帧数太少(${frames.length})，可能导致音频无法播放`);
      }
      
      // 获取这一秒的音频数据
      const audioChunks = audioChunksRef.current[currentClass];
      if (audioChunks && audioChunks.length > 0) {
        // 如果有原始录音块可用
        try {
          // 创建一个AudioContext来处理音频
          const AudioContext = window.AudioContext || window.webkitAudioContext;
          const audioCtx = new AudioContext();
          
          // 先将整个音频数据解码
          const fullAudioBlob = new Blob(audioChunks, { type: 'audio/wav' });
          const arrayBuffer = await fullAudioBlob.arrayBuffer();
          const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
          
          // 计算一秒对应的采样数
          const samplesPerSecond = audioBuffer.sampleRate;
          
          // 创建一个新的一秒音频缓冲区
          const singleSecondBuffer = audioCtx.createBuffer(
            audioBuffer.numberOfChannels,
            samplesPerSecond,
            audioBuffer.sampleRate
          );
          
          // 对每个声道进行处理
          for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
            // 获取原始音频数据
            const channelData = audioBuffer.getChannelData(channel);
            // 获取新缓冲区的数据引用
            const newChannelData = singleSecondBuffer.getChannelData(channel);
            
            // 计算开始索引
            const startIndex = second * samplesPerSecond;
            // 确保不超出边界
            if (startIndex < channelData.length) {
              // 复制这一秒的数据
              for (let i = 0; i < samplesPerSecond && startIndex + i < channelData.length; i++) {
                newChannelData[i] = channelData[startIndex + i];
              }
            }
          }
          
          // 将新缓冲区转换为WAV数据
          const offlineCtx = new OfflineAudioContext(
            singleSecondBuffer.numberOfChannels,
            singleSecondBuffer.length,
            singleSecondBuffer.sampleRate
          );
          
          const source = offlineCtx.createBufferSource();
          source.buffer = singleSecondBuffer;
          source.connect(offlineCtx.destination);
          source.start(0);
          
          // 离线渲染
          const renderedBuffer = await offlineCtx.startRendering();
          
          // 将渲染的缓冲区转换为AudioBuffer格式
          const wavBytes = toWav(renderedBuffer);
          const blob = new Blob([wavBytes], { type: 'audio/wav' });
          
          // 关闭上下文
          audioCtx.close();
          
          console.log(`第${second}秒的音频Blob创建成功`);
          return blob;
        } catch (decodeError) {
          console.error(`解码音频数据失败:`, decodeError);
          // 如果解码失败，尝试使用其他方法
        }
      }
      
      // 如果上面的方法失败或不可用，尝试使用帧数据重建音频
      const audioValues = frames.flatMap(frame => {
        if (frame.rawAudio) return frame.rawAudio;
        return frame.waveform.map(v => v * 32767);
      });
      
      // 如果获取到的数据太少，可能导致无法播放
      if (audioValues.length < 100) {
        console.warn(`第${second}秒的音频数据太少(${audioValues.length})，将创建填充数据`);
        // 创建一个固定的填充数据，确保有足够长度
        const fillCount = 8000; // 约为200ms的44.1kHz音频
        const filledValues = new Array(fillCount).fill(0);
        for (let i = 0; i < fillCount; i++) {
          // 创建一个静音但有轻微波动的数据，避免完全静音可能导致的问题
          filledValues[i] = Math.sin(i * 0.01) * 100;
        }
        return new Blob([createWavHeader(filledValues.length, 1, 8000), new Int16Array(filledValues)], { type: 'audio/wav' });
      }
      
      // 创建一个16位PCM音频缓冲区
      const buffer = new ArrayBuffer(audioValues.length * 2);
      const view = new DataView(buffer);
      
      // 填充缓冲区
      audioValues.forEach((value, index) => {
        view.setInt16(index * 2, value, true);
      });
      
      // 创建适当的WAV头
      const wavHeader = createWavHeader(audioValues.length, 1, 8000);
      
      // 创建音频blob
      return new Blob([wavHeader, buffer], { type: 'audio/wav' });
    } catch (error) {
      console.error(`创建第${second}秒的音频Blob失败:`, error);
      
      // 如果提供了后备URL，返回一个错误标记，稍后处理
      if (fallbackAudioUrl) {
        return new Blob(['ERROR'], { type: 'text/plain' });
      }
      
      // 返回最小音频blob作为后备
      return createEmptyAudioBlob();
    }
  };
  
  // 创建一个空的音频Blob（静音但有效的WAV）
  const createEmptyAudioBlob = () => {
    // 创建一个短的静音音频，约100ms
    const sampleRate = 8000;
    const numSamples = sampleRate / 10;
    const buffer = new ArrayBuffer(numSamples * 2);
    const view = new DataView(buffer);
    
    // 填充静音数据（轻微波动避免播放问题）
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(i * 2, Math.sin(i * 0.1) * 10, true);
    }
    
    // 创建WAV头
    const wavHeader = createWavHeader(numSamples, 1, sampleRate);
    
    return new Blob([wavHeader, buffer], { type: 'audio/wav' });
  };
  
  // 创建WAV文件头
  const createWavHeader = (dataLength, numChannels = 1, sampleRate = 8000, bitsPerSample = 16) => {
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const wavDataSize = dataLength * blockAlign;
    const header = new ArrayBuffer(44);
    const view = new DataView(header);
    
    // "RIFF"标识
    view.setUint8(0, 'R'.charCodeAt(0));
    view.setUint8(1, 'I'.charCodeAt(0));
    view.setUint8(2, 'F'.charCodeAt(0));
    view.setUint8(3, 'F'.charCodeAt(0));
    
    // RIFF块大小
    view.setUint32(4, 36 + wavDataSize, true);
    
    // "WAVE"标识
    view.setUint8(8, 'W'.charCodeAt(0));
    view.setUint8(9, 'A'.charCodeAt(0));
    view.setUint8(10, 'V'.charCodeAt(0));
    view.setUint8(11, 'E'.charCodeAt(0));
    
    // "fmt "子块标识
    view.setUint8(12, 'f'.charCodeAt(0));
    view.setUint8(13, 'm'.charCodeAt(0));
    view.setUint8(14, 't'.charCodeAt(0));
    view.setUint8(15, ' '.charCodeAt(0));
    
    // fmt子块大小
    view.setUint32(16, 16, true);
    
    // 音频格式（1为PCM）
    view.setUint16(20, 1, true);
    
    // 声道数
    view.setUint16(22, numChannels, true);
    
    // 采样率
    view.setUint32(24, sampleRate, true);
    
    // 字节率 = 采样率 * 通道数 * 位深/8
    view.setUint32(28, byteRate, true);
    
    // 块对齐 = 通道数 * 位深/8
    view.setUint16(32, blockAlign, true);
    
    // 采样位深
    view.setUint16(34, bitsPerSample, true);
    
    // "data"子块标识
    view.setUint8(36, 'd'.charCodeAt(0));
    view.setUint8(37, 'a'.charCodeAt(0));
    view.setUint8(38, 't'.charCodeAt(0));
    view.setUint8(39, 'a'.charCodeAt(0));
    
    // data子块大小
    view.setUint32(40, wavDataSize, true);
    
    return header;
  };
  
  // AudioBuffer转WAV
  const toWav = (audioBuffer) => {
    const numOfChannels = audioBuffer.numberOfChannels;
    const length = audioBuffer.length * numOfChannels * 2;
    const buffer = new ArrayBuffer(44 + length);
    const view = new DataView(buffer);
    const channels = [];
    let offset = 0;
    let pos = 0;
    
    // 提取通道数据
    for (let i = 0; i < numOfChannels; i++) {
      channels.push(audioBuffer.getChannelData(i));
    }
    
    // 写入WAV头
    writeString(view, offset, 'RIFF'); offset += 4;
    view.setUint32(offset, 36 + length, true); offset += 4;
    writeString(view, offset, 'WAVE'); offset += 4;
    writeString(view, offset, 'fmt '); offset += 4;
    view.setUint32(offset, 16, true); offset += 4;
    view.setUint16(offset, 1, true); offset += 2; // PCM格式
    view.setUint16(offset, numOfChannels, true); offset += 2;
    view.setUint32(offset, audioBuffer.sampleRate, true); offset += 4;
    view.setUint32(offset, audioBuffer.sampleRate * 2 * numOfChannels, true); offset += 4;
    view.setUint16(offset, numOfChannels * 2, true); offset += 2;
    view.setUint16(offset, 16, true); offset += 2;
    writeString(view, offset, 'data'); offset += 4;
    view.setUint32(offset, length, true); offset += 4;
    
    // 写入交错的音频数据
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, channels[channel][i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
      }
    }
    
    return buffer;
  };
  
  // 辅助函数：写入字符串到DataView
  const writeString = (view, offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  // 创建波形图像函数
  const createWaveformImage = (waveform) => {
    return new Promise((resolve) => {
      // 创建一个离屏canvas
      const canvas = document.createElement('canvas');
      canvas.width = 100;  // 与波形数据长度匹配
      canvas.height = 60;  // 合适的高度
      
      const ctx = canvas.getContext('2d');
      
      // 绘制背景
      ctx.fillStyle = 'rgb(1,1,149)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // 绘制波形
      const centerY = canvas.height / 2;
      
      // 创建渐变填充
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      gradient.addColorStop(0, 'rgba(24, 144, 255, 0.2)');
      gradient.addColorStop(1, 'rgba(24, 144, 255, 0.8)');
      
      // 绘制填充区域
      ctx.beginPath();
      ctx.moveTo(0, centerY);
      
      waveform.forEach((value, index) => {
        const x = index;
        const y = centerY - value * 25;
        ctx.lineTo(x, y);
      });
      
      ctx.lineTo(canvas.width, centerY);
      ctx.closePath();
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // 绘制波形线
      ctx.beginPath();
      ctx.moveTo(0, centerY);
      
      waveform.forEach((value, index) => {
        const x = index;
        const y = centerY - value * 25;
        ctx.lineTo(x, y);
      });
      
      ctx.strokeStyle = '#1890ff';
      ctx.lineWidth = 1.5;
      ctx.stroke();
      
      // 转换为图像URL
      const imageUrl = canvas.toDataURL('image/png');
      resolve(imageUrl);
    });
  };
  
  // 格式化时间函数（将秒数转为 mm:ss 格式）
  const formatTime = (time) => {
    if (time === undefined || time === null || isNaN(time) || !isFinite(time)) {
      return '00:00';
    }
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // 添加播放特定样本的函数
  const playSample = (classId, sampleId) => {
    try {
      // 停止当前正在播放的样本音频（如果有）
      if (samplePlayingState.isPlaying && samplePlayingState.audioElement) {
        samplePlayingState.audioElement.pause();
        if (samplePlayingState.audioElement.src) {
          // 尝试释放URL，防止内存泄漏
          try {
            URL.revokeObjectURL(samplePlayingState.audioElement.src);
          } catch (e) {
            console.log('无法释放音频URL:', e);
          }
        }
        
        // 更新样本播放状态
        setSamplePlayingState(prev => ({
          ...prev,
          isPlaying: false,
          currentSampleId: null,
          audioElement: null
        }));
      }
      
      // 查找样本
      const sample = samples[classId]?.find(s => (s.id || s.second) === sampleId);
      if (!sample || !sample.audioUrl) {
        console.warn('未找到样本音频或样本不包含音频URL');
        return;
      }
      
      console.log(`播放样本: ID=${sampleId}, 时间段=${sample.second || 0}秒`);
      
      // 创建临时音频元素，不影响主播放器
      const tempAudio = new Audio();
      
      // 设置音频格式处理
      tempAudio.preload = 'auto';
      
      // 设置事件处理
      tempAudio.onloadedmetadata = () => {
        console.log(`样本音频元数据加载完成, 时长: ${tempAudio.duration}秒`);
      };
      
      tempAudio.onloadeddata = () => {
        console.log('样本音频数据加载完成，开始播放');
      };
      
      tempAudio.onended = () => {
        console.log('样本音频播放结束');
        // 更新样本播放状态
        setSamplePlayingState(prev => ({
          ...prev,
          isPlaying: false,
          currentSampleId: null,
          audioElement: null
        }));
      };
      
      tempAudio.onerror = (e) => {
        console.error('样本音频播放错误:', e);
        // 如果发生错误，尝试使用备用URL - 使用对应类别的录音
        const classAudioUrl = fullAudioDataRef.current.classAudioUrls[classId];
        if (classAudioUrl && sample.second !== undefined) {
          console.log('尝试使用完整录音并定位到指定时间');
          // 创建新的音频对象，使用对应类别的音频
          const backupAudio = new Audio(classAudioUrl);
          
          backupAudio.onloadedmetadata = () => {
            // 设置播放位置到指定秒数
            backupAudio.currentTime = sample.second;
            
            // 播放时长限制 - 用于确保只播放1秒
            const maxPlayDuration = setTimeout(() => {
              if (backupAudio.currentTime >= sample.second + 1) {
                backupAudio.pause();
                // 更新样本播放状态
                setSamplePlayingState(prev => ({
                  ...prev,
                  isPlaying: false,
                  currentSampleId: null,
                  audioElement: null
                }));
              }
            }, 1100); // 设置略多于1秒以确保完整播放
            
            // 存储定时器ID，以便清理
            const timerId = maxPlayDuration;
            
            // 结束时清理定时器
            backupAudio.onended = () => {
              clearTimeout(timerId);
              setSamplePlayingState(prev => ({
                ...prev,
                isPlaying: false,
                currentSampleId: null,
                audioElement: null
              }));
            };
          };
          
          backupAudio.play().catch(err => {
            console.error('备用播放失败:', err);
            showMessage('播放样本失败', 'error');
            setSamplePlayingState(prev => ({
              ...prev,
              isPlaying: false,
              currentSampleId: null,
              audioElement: null
            }));
          });
          
          // 更新样本播放状态
          setSamplePlayingState({
            isPlaying: true,
            currentSampleId: sampleId,
            audioElement: backupAudio
          });
        } else {
          showMessage('播放样本失败', 'error');
          setSamplePlayingState(prev => ({
            ...prev,
            isPlaying: false,
            currentSampleId: null,
            audioElement: null
          }));
        }
      };
      
      // 设置音频源
      tempAudio.src = sample.audioUrl;
      
      // 尝试播放音频
      tempAudio.play().catch(error => {
        console.error('播放样本音频失败:', error);
        // 触发error事件处理
        tempAudio.dispatchEvent(new ErrorEvent('error'));
      });
      
      // 更新样本播放状态
      setSamplePlayingState({
        isPlaying: true,
        currentSampleId: sampleId,
        audioElement: tempAudio
      });
      
    } catch (error) {
      console.error('播放样本失败:', error);
      showMessage('播放样本失败', 'error');
      setSamplePlayingState(prev => ({
        ...prev,
        isPlaying: false,
        currentSampleId: null,
        audioElement: null
      }));
    }
  };
  
  const renderDataChart = () => {
    if (!showDataChart) return null;
    
    // 使用临时变量存储训练历史数据的状态，避免使用stale数据
    const currentTrainingHistory = trainingHistory;
    const hasData = currentTrainingHistory.epochs.length > 0;
    
    // 图表容器的样式
    const chartPanelStyle = {
      position: 'absolute',
      top: 0,
      right: 0,
      width: '300px',
      height: '100%', 
      backgroundColor: 'white',
      boxShadow: '-4px 0 12px rgba(0, 0, 0, 0.1)',
      zIndex: 100,
      transform: showDataChart ? 'translateX(0)' : 'translateX(100%)',
      transition: 'transform 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      borderLeft: '1px solid #eee'
    };
    
    const chartHeaderStyle = {
      padding: '16px',
      borderBottom: '1px solid #eee',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    };
    
    const chartContentStyle = {
      flex: 1,
      padding: '16px',
      overflowY: 'auto'
    };
    
    const closeButtonStyle = {
      background: 'none',
      border: 'none',
      fontSize: '20px',
      cursor: 'pointer',
      color: '#666'
    };
    
    const chartStyle = {
      marginBottom: '24px',
      padding: '16px',
      border: '1px solid #eee',
      borderRadius: '8px',
      backgroundColor: '#f9f9f9'
    };
    
    // 图表图标样式
    const chartIconStyle = {
      marginRight: '8px', 
      display: 'inline-flex',
      verticalAlign: 'middle',
      width: '20px',
      height: '20px',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#4f7df9'
    };
    
    // 介绍内容块样式
    const introBlockStyle = {
      padding: '12px',
      backgroundColor: '#f0f7ff',
      borderRadius: '8px',
      fontSize: '14px',
      lineHeight: 1.5,
      color: '#4a5568',
      marginBottom: '24px',
      border: '1px solid #e3eeff'
    };
    
    return (
      <>
        <div style={chartPanelStyle}>
          <div style={chartHeaderStyle}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
              <span style={chartIconStyle}>
                <svg viewBox="0 0 24 24" width="20" height="20" stroke="currentColor" strokeWidth="2" fill="none">
                  <path d="M2 22h20M2 2v16h20V2H2z" />
                  <path d="M7 14l3-3 2 2 3-3 3 3" />
                </svg>
              </span>
              数据图表
            </h3>
            <button 
              style={closeButtonStyle}
              onClick={() => setShowDataChart(false)}
            >
              ×
            </button>
          </div>
          
          <div style={chartContentStyle}>
            {/* 添加介绍内容 */}
            <div style={introBlockStyle}>
              <strong style={{ fontSize: '15px', display: 'block', marginBottom: '4px' }}>训练图表</strong>
              <p style={{ margin: '0' }}>这里的图表可以帮助您了解模型的训练和运行情况</p>
            </div>
            
            {hasData ? (
              <>
                <div style={chartStyle}>
                  <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#4f7df9', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M23 6l-9.5 9.5-5-5L1 18" />
                      </svg>
                    </span>
                    每个周期的准确度
                  </h4>
                  <canvas 
                    ref={accChartRef} 
                    style={{ width: '100%', height: '180px' }}
                  />
                </div>
                
                <div style={chartStyle}>
                  <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#4fb14f', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 3v18h18" />
                        <path d="M18 12l-6-6-6 6" />
                      </svg>
                    </span>
                    每个周期的损失
                  </h4>
                  <canvas 
                    ref={lossChartRef} 
                    style={{ width: '100%', height: '180px' }}
                  />
                </div>
                
                <div style={{ marginTop: '20px', backgroundColor: 'white', padding: '16px', borderRadius: '8px', border: '1px solid #eee' }}>
                  <h4 style={{ margin: '0 0 12px 0', display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#718096', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 3v18M3 12h18" />
                        <path d="M5 5l14 14M5 19l14-14" />
                      </svg>
                    </span>
                    数据统计
                  </h4>
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr>
                        <th style={{ padding: '8px', textAlign: 'left', borderBottom: '1px solid #eee', color: '#718096' }}>指标</th>
                        <th style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', color: '#718096' }}>训练集</th>
                        <th style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', color: '#718096' }}>验证集</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>最终准确度</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#4f7df9' }}>
                          {currentTrainingHistory.trainAccuracy.length > 0 
                            ? (currentTrainingHistory.trainAccuracy[currentTrainingHistory.trainAccuracy.length - 1] * 100).toFixed(2) + '%' 
                            : '-'}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#f97d4f' }}>
                          {currentTrainingHistory.valAccuracy.length > 0 && currentTrainingHistory.valAccuracy[currentTrainingHistory.valAccuracy.length - 1] > 0
                            ? (currentTrainingHistory.valAccuracy[currentTrainingHistory.valAccuracy.length - 1] * 100).toFixed(2) + '%' 
                            : '-'}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>最终损失值</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#4fb14f' }}>
                          {currentTrainingHistory.trainLoss.length > 0 
                            ? currentTrainingHistory.trainLoss[currentTrainingHistory.trainLoss.length - 1].toFixed(4)
                            : '-'}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#b14f4f' }}>
                          {currentTrainingHistory.valLoss.length > 0 && currentTrainingHistory.valLoss[currentTrainingHistory.valLoss.length - 1] > 0
                            ? currentTrainingHistory.valLoss[currentTrainingHistory.valLoss.length - 1].toFixed(4)
                            : '-'}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>总训练周期</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold' }} colSpan={2}>
                          {currentTrainingHistory.epochs.length}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999', backgroundColor: '#f9f9f9', borderRadius: '8px', marginTop: '20px' }}>
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#ccc" strokeWidth="1.5" style={{ margin: '0 auto 16px', display: 'block' }}>
                  <path d="M2 22h20M2 2v16h20V2H2z" />
                  <path d="M7 14l3-3 2 2 3-3 3 3" strokeDasharray="4" />
                </svg>
                <p style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '8px' }}>尚无训练数据</p>
                <p style={{ fontSize: '14px' }}>训练模型后将在此显示训练过程的准确率和损失</p>
              </div>
            )}
          </div>
        </div>
      </>
    );
  };

  // 如果你要在预览头部增加内容，你应该在这里添加
  const renderPreviewHeaderFunction = () => {
    return (
      <div>
          {/* 隐藏文件输入但保留功能 */}
          <input
              ref={fileInputRef}
              type="file"
              accept=".zip"
              style={{ display: 'none' }}
              onChange={async (e) => {
                const file = e.target.files?.[0];
                if (!file) return;

                try {
                  await validateModelFile(file);
                  const { model, metadata } = await loadModel(file);
                  
                  // 更新模型
                  modelRef.current = model;
                  setModelTrained(true);
                  
                  // 如果有类别信息，更新类别
                  if (metadata?.userMetadata?.classes) {
                    const originalClasses = metadata.userMetadata.classes;
                    setOriginalModelClasses(originalClasses);
                    
                    console.log('导入模型的原始类别:', originalClasses);
                    
                    // 清空现有类别节点
                    setClassNodes([]);
                    
                    // 保存类别ID映射关系 - 这是关键改进点
                    const mapping = {};
                    
                    // 为每个导入的类别创建节点
                    const loadedClasses = originalClasses.map((name, index) => {
                      // 使用显示的ID (0, 1, 2...) 作为UI显示ID
                      // 但在内部，我们知道它映射到原模型的类别索引
                      mapping[index] = index;
                      
                      return {
                        id: index, // UI显示ID
                        name,
                        samples: 0,
                        position: { x: 100, y: 100 + index * 150 },
                        showMenu: false,
                        isLoaded: true, // 标记为已加载的类别
                        originalIndex: index // 保存原始索引
                      };
                    });
                    
                    setClassNodes(loadedClasses);
                    setClassIdMapping(mapping);
                    
                    console.log('创建的类别ID映射:', mapping);
                    
                    // 清理样本数据
                    samplesRef.current = [];
                    setSamples({});
                  }
                  
                  // 如果有训练参数，更新参数
                  if (metadata?.userMetadata?.trainParams) {
                    setTrainParams(metadata.userMetadata.trainParams);
                  }
                  
                  showMessage('模型已成功加载', 'success');
                } catch (error) {
                  showMessage(error.message || '加载模型时发生错误', 'error');
                }
                
                // 清除文件选择
                if (fileInputRef.current) {
                  fileInputRef.current.value = '';
                }
              }}
            />
      </div>
    )
  }

  // 训练主组件的属性
  const trainMainProps={
    //ref元素
    containerRef,
    classesPanelContentRef,

    //传入面板方法
    //渲染连接线
    //渲染预览内容面板
    
    //传入训练的属性或者方法
    //类别属性
    classNodes,
    //类别方法
    renderClassCard,
    addClassNode,

    //训练属性
    trainParams,
    setTrainParams,
    DefaultTrainParams,
    isTraining,
    training,
    advancedExpanded,
    setAdvancedExpanded,
    canTrainRef,

    //训练方法
    train,

    //传入预览的属性或者方法
    //预览属性
    modelTrained,
    predicting,
    confidences,

    //预览方法
    exportModelToBlocks,
    renderPreviewHeaderFunction,
    renderPreviewContainer,


    //ref,用于回传上面组件需要调用的方法
    trainMainFunctionRef,

    //数据图表
    showDataChart,
    setShowDataChart,
    renderDataChart,
  }

  // 在组件返回部分添加图表渲染
  return (
    <>
      <TrainMain
        {...trainMainProps}
      />
    </>
  );
};

export default SoundMode;