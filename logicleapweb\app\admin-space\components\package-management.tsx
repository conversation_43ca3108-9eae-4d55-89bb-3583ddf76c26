'use client'

import { useState } from 'react';
import { Button, Table, Tag, Space, Modal, Form, Input, InputNumber, Select, Spin, Card, Upload, Alert, Radio, List, Checkbox, Switch } from 'antd';
import { packageApi } from '@/lib/api/package';
import { userApi } from '@/lib/api/user';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { debounce } from 'lodash';
import Search from 'antd/es/input/Search';
import { UserInfo } from '@/types/user';
import { InboxOutlined, LeftOutlined } from '@ant-design/icons';
import { studentApi } from '@/lib/api/student';
import { schoolApi } from '@/lib/api/school';

const { Dragger } = Upload;

const { Option } = Select;

interface PackageManagementProps {
  userId: number;
  roleMap: Record<number, string>;
}

interface SchoolInfo {
  id: number;
  schoolName: string;
  province: string;
  city: string;
  district: string;
}

interface ClassInfo {
  id: number;
  grade: string;
  className: string;
}

interface StudentInfo {
  userId: number;
  nickName: string;
  studentNumber: string;
  phone: string;
  avatarUrl: string;
}

export default function PackageManagement({ userId, roleMap }: PackageManagementProps) {
  const [isPackageModalVisible, setIsPackageModalVisible] = useState(false);
  const [isAddPackageModalVisible, setIsAddPackageModalVisible] = useState(false);
  const [packageList, setPackageList] = useState<any[]>([]);
  const [editingPackage, setEditingPackage] = useState<any>(null);
  const [addPackageForm] = Form.useForm();
  const [packagePage, setPackagePage] = useState(1);
  const [packageTotal, setPackageTotal] = useState(0);
  const [isAssignPackageModalVisible, setIsAssignPackageModalVisible] = useState(false);
  const [searchedUsers, setSearchedUsers] = useState<any[]>([]);
  const [userSearchLoading, setUserSearchLoading] = useState(false);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [isBatchAssign, setIsBatchAssign] = useState(false);
  const [assignPackageForm] = Form.useForm();
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [userTableLoading, setUserTableLoading] = useState(false);
  const [userTablePage, setUserTablePage] = useState(1);
  const [userTableTotal, setUserTableTotal] = useState(0);
  const [userTableKeyword, setUserTableKeyword] = useState('');
  const [selectedUserMap, setSelectedUserMap] = useState<Record<number, any>>({});
  const [csvUsers, setCsvUsers] = useState<any[]>([]);
  const [csvMatchResults, setCsvMatchResults] = useState<{
    matched: any[];
    unmatched: any[];
  }>({ matched: [], unmatched: [] });
  const [selectMode, setSelectMode] = useState<'manual' | 'csv' | 'school'>('manual');
  const [selectedSchool, setSelectedSchool] = useState<number | null>(null);
  const [selectedClass, setSelectedClass] = useState<number | null>(null);
  const [schoolList, setSchoolList] = useState<SchoolInfo[]>([]);
  const [classList, setClassList] = useState<ClassInfo[]>([]);
  const [studentList, setStudentList] = useState<StudentInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [schoolPage, setSchoolPage] = useState(1);
  const [schoolTotal, setSchoolTotal] = useState(0);
  const [classPage, setClassPage] = useState(1);
  const [classTotal, setClassTotal] = useState(0);
  const [enableNotification, setEnableNotification] = useState(false);
  const [showPackagePoints, setShowPackagePoints] = useState(false);
  const [showInMessageCenter, setShowInMessageCenter] = useState(false);
  const [isMessageSettingModalVisible, setIsMessageSettingModalVisible] = useState(false);
  const notification = GetNotification();

  // 获取套餐列表
  const fetchPackageList = async (page: number = 1, size: number = 10) => {
    try {
      const { data: res } = await packageApi.getList({
        page,
        size
      });

      if (res.code === 200) {
        setPackageList(res.data.list);
        setPackageTotal(res.data.pagination.total);
      }
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      notification.error('获取套餐列表失败');
    }
  };

  // 获取所有用户
  const fetchAllUsers = async (page: number = 1, keyword: string = '') => {
    setUserTableLoading(true);
    try {
      const { data: res } = await userApi.getUserList({
        page,
        size: 5,
        keyword
      });
      if (res.code === 200) {
        setAllUsers(res.data);
        // setUserTableTotal(res.data.pagination?.total || 0);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    }
    setUserTableLoading(false);
  };

  // 搜索用户的防抖函数
  const debounceFetcher = debounce(async (value: string) => {
    if (!value) {
      setSearchedUsers([]);
      return;
    }

    setUserSearchLoading(true);
    try {
      const { data: res } = await userApi.getUserList({
        keyword: value,
        page: 1,
        size: 20
      });
      if (res.code === 200) {
        setSearchedUsers(res.data.list);
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
    }
    setUserSearchLoading(false);
  }, 500);

  // 获取学校列表
  const fetchSchools = async (page: number = 1, keyword: string = '') => {
    setLoading(true);
    try {
      const { data: res } = await schoolApi.getList({
        keyword: keyword,
        page,
        size: 10
      });
      console.log('来了', res);
      if (res.code === 200) {
        setSchoolList(res.data);
        setSchoolTotal(res.data.length);
      }
    } catch (error) {
      notification.error('获取学校列表失败');
    }
    setLoading(false);
  };

  // 获取班级列表
  const fetchClasses = async (schoolId: number, page: number = 1) => {
    setLoading(true);
    try {
      const { data: res } = await schoolApi.getSchoolClasses({
        schoolId,
        page,
        size: 10
      });
      if (res.code === 200) {
        setClassList(res.data.list);
        setClassTotal(res.data.total);
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      notification.error('获取班级列表失败');
    }
    setLoading(false);
  };

  // 获取学生列表
  const fetchStudents = async (classId: number) => {
    setLoading(true);
    try {
      const { data: res } = await studentApi.searchStudents(classId, '');
      if (res.code === 200) {
        setStudentList(res.data);
      }
    } catch (error) {
      console.error('获取学生列表失败:', error);
      notification.error('获取学生列表失败');
    }
    setLoading(false);
  };

  return (
    <>
      <Card
        title="套餐管理"
        extra={<Button type="primary" onClick={() => {
          fetchPackageList();
          setIsPackageModalVisible(true);
        }}>查看全部</Button>}
        className="shadow-sm"
      >
        <div className="space-y-4">
          <Button block onClick={() => setIsAddPackageModalVisible(true)}>
            添加套餐
          </Button>
          <Button block onClick={() => {
            packageApi.getAvailablePackages().then((packageRes) => {
              if (packageRes.data.code === 200) {
                setPackageList(packageRes.data.data);
                setIsAssignPackageModalVisible(true);
                addPackageForm.resetFields();
                setSearchedUsers([]);
                setSelectedUserIds([]);
                setIsBatchAssign(false);
                setUserTablePage(1);
                setUserTableKeyword('');
                setEnableNotification(false);
                setShowPackagePoints(false);
                setShowInMessageCenter(false);
                assignPackageForm.setFieldsValue({
                  enableNotification: false,
                  showPackagePoints: false,
                  showInMessageCenter: false
                });
                fetchAllUsers();
              }
            });
          }}>
            为用户分配套餐
          </Button>
        </div>
      </Card>

      {/* 套餐列表模态框 */}
      <Modal
        title="套餐管理"
        open={isPackageModalVisible}
        onCancel={() => setIsPackageModalVisible(false)}
        width={800}
        footer={null}
      >
        <div className="mb-4">
          <Button type="primary" onClick={() => setIsAddPackageModalVisible(true)}>
            添加套餐
          </Button>
        </div>
        <Table
          dataSource={packageList}
          columns={[
            {
              title: '套餐名称',
              dataIndex: 'name',
              key: 'name',
            },
            {
              title: '能量点数',
              dataIndex: 'points',
              key: 'points',
            },
            {
              title: '有效期(天)',
              dataIndex: 'validityDays',
              key: 'validityDays',
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              render: (status: number) => (
                <Tag color={status === 1 ? 'green' : 'red'}>
                  {status === 1 ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Space>
                  <Button
                    type="link"
                    onClick={() => {
                      setEditingPackage(record);
                      addPackageForm.setFieldsValue(record);
                      setIsAddPackageModalVisible(true);
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    type="link"
                    onClick={async () => {
                      try {
                        const { data: res } = await packageApi.update({
                          id: record.id,
                          status: record.status === 1 ? 0 : 1
                        });
                        if (res.code === 200) {
                          notification.success(record.status === 1 ? '已禁用' : '已启用');
                          fetchPackageList(packagePage);
                        }
                      } catch (error) {
                        notification.error('操作失败');
                      }
                    }}
                  >
                    {record.status === 1 ? '禁用' : '启用'}
                  </Button>
                  <Button
                    type="link"
                    danger
                    onClick={() => {
                      Modal.confirm({
                        title: '确认删除',
                        content: '确定要删除这个套餐吗？',
                        onOk: async () => {
                          try {
                            const { data: res } = await packageApi.delete(record.id);
                            if (res.code === 200) {
                              notification.success('删除成功');
                              fetchPackageList(packagePage);
                            }
                          } catch (error) {
                            notification.error('删除失败');
                          }
                        },
                      });
                    }}
                  >
                    删除
                  </Button>
                </Space>
              ),
            },
          ]}
          pagination={{
            current: packagePage,
            total: packageTotal,
            onChange: (page) => {
              setPackagePage(page);
              fetchPackageList(page);
            },
          }}
        />
      </Modal>

      {/* 添加/编辑套餐模态框 */}
      <Modal
        title={editingPackage ? '编辑套餐' : '添加套餐'}
        open={isAddPackageModalVisible}
        onCancel={() => {
          setIsAddPackageModalVisible(false);
          setEditingPackage(null);
          addPackageForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={addPackageForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              // 强制将points转换为数字类型
              const formattedValues = {
                ...values,
                points: parseFloat(values.points), // 使用parseFloat支持小数,

              };
              const api = editingPackage ? packageApi.update : packageApi.add;
              const data = editingPackage ? { ...formattedValues, id: editingPackage.id } : formattedValues;
              const { data: res } = await api(data);
              if (res.code === 200) {
                notification.success(editingPackage ? '更新成功' : '添加成功');
                setIsAddPackageModalVisible(false);
                setEditingPackage(null);
                addPackageForm.resetFields();
                fetchPackageList(packagePage);
              }
            } catch (error) {
              notification.error(editingPackage ? '更新失败' : '添加失败');
              console.log(error);
            }
          }}
        >
          <Form.Item
            name="name"
            label="套餐名称"
            rules={[{ required: true, message: '请输入套餐名称' }]}
          >
            <Input placeholder="请输入套餐名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="套餐描述"
          >
            <Input.TextArea placeholder="请输入套餐描述" />
          </Form.Item>

          <Form.Item
            name="points"
            label="能量点数"
            rules={[{ required: true, message: '请输入能量点数' }]}
            getValueFromEvent={(value) => typeof value === 'string' ? parseInt(value, 10) : value}
          >
            <InputNumber min={0} placeholder="请输入能量点数" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="validityDays"
            label="有效期(天)"
            rules={[{ required: true, message: '请输入有效期' }]}
          >
            <InputNumber min={1} placeholder="请输入有效期天数" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            initialValue={1}
          >
            <Select>
              <Option value={1}>启用</Option>
              <Option value={0}>禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              {editingPackage ? '更新' : '添加'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分配套餐模态框 */}
      <Modal
        title="分配套餐"
        open={isAssignPackageModalVisible}
        onCancel={() => {
          setIsAssignPackageModalVisible(false);
          setSearchedUsers([]);
          setSelectedUserIds([]);
          setSelectedUserMap({});
          setIsBatchAssign(false);
          setSelectMode('manual');
          setCsvMatchResults({ matched: [], unmatched: [] });
          assignPackageForm.resetFields();
          setEnableNotification(true);
          setShowPackagePoints(true);
          setShowInMessageCenter(false);
        }}
        footer={null}
      >
        <div className="mb-4">
          <Button
            type="link"
            onClick={() => {
              setIsBatchAssign(!isBatchAssign);
              assignPackageForm.resetFields();
              setSelectedUserIds([]);
              setSelectedUserMap({});
              setSelectMode('manual');
              setCsvMatchResults({ matched: [], unmatched: [] });
              setEnableNotification(true);
              setShowPackagePoints(true);
              setShowInMessageCenter(false);
              assignPackageForm.setFieldsValue({
                enableNotification: true,
                showPackagePoints: true,
                showInMessageCenter: false
              });
              if (!isBatchAssign) {
                fetchAllUsers();
              }
            }}
          >
            {isBatchAssign ? '单个分配' : '批量分配'}
          </Button>
        </div>

        <Form
          form={assignPackageForm}
          onFinish={async (values) => {
            try {
              if (selectMode === 'school' || selectMode === 'csv') {
                // 检查是否有选中的用户
                if (selectedUserIds.length === 0) {
                  notification.error('请选择至少一个学生');
                  return;
                }

                const promises = selectedUserIds.map(targetUserId =>
                  packageApi.assignPackage({
                    userId: targetUserId,
                    packageId: values.packageId,
                    remark: values.remark,
                    operatorId: userId,
                    enableNotification: values.enableNotification,
                    showPackagePoints: values.showPackagePoints,
                    showInMessageCenter: values.showInMessageCenter
                  })
                );

                const results = await Promise.all(promises);
                const hasError = results.some(res => res.data.code !== 200);

                if (!hasError) {
                  notification.success('批量分配成功');
                  setIsAssignPackageModalVisible(false);
                  setSelectedUserIds([]);
                  setSelectedUserMap({});
                  setIsBatchAssign(false);
                  setCsvMatchResults({ matched: [], unmatched: [] });
                } else {
                  notification.error('部分用户分配失败');
                }
              } else {
                // 单个分配
                const params = {
                  userId: values.userId,
                  packageId: values.packageId,
                  remark: values.remark,
                  operatorId: userId,
                  enableNotification: values.enableNotification,
                  showPackagePoints: values.showPackagePoints,
                  showInMessageCenter: values.showInMessageCenter
                };

                const { data: res } = await packageApi.assignPackage(params);

                if (res.code === 200) {
                  notification.success('分配成功');
                  setIsAssignPackageModalVisible(false);
                }
              }
            } catch (error) {
              console.error('分配失败:', error);
              notification.error('分配失败');
            }
          }}
          layout="vertical"
        >
          <Form.Item label="选择方式">
            <Radio.Group
              value={selectMode}
              onChange={(e) => {
                const mode = e.target.value;
                setSelectMode(mode);
                setSelectedUserIds([]);
                setSelectedUserMap({});
                setCsvMatchResults({ matched: [], unmatched: [] });
                setSelectedSchool(null);
                setSelectedClass(null);
                if (mode === 'manual') {
                  fetchAllUsers(1, '');
                } else if (mode === 'school') {
                  fetchSchools();
                }
              }}
            >
              <Radio.Button value="manual">手动选择</Radio.Button>
              <Radio.Button value="school">按学校班级选择</Radio.Button>
              <Radio.Button value="csv">CSV导入</Radio.Button>
            </Radio.Group>
          </Form.Item>

          {selectMode === 'school' && (
            <div className="space-y-4">
              {/* 学校选择 */}
              {!selectedSchool && (
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">选择学校</h3>
                    <Input.Search
                      placeholder="搜索学校"
                      onSearch={(value) => {
                        setSchoolPage(1);
                        fetchSchools(1, value);
                      }}
                      style={{ width: 200 }}
                    />
                  </div>
                  <List
                    loading={loading}
                    dataSource={schoolList}
                    renderItem={(school) => (
                      <List.Item
                        className="cursor-pointer hover:bg-gray-50 p-4 rounded"
                        onClick={() => {
                          setSelectedSchool(school.id);
                          setClassPage(1);
                          fetchClasses(school.id, 1);
                        }}
                      >
                        <div>
                          <div className="font-medium">{school.schoolName}</div>
                          <div className="text-sm text-gray-500">
                            {school.province} {school.city} {school.district}
                          </div>
                        </div>
                      </List.Item>
                    )}
                    pagination={{
                      current: schoolPage,
                      pageSize: 10,
                      total: schoolTotal,
                      onChange: (page) => {
                        setSchoolPage(page);
                        fetchSchools(page);
                      }
                    }}
                  />
                </div>
              )}

              {/* 班级选择 */}
              {selectedSchool && !selectedClass && (
                <div>
                  <div className="flex items-center mb-4">
                    <Button
                      type="link"
                      icon={<LeftOutlined />}
                      onClick={() => {
                        setSelectedSchool(null);
                        setClassList([]);
                        fetchSchools(schoolPage);
                      }}
                    >
                      返回学校列表
                    </Button>
                  </div>
                  <List
                    loading={loading}
                    dataSource={classList}
                    renderItem={(classItem) => (
                      <List.Item
                        className="cursor-pointer hover:bg-gray-50 p-4 rounded"
                        onClick={() => {
                          setSelectedClass(classItem.id);
                          fetchStudents(classItem.id);
                        }}
                      >
                        <div className="font-medium">
                          {classItem.grade} {classItem.className}
                        </div>
                      </List.Item>
                    )}
                    pagination={{
                      current: classPage,
                      pageSize: 10,
                      total: classTotal,
                      onChange: (page) => {
                        setClassPage(page);
                        fetchClasses(selectedSchool, page);
                      }
                    }}
                  />
                </div>
              )}

              {/* 学生列表 */}
              {selectedClass && (
                <div>
                  <div className="flex items-center mb-4">
                    <Button
                      type="link"
                      icon={<LeftOutlined />}
                      onClick={() => {
                        setSelectedClass(null);
                        setStudentList([]);
                      }}
                    >
                      返回班级列表
                    </Button>
                  </div>
                  <Table
                    loading={loading}
                    rowSelection={{
                      selectedRowKeys: selectedUserIds,
                      onChange: (selectedRowKeys, selectedRows) => {
                        const newSelectedMap = { ...selectedUserMap };
                        selectedRows.forEach((student: StudentInfo) => {
                          newSelectedMap[student.userId] = {
                            ...student,
                            id: student.userId
                          };
                        });
                        setSelectedUserMap(newSelectedMap);
                        setSelectedUserIds(selectedRowKeys as number[]);
                      }
                    }}
                    columns={[
                      {
                        title: '头像',
                        dataIndex: 'avatarUrl',
                        width: 60,
                        render: (avatarUrl: string) => (
                          <div className="w-8 h-8 rounded-full overflow-hidden">
                            <img
                              src={avatarUrl || 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png'}
                              alt=""
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.currentTarget as HTMLImageElement;
                                target.src = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png';
                                target.onerror = null;
                              }}
                            />
                          </div>
                        )
                      },
                      {
                        title: '姓名',
                        dataIndex: 'nickName',
                        render: (text, record) => text || record.phone || `用户${record.userId}`
                      },
                      {
                        title: '学号',
                        dataIndex: 'studentNumber'
                      },
                      {
                        title: '手机号',
                        dataIndex: 'phone',
                        render: (text: string) => text || '-'
                      }
                    ]}
                    dataSource={studentList}
                    rowKey="userId"
                    pagination={false}
                    scroll={{ y: 400 }}
                  />
                </div>
              )}
            </div>
          )}

          {selectMode === 'manual' ? (
            <Form.Item
              name="userId"
              label="选择用户"
              rules={[{ required: true, message: '请选择用户' }]}
            >
              <Select
                showSearch
                placeholder="请输入用户昵称或手机号搜索"
                filterOption={false}
                onSearch={debounceFetcher}
                loading={userSearchLoading}
                notFoundContent={userSearchLoading ? <Spin size="small" /> : null}
              >
                {searchedUsers.map((user) => (
                  <Option key={user.id} value={user.id}>
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 rounded-full overflow-hidden">
                        <img
                          src={user.avatarUrl || 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png'}
                          alt=""
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <span>{user.nickName || user.phone || `用户${user.id}`}</span>
                      {user.phone && <span className="text-gray-400">({user.phone})</span>}
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          ) : selectMode === 'csv' ? (
            <div>
              <Form.Item label="上传CSV文件">
                <Dragger
                  accept=".csv"
                  beforeUpload={(file) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                      const csv = e.target?.result;
                      if (typeof csv === 'string') {
                        const rows = csv.split('\n').map(row => row.split(','));
                        const headers = rows[0];
                        const users = rows.slice(1).map(row => {
                          const user: any = {};
                          headers.forEach((header, index) => {
                            user[header.trim()] = row[index]?.trim();
                          });
                          return user;
                        });
                        setCsvUsers(users);

                        // 匹配用户信息
                        studentApi.matchStudents({
                          students: users.map(user => ({
                            studentNumber: user.studentNumber,
                            schoolId: user.schoolId,
                            classId: user.classId
                          }))
                        }).then(res => {
                          if (res.data.code === 200) {
                            setCsvMatchResults(res.data.data);
                          }
                        });
                      }
                    };
                    reader.readAsText(file);
                    return false;
                  }}
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
                  <p className="ant-upload-hint">
                    支持 .csv 格式文件
                  </p>
                </Dragger>
              </Form.Item>

              {csvMatchResults.matched.length > 0 && (
                <div>
                  <div className="mb-4">
                    <Alert
                      message={`成功匹配 ${csvMatchResults.matched.length} 个用户`}
                      type="success"
                      showIcon
                    />
                  </div>
                  <Table
                    dataSource={csvMatchResults.matched}
                    columns={[
                      {
                        title: '学号',
                        dataIndex: 'studentNumber'
                      },
                      {
                        title: '姓名',
                        dataIndex: 'nickName'
                      },
                      {
                        title: '手机号',
                        dataIndex: 'phone'
                      }
                    ]}
                    rowKey="userId"
                    pagination={false}
                  />
                </div>
              )}
            </div>
          ) : null}

          <Form.Item
            name="packageId"
            label="选择套餐"
            rules={[{ required: true, message: '请选择套餐' }]}
          >
            <Select
              placeholder="请选择套餐"
              showSearch
              optionFilterProp="children"
            >
              {packageList.map((pkg) => (
                <Option key={pkg.id} value={pkg.id}>
                  {pkg.name} ({pkg.points} 能量点 / {pkg.validityDays} 天)
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="消息管理"
          >
            <div className="flex items-center">
              <Button
                type="link"
                onClick={() => {
                  setIsMessageSettingModalVisible(true);
                }}
              >
                设置消息选项
              </Button>
              <div className="ml-2 text-xs text-gray-500">
                {showInMessageCenter ? '消息中心: 开启' : '消息中心: 关闭'} |
                {enableNotification ? '弹窗通知: 开启' : '弹窗通知: 关闭'} |
                {showPackagePoints ? '显示点数: 开启' : '显示点数: 关闭'} |

              </div>
            </div>
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <Input.TextArea placeholder="请输入备注信息（选填）" />
          </Form.Item>

          <Form.Item
            name="showInMessageCenter"
            hidden={true}
            initialValue={false}
          />

          <Form.Item
            name="enableNotification"
            hidden={true}
            initialValue={true}
          />

          <Form.Item
            name="showPackagePoints"
            hidden={true}
            initialValue={true}
          />



          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              {isBatchAssign ? '批量分配' : '确认分配'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 消息设置模态框 */}
      <Modal
        title="消息设置"
        open={isMessageSettingModalVisible}
        onCancel={() => setIsMessageSettingModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsMessageSettingModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              setIsMessageSettingModalVisible(false);
            }}
          >
            确定
          </Button>
        ]}
        width={400}
        centered
      >
        <div className="py-4 space-y-6">

          <div>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-base font-medium">显示在消息中心</div>
                <div className="text-gray-500 text-sm">套餐分配记录显示在用户的消息中心</div>
              </div>
              <Switch
                checked={showInMessageCenter}
                onChange={(checked) => {
                  setShowInMessageCenter(checked);
                  assignPackageForm.setFieldsValue({ showInMessageCenter: checked });
                }}
              />
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-base font-medium">开启弹窗通知</div>
                <div className="text-gray-500 text-sm">用户收到套餐时会收到弹窗通知</div>
              </div>
              <Switch
                checked={enableNotification}
                onChange={(checked) => {
                  setEnableNotification(checked);
                  assignPackageForm.setFieldsValue({ enableNotification: checked });
                  console.log("111", assignPackageForm.getFieldValue("enableNotification"));
                  // 如果关闭弹窗通知，同时关闭显示套餐点数
                  if (!checked) {
                    setShowPackagePoints(false);
                    assignPackageForm.setFieldsValue({ showPackagePoints: false });
                  }
                }}
              />
            </div>
          </div>

          <div>
            <div className="flex justify-between items-center">
              <div>
                <div className="text-base font-medium">显示套餐点数</div>
                <div className="text-gray-500 text-sm">在弹窗通知中显示套餐包含的能量点数</div>
              </div>
              <Switch
                disabled={!enableNotification}
                checked={showPackagePoints}
                onChange={(checked) => {

                  setShowPackagePoints(checked);
                  assignPackageForm.setFieldsValue({ showPackagePoints: checked });


                }}
              />
            </div>
          </div>


        </div>
      </Modal>
    </>
  );
} 