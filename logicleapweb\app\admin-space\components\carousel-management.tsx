'use client'

import { useState } from 'react';
import { Card, Button, Modal, Form, Input, Select, Tag, Space, Upload } from 'antd';
import { PictureOutlined, UploadOutlined } from '@ant-design/icons';
import { carouselApi } from '@/lib/api/carousel';
import { uploadApi } from '@/lib/api/upload';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Option } = Select;

interface CarouselManagementProps {
  onCarouselCountChange?: (count: number) => void;
}

interface CarouselItem {
  id: number;
  type: number;
  displayType: number;
  largeTitle: string;
  mediumTitle: string;
  smallTitle: string;
  imageUrl: string;
  videoUrl: string;
  redirectUrl: string;
  sort: number;
  buttonStatus: number;
  buttonText: string;
  status: number;
}

export default function CarouselManagement({ onCarouselCountChange }: CarouselManagementProps) {
  const [isCarouselModalVisible, setIsCarouselModalVisible] = useState(false);
  const [isAddCarouselModalVisible, setIsAddCarouselModalVisible] = useState(false);
  const [carouselList, setCarouselList] = useState<CarouselItem[]>([]);
  const [carouselTotal, setCarouselTotal] = useState(0);
  const [addCarouselForm] = Form.useForm();
  const [editingCarousel, setEditingCarousel] = useState<CarouselItem | null>(null);
  const notification = GetNotification();

  // 获取轮播图列表
  const fetchCarouselList = async () => {
    try {
      const { data: res } = await carouselApi.getAllList();
      if (res.code === 200) {
        setCarouselList(res.data);
        setCarouselTotal(res.data.length);
        onCarouselCountChange?.(res.data.length);
      }
    } catch (error) {
      console.error('获取轮播图列表失败:', error);
      notification.error('获取轮播图列表失败');
    }
  };

  return (
    <>
      <Card
        title="轮播图管理"
        extra={<Button type="primary" onClick={() => {
          fetchCarouselList();
          setIsCarouselModalVisible(true);
        }}>查看全部</Button>}
        className="shadow-sm"
      >
        <div className="space-y-4">
          <Button block onClick={() => setIsAddCarouselModalVisible(true)}>
            添加轮播图
          </Button>
        </div>
      </Card>

      {/* 轮播图列表模态框 */}
      <Modal
        title={
          <div className="flex items-center justify-between">
            <span>轮播图管理</span>
            <Button type="primary" onClick={() => {
              setEditingCarousel(null);
              addCarouselForm.resetFields();
              setIsAddCarouselModalVisible(true);
            }}>
              添加轮播图
            </Button>
          </div>
        }
        open={isCarouselModalVisible}
        onCancel={() => setIsCarouselModalVisible(false)}
        width={1200}
        footer={null}
        zIndex={200}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
          {carouselList.map((item) => (
            <div key={item.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="aspect-w-16 aspect-h-9 rounded-t-lg overflow-hidden">
                <img
                  src={item.imageUrl}
                  alt={item.largeTitle}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png';
                  }}
                />
              </div>
              <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <Tag color={item.type === 0 ? 'blue' : 'green'}>
                    {item.type === 0 ? '图片' : '视频'}
                  </Tag>
                  <Tag color={item.status === 1 ? 'success' : 'default'}>
                    {item.status === 1 ? '启用' : '禁用'}
                  </Tag>
                </div>
                <h3 className="text-lg font-medium mb-2 truncate" title={item.largeTitle}>
                  {item.largeTitle || '无标题'}
                </h3>
                {item.mediumTitle && (
                  <p className="text-gray-500 text-sm mb-1 truncate" title={item.mediumTitle}>
                    {item.mediumTitle}
                  </p>
                )}
                {item.smallTitle && (
                  <p className="text-gray-400 text-sm mb-2 truncate" title={item.smallTitle}>
                    {item.smallTitle}
                  </p>
                )}
                <div className="flex items-center justify-between mt-4">
                  <span className="text-gray-500 text-sm">排序: {item.sort}</span>
                  <Space>
                    <Button
                      type="link"
                      size="small"
                      onClick={() => {
                        setEditingCarousel(item);
                        addCarouselForm.setFieldsValue(item);
                        setIsAddCarouselModalVisible(true);
                      }}
                    >
                      编辑
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      onClick={async () => {
                        try {
                          const { data: res } = await carouselApi.updateStatus(
                            item.id,
                            item.status === 1 ? 0 : 1
                          );
                          if (res.code === 200) {
                            notification.success(item.status === 1 ? '已禁用' : '已启用');
                            fetchCarouselList();
                          }
                        } catch (error) {
                          notification.error('操作失败');
                        }
                      }}
                    >
                      {item.status === 1 ? '禁用' : '启用'}
                    </Button>
                    <Button
                      type="link"
                      danger
                      size="small"
                      onClick={() => {
                        Modal.confirm({
                          title: '确认删除',
                          content: '确定要删除这个轮播图吗？',
                          onOk: async () => {
                            try {
                              const { data: res } = await carouselApi.delete(item.id);
                              if (res.code === 200) {
                                notification.success('删除成功');
                                fetchCarouselList();
                              }
                            } catch (error) {
                              notification.error('删除失败');
                            }
                          },
                        });
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Modal>

      {/* 添加/编辑轮播图模态框 */}
      <Modal
        title={editingCarousel ? '编辑轮播图' : '添加轮播图'}
        open={isAddCarouselModalVisible}
        onCancel={() => {
          setIsAddCarouselModalVisible(false);
          setEditingCarousel(null);
          addCarouselForm.resetFields();
        }}
        footer={null}
        width={800}
        zIndex={1001}
      >
        <Form
          form={addCarouselForm}
          layout="vertical"
          onFinish={async (values) => {
            try {
              const api = editingCarousel ? carouselApi.update : carouselApi.add;
              const data = editingCarousel ? { ...values, id: editingCarousel.id } : values;
              const { data: res } = await api(data);
              if (res.code === 200) {
                notification.success(editingCarousel ? '更新成功' : '添加成功');
                setIsAddCarouselModalVisible(false);
                setEditingCarousel(null);
                addCarouselForm.resetFields();
                fetchCarouselList();
              }
            } catch (error) {
              notification.error(editingCarousel ? '更新失败' : '添加失败');
            }
          }}
        >
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="type"
              label="类型"
              initialValue={0}
              hidden
            >
              <Select>
                <Option value={0}>图片</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="displayType"
              label="显示方式"
              rules={[{ required: true, message: '请选择显示方式' }]}
            >
              <Select>
                <Option value={1}>全显示</Option>
                <Option value={2}>半显示</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="largeTitle"
            label="大标题"
            rules={[{ required: true, message: '请输入大标题' }]}
          >
            <Input placeholder="请输入大标题" maxLength={50} showCount />
          </Form.Item>

          <Form.Item
            name="mediumTitle"
            label="中标题"
          >
            <Input placeholder="请输入中标题（选填）" maxLength={100} showCount />
          </Form.Item>

          <Form.Item
            name="smallTitle"
            label="小标题"
          >
            <Input placeholder="请输入小标题（选填）" maxLength={200} showCount />
          </Form.Item>

          <Form.Item
            name="imageUrl"
            label="轮播图"
            rules={[{ required: true, message: '请上传轮播图' }]}
            extra="建议尺寸 1920x1080，支持 jpg、png 格式"
          >
            <div className="space-y-4">
              {addCarouselForm.getFieldValue('imageUrl') && (
                <div className="relative w-full aspect-w-16 aspect-h-9 rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={addCarouselForm.getFieldValue('imageUrl')}
                    alt="预览图"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png';
                    }}
                  />
                  <div className="absolute top-2 right-2">
                    <Button
                      type="primary"
                      danger
                      size="small"
                      onClick={() => {
                        addCarouselForm.setFieldValue('imageUrl', '');
                      }}
                    >
                      删除图片
                    </Button>
                  </div>
                </div>
              )}
              <Upload
                accept=".jpg,.jpeg,.png"
                showUploadList={false}
                customRequest={async ({ file }) => {
                  try {
                    const url = await uploadApi.uploadToOss(file as File);
                    addCarouselForm.setFieldValue('imageUrl', url);
                    notification.success('上传成功');
                  } catch (error: any) {
                    notification.error(error.message || '上传失败');
                  }
                }}
              >
                <Button icon={<UploadOutlined />} block>
                  {addCarouselForm.getFieldValue('imageUrl') ? '重新上传' : '上传图片'}
                </Button>
              </Upload>
            </div>
          </Form.Item>

          <Form.Item
            name="redirectUrl"
            label="跳转URL"
          >
            <Input placeholder="点击轮播图后跳转的链接（选填）" />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="sort"
              label="排序"
              rules={[{ required: true, message: '请输入排序号' }]}
              extra="数字越小越靠前"
            >
              <Input type="number" min={0} max={9999} />
            </Form.Item>

            <Form.Item
              name="buttonStatus"
              label="按钮状态"
              rules={[{ required: true, message: '请选择按钮状态' }]}
            >
              <Select>
                <Option value={0}>隐藏</Option>
                <Option value={1}>显示</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="buttonText"
            label="按钮文字"
            extra="仅在按钮状态为显示时生效"
          >
            <Input placeholder="请输入按钮文字（选填）" maxLength={20} showCount />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              {editingCarousel ? '保存修改' : '确认添加'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
} 