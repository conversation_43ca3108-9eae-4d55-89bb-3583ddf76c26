/* eslint-disable import/no-nodejs-modules */
// eslint-disable-next-line import/no-commonjs
const BlockType = require('@scratch-vm/extension-support/block-type');
// eslint-disable-next-line import/no-commonjs
const ArgumentType = require('@scratch-vm/extension-support/argument-type');
// eslint-disable-next-line import/no-commonjs
const Cast = require('@scratch-vm/util/cast');
// eslint-disable-next-line import/no-commonjs
const JSZip = require('jszip');
// eslint-disable-next-line import/no-commonjs
const tf = require('@tensorflow/tfjs');
const {getExtensionPermissions} = require('../index.jsx');
// eslint-disable-next-line import/no-commonjs
const {createZipUploader} = require('../utils/zipUploader');

// 导入React和ReactDOM
// eslint-disable-next-line import/no-commonjs
const React = require('react');
// eslint-disable-next-line import/no-commonjs
const ReactDOM = require('react-dom');
// 导入React组件版本的TrainDialog
// eslint-disable-next-line import/no-commonjs
const TrainDialogComponent = require('../utils/trainUtil/trainDialog/train-dialog.jsx').default;

// 导入工具函数
const notification = require('../utils/notification').default;;
const createJsonFileUploader = require('../utils/jsonFileUploader');
const { createLoadingAnimation, updateLoadingProgress, removeLoadingAnimation } = require('../utils/loadingAnimation');

// 导入按钮处理模块
const buttonHandler = require('../utils/button-handler.js');

// 导入图片资源
// eslint-disable-next-line import/no-commonjs
const IconURI = require('./sound_train.svg');

const { loadModel, saveModel, validateModelFile, predictAudioData } = require('./modelUtils');

// 移除对 API_URL 的直接依赖
// const { API_URL } = require('../../../../config/config.js');
// 导入API模块
const workApi = require('@api/work_api.ts').default;

// 添加loadSpeechCommands函数
// 修改动态加载方式
const loadSpeechCommands = async () => {
  try {
    // 先检查是否已经加载
    if (window.speechCommands) {
      return window.speechCommands;
    }

    // 如果没有加载，先加载 TensorFlow.js
    if (!window.tf) {
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = '/static/utils/sound_train/tf.min.js';
        script.crossOrigin = 'anonymous';
        
        script.onload = () => {
          resolve();
        };
        
        script.onerror = (error) => {
          console.error('TensorFlow.js 加载失败:', error);
          console.error('加载失败的文件路径:', script.src);
          reject(error);
        };
        
        document.head.appendChild(script);
      });
    }

    // 然后加载 speech-commands
    await new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/static/utils/sound_train/speech-commands.min.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => {
        resolve();
      };
      
      script.onerror = (error) => {
        console.error('speech-commands 加载失败:', error);
        console.error('加载失败的文件路径:', script.src);
        reject(error);
      };
      
      document.head.appendChild(script);
    });

    if (window.speechCommands) {
      return window.speechCommands;
    } else {
      throw new Error('语音命令模型加载后未找到 window.speechCommands 对象');
    }
    
  } catch (error) {
    console.error('加载语音命令模型时发生错误:', error);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
};

class LogicLeapSoundTrainBlocks {
    constructor (runtime) {
        /**
         * The runtime instantiating this block package.
         * @type {Runtime}
         */
        this.runtime = runtime;

        // 初始化扩展ID
        this.extensionId = 'logicleapSoundTrain';

        // 初始化各种状态
        this.dialogContainer = null;
        this.dialogRoot = null;
        
        // 模型相关状态
        this.modelData = null;
        this.modelImported = false;
        this.modelCount = 0;
        this.modelCache = {};
        this.modelDataCache = {};
        this.currentModel = null;
        this.lastPrediction = null;
        this._hadModelLayerError = false; // 记录模型层错误状态
        this.trainedModels = {}; // 初始化为空对象，不再直接存储模型实例
        
        // 音频相关状态
        this.audioContext = null;
        this.mediaStream = null;
        this.audioAnalyser = null;
        this.isRecording = false;
        this.recordingTimeout = null;
        this.recognizer = null;

        // 音频处理相关常量
        this.NUM_FRAMES = 3;  // 添加这个常量定义
        this.INPUT_SHAPE = [this.NUM_FRAMES, 232, 1];

        // 从预加载的权限数据中获取本扩展的权限
        this.visibleBlocks = getExtensionPermissions('logicleapSoundTrain');

        // 绑定方法
        this.showTrainDialogSound = this.showTrainDialogSound.bind(this);
        this.closeTrainDialogSound = this.closeTrainDialogSound.bind(this);
        this.uploadModel = this.uploadModel.bind(this);
        this.importModel = this.importModel.bind(this);
        this.loadModelByNumber = this.loadModelByNumber.bind(this);
        this.getModelNumber = this.getModelNumber.bind(this);
        this.listCachedModels = this.listCachedModels.bind(this);
        this.predictSound = this.predictSound.bind(this);
        this.cleanupModelCache = this.cleanupModelCache.bind(this);
        this.openTrainerWithLabels = this.openTrainerWithLabels.bind(this);

        // 初始化模型缓存
        try {
            const savedCacheIndex = localStorage.getItem('logicleap_sound_model_cache_index');
            if (savedCacheIndex) {
                this.modelCache = JSON.parse(savedCacheIndex);
                
                // 从缓存中找出最大的模型编号
                const modelNumbers = Object.keys(this.modelCache).map(Number);
                this.modelCount = modelNumbers.length > 0 ? Math.max(...modelNumbers) : 0;
                
                // 不直接从localStorage加载模型数据，仅维护索引信息
            }
        } catch (error) {
            console.error('初始化模型缓存索引失败:', error);
            this.modelCache = {};
        }

        // 初始化模型数据
        this.initializeModelData();

        // 保存实例引用到全局变量
        window._logicleapSoundTrainInstance = this;

        // 初始化按钮处理模块
        buttonHandler.initialize(this);

        // 注册"训练模型"按钮
        buttonHandler.registerButton(
            this.extensionId, 
            '训练声音模型', 
            'showTrainDialogSound', 
            this,
            { color: '#F8C91C' } // 黄色
        );

        // 注册"上传模型"按钮
        buttonHandler.registerButton(
            this.extensionId, 
            '上传声音模型', 
            'importModel', 
            this,
            { color: '#FFA726' } // 橙色
        );
    }

    getInfo () {
        // 定义所有积木块
        const allBlocks = [
            {
                opcode: 'showTrainDialogSound',
                blockId: 'train_show_dialog_sound',
                blockType: BlockType.BUTTON,
                text: '训练声音模型'
            },
            {
                opcode: 'importModel',
                blockId: 'train_import_model_sound',
                blockType: BlockType.BUTTON,
                text: '上传声音模型'
            },
            {
                opcode: 'showSoundIdentifyWindow',
                blockId: 'train_show_sound_identify_window',
                blockType: BlockType.COMMAND,
                text: '打开声音识别窗口',
            },
            {
                opcode: 'openTrainerWithLabels',
                blockId: 'train_open_trainer_with_labels_sound',
                blockType: BlockType.COMMAND,
                text: '打开训练器, 标签1 [LABEL1], 标签2 [LABEL2]',
                arguments: {
                    LABEL1: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别1'
                    },
                    LABEL2: {
                        type: ArgumentType.STRING,
                        defaultValue: '类别2'
                    }
                }
            },
            {
                opcode: 'loadModelByNumber',
                blockId: 'train_load_model_by_number_sound',
                blockType: BlockType.COMMAND,
                text: '加载编号为 [MODEL_NUMBER] 的声音模型',
                arguments: {
                    MODEL_NUMBER: {
                        type: ArgumentType.STRING,
                        defaultValue: '1'
                    }
                }
            },
            {
                opcode: 'predictSound',
                blockId: 'train_predict_sound',
                blockType: BlockType.REPORTER,
                text: '使用麦克风预测声音的 [PROPERTY]',
                arguments: {
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'soundProperties',
                        defaultValue: 'label'
                    }
                }
            },
            {
                opcode: 'predictAudioData',
                blockId: 'train_predict_wav_file',
                blockType: BlockType.REPORTER,
                text: '预测音频数据 [AUDIO_PATH] 的 [PROPERTY]',
                arguments: {
                    AUDIO_PATH: {
                        type: ArgumentType.STRING,
                        defaultValue: '音频数据'
                    },
                    PROPERTY: {
                        type: ArgumentType.STRING,
                        menu: 'soundProperties',
                        defaultValue: 'label'
                    }
                }
            },
            
            {
                opcode: 'getModelNumber',
                blockId: 'train_get_model_number_sound',
                blockType: BlockType.REPORTER,
                text: '当前声音模型编号'
            },
            // {
            //     opcode: 'listCachedModels',
            //     blockId: 'train_list_cached_models_sound',
            //     blockType: BlockType.REPORTER,
            //     text: '已缓存的所有声音模型编号'
            // }
        ];

        // 根据权限过滤积木块
        const blocks = allBlocks.filter(block => this.visibleBlocks[block.blockId] === true);

        return {
            id: 'logicleapSoundTrain',
            name: '声音训练',
            color1: '#F8C91C',
            blockIconURI: IconURI,
            menuIconURI: IconURI,
            blocks: blocks,
            menus: {
                soundProperties: {
                    acceptReporters: false,
                    items: [
                        {
                            text: '标签',
                            value: 'label'
                        },
                        {
                            text: '置信度',
                            value: 'confidence'
                        },
                        {
                            text: '标签和置信度',
                            value: 'both'
                        }
                    ]
                }
            }
        };
    }

    showTrainDialogSound () {
        // 如果对话框已经打开，先关闭它
        if (this.dialogRoot) {
            this.closeTrainDialog();
        }

        // 创建对话框容器
        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        // 禁用页面滚动
        document.body.style.overflow = 'hidden';
        
        // 渲染React组件
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'sound',
                onClose: this.closeTrainDialogSound
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
            }
        );
        
        notification.show('已打开训练窗口', 'success');
        return ;
    }
    
    closeTrainDialogSound () {
        if (this.dialogRoot) {
            // 卸载React组件
            ReactDOM.unmountComponentAtNode(this.dialogRoot);
            // 移除容器
            if (this.dialogRoot.parentNode) {
                this.dialogRoot.parentNode.removeChild(this.dialogRoot);
            }
            // 重置引用
            this.dialogRoot = null;
            this.dialogContainer = null;
            
            // 恢复页面滚动
            document.body.style.overflow = '';
            
            ////console.log('训练窗口已关闭');
        }
    }

    openTrainerWithLabels (args) {
        const { LABEL1, LABEL2 } = args;
        const classLabels = [LABEL1, LABEL2].filter(Boolean);
        if (classLabels.length === 0) {
            classLabels.push('类别1', '类别2');
        } else if (classLabels.length === 1) {
            classLabels.push('类别2');
        }

        if (this.dialogRoot) {
            this.closeTrainDialogSound();
        }

        this.dialogContainer = document.createElement('div');
        this.dialogContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        `;
        document.body.appendChild(this.dialogContainer);
        
        document.body.style.overflow = 'hidden';
        
        ReactDOM.render(
            React.createElement(TrainDialogComponent, {
                modeString: 'sound',
                onClose: this.closeTrainDialogSound,
                initialClassLabels: classLabels
            }),
            this.dialogContainer,
            () => {
                this.dialogRoot = this.dialogContainer;
            }
        );
        
        notification.show('已打开训练窗口', 'success');
        return ;
    }

    async showSoundIdentifyWindow () {
        if(this.soundIdentifyRoot!=null){
            notification.show('请关闭之前打开的声音识别窗口', 'error');
            return;
        }
        // 检查模型是否已导入 - 同时检查全局模型和实例模型
        // 检查模型是否已导入 - 同时检查全局模型和实例模型
        try {
            const modelLoaded = (window.LogicLeapSoundModel && window.LogicLeapSoundModel.model) || 
            (this.modelImported && this.currentModelTF);
    
            if (!modelLoaded) {
                notification.show('请先导入或训练声音模型', 'error');
                return ;
            }
            if (!modelLoaded) {
            notification.show('请先导入或训练声音模型', 'error');
            return;
            }
        }catch(error){
            console.error('检查模型是否已导入失败:', error);
            notification.show('检查模型是否已导入失败: ' + error.message, 'error');
            return ;
        }

        let model = null;
        let classLabels = [];
        
        // 检查全局模型是否可用
        if (window.LogicLeapSoundModel && window.LogicLeapSoundModel.model) {
            model = window.LogicLeapSoundModel.model;
            classLabels = window.LogicLeapSoundModel.metadata.classes || [];
        }
        // 如果全局模型不可用，使用实例模型
        else if (this.modelImported && this.currentModelTF) {
            model = this.currentModelTF;
            classLabels = this.currentModelMetadata.classes || [];
        }
        
        if (!model) {
            throw new Error('模型不可用，请重新加载模型');
        }
        

        // 创建一个悬浮窗口来显示声音识别窗口
        const tempDiv = document.createElement('div');
        tempDiv.style.cssText = `
            position: fixed;
            top: 50px;
            left: 50px;
            z-index: 9999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            overflow: hidden;
        `;
        
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            width: 280px;
            max-width: 280px;
            min-width: 280px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            border-radius: 8px;
        `;
        
        const modalHeader = document.createElement('div');
        modalHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background:rgb(255, 255, 255);
            color: #333;
            cursor: move;
            border-bottom: 1px solid #e0e0e0;
            user-select: none;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        `;
        
        const modalTitle = document.createElement('h3');
        modalTitle.textContent = '识别窗口';
        modalTitle.style.cssText = `
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        `;
        
        const closeButton = document.createElement('button');
        closeButton.textContent = 'X';
        closeButton.style.cssText = `
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 4px 8px;
        `;
        closeButton.onclick = () => {
            document.body.removeChild(tempDiv);
            if (this.soundIdentifyRoot) {
                ReactDOM.unmountComponentAtNode(this.soundIdentifyRoot);
                this.soundIdentifyRoot = null;
            }
        };
        
        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeButton);
        modalContent.appendChild(modalHeader);
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            padding: 16px;
            overflow-y: auto;
        `;
        modalContent.appendChild(contentContainer);
        
        // 创建React组件挂载点
        this.soundIdentifyRoot = document.createElement('div');
        contentContainer.appendChild(this.soundIdentifyRoot);
        tempDiv.appendChild(modalContent);
        document.body.appendChild(tempDiv);
        
        // 添加拖动功能
        let isDragging = false;
        let offsetX, offsetY;
        
        modalHeader.onmousedown = (e) => {
            isDragging = true;
            offsetX = e.clientX - tempDiv.getBoundingClientRect().left;
            offsetY = e.clientY - tempDiv.getBoundingClientRect().top;
            
            // 添加鼠标样式反馈
            modalHeader.style.cursor = 'grabbing';
        };
        
        document.onmousemove = (e) => {
            if (!isDragging) return;
            
            const left = e.clientX - offsetX;
            const top = e.clientY - offsetY;
            
            // 确保窗口不会拖出视口
            const maxX = window.innerWidth - tempDiv.offsetWidth;
            const maxY = window.innerHeight - tempDiv.offsetHeight;
            
            tempDiv.style.left = `${Math.max(0, Math.min(left, maxX))}px`;
            tempDiv.style.top = `${Math.max(0, Math.min(top, maxY))}px`;
        };
        
        document.onmouseup = () => {
            isDragging = false;
            modalHeader.style.cursor = 'move';
        };
        
        try {
            // 导入并渲染SoundIdentifyWindow组件
            const SoundIdentifyWindow = require('./SoundIdentifyWindow.jsx').default;
            
            if (SoundIdentifyWindow) {
                ReactDOM.render(
                    React.createElement(SoundIdentifyWindow, {
                        model: model,
                        classLabels: classLabels
                    }),
                    this.soundIdentifyRoot
                );
            } else {
                document.body.removeChild(tempDiv);
            }
        } catch (error) {
            console.error('渲染声音识别窗口时出错:', error);
            document.body.removeChild(tempDiv);
        }
    }
    
    /**
     * 上传已训练的模型
     * @returns {Promise<string>} 操作状态
     */
    async uploadModel() {
        try {
            // 使用uploader工具上传ZIP文件
            const file = await createZipUploader({
                maxSize: 10 * 1024 * 1024, // 10MB限制
                onLoading: () => {
                    notification.show('正在处理模型文件...', 'info');
                },
                onError: (error) => {
                    notification.show('上传失败: ' + error.message, 'error');
                },
                validateZip: async (file) => {
                    // 直接使用modelUtils的validateModelFile验证文件
                    return await validateModelFile(file);
                }
            });
            
            // 如果用户取消了选择，则返回
            if (!file) {
                notification.show('取消上传', 'info');
                return ;
            }

            try {
                // 显示加载动画
                const loadingAnimation = createLoadingAnimation(`正在解析模型文件...`);
                updateLoadingProgress('解析中...');
                
                // 直接使用现有的loadModel函数验证模型
                const { metadata } = await loadModel(file);
                
                updateLoadingProgress('保存模型到浏览器缓存...');
                
                // 创建新的模型编号，为现有最大编号+1
                this.modelCount++;
                const modelNumber = this.modelCount.toString();
                
                // 获取类别标签
                const labels = metadata?.userMetadata?.classes || 
                               metadata?.labels || 
                               ['类别1', '类别2', '类别3'];
                
                // 为模型创建名称
                const modelName = `声音模型 ${modelNumber}`;
                
                // 更新缓存索引
                this.modelCache[modelNumber] = {
                    modelName: modelName,
                    importTime: new Date().toISOString(),
                    labels: labels
                };
                
                // 保存模型文件到缓存
                try {
                    // 将文件转换为Base64
                    const reader = new FileReader();
                    const fileData = await new Promise((resolve, reject) => {
                        reader.onload = () => resolve(reader.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(file);
                    });
                    
                    // 去除Data URL前缀，只保留Base64部分
                    const base64Data = fileData.split(',')[1];
                    
                    // 保存到localStorage
                    localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                    localStorage.setItem(`logicleap_sound_model_zip_${modelNumber}`, base64Data);
                    
                    // 清理过多的缓存，仅保留最新的5个模型
                    await this.cleanupModelCache();
                    
                    // 触发模型缓存更新事件
                    this.dispatchModelCacheUpdatedEvent();
                    
                    // 移除加载动画
                    removeLoadingAnimation();
                    return ;
                } catch (storageError) {
                    console.error('保存模型文件到本地缓存失败:', storageError);
                    // 如果存储失败，清理缓存
                    removeLoadingAnimation();
                    await this.cleanupModelCache();
                    try {
                        localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                        notification.show(`模型"${modelName}"已上传但无法保存到浏览器缓存，请尝试清理浏览器存储空间`, 'warning');
                        return ;
                    } catch (e) {
                        console.error('即使清理后仍无法保存模型索引:', e);
                        notification.show('无法保存模型到浏览器缓存，请尝试清理浏览器存储空间', 'error');
                        return ;
                    }
                }
                
            } catch (parseError) {
                console.error('解析模型文件失败:', parseError);
                notification.show('解析模型文件失败: ' + parseError.message, 'error');
                return ;
            }

        } catch (error) {
            console.error('上传模型错误:', error);
            notification.show('上传模型失败: ' + error.message, 'error');
            return ;
        }
    }

    /**
     * 导入已训练的模型
     * @returns {string} 操作状态
     */
    async importModel() {
        try {


            // 创建模型选择弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 24px;
                border-radius: 8px;
                width: 600px;
                max-width: 90%;
                max-height: 80vh;
                display: flex;
                flex-direction: column;
            `;

            const modalTitle = document.createElement('h3');
            modalTitle.textContent = '导入已上传的声音模型';
            modalTitle.style.cssText = `
                margin: 0 0 16px 0;
                font-size: 20px;
            `;

            // 创建标签页容器
            const tabsContainer = document.createElement('div');
            tabsContainer.style.cssText = `
                display: flex;
                border-bottom: 1px solid #e0e0e0;
                margin-bottom: 16px;
            `;

            // 创建本地模型标签
            const localTab = document.createElement('div');
            localTab.textContent = '本地模型';
            localTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                font-weight: bold;
                color: #4766C2;
                border-bottom: 2px solid #4766C2;
            `;
            
            // 创建云端模型标签
            const cloudTab = document.createElement('div');
            cloudTab.textContent = '云端模型';
            cloudTab.style.cssText = `
                padding: 8px 16px;
                cursor: pointer;
                color: #666;
            `;
            
            tabsContainer.appendChild(localTab);
            tabsContainer.appendChild(cloudTab);

            const modelList = document.createElement('div');
            modelList.style.cssText = `
                flex: 1;
                overflow-y: auto;
                margin-bottom: 16px;
                min-height: 200px;
                max-height: 400px;
            `;

            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                justify-content: space-between;
                gap: 16px;
            `;

            const uploadButton = document.createElement('button');
            uploadButton.textContent = '上传并导入新模型';
            uploadButton.style.cssText = `
                padding: 8px 16px;
                background: #4766C2;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                &:hover {
                    background: #3d57a3;
                }
            `;

            const cancelButton = document.createElement('button');
            cancelButton.textContent = '取消';
            cancelButton.style.cssText = `
                padding: 8px 16px;
                background: #f5f5f5;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                &:hover {
                    background: #e8e8e8;
                }
            `;

            buttonContainer.appendChild(uploadButton);
            buttonContainer.appendChild(cancelButton);

            modalContent.appendChild(modalTitle);
            modalContent.appendChild(tabsContainer);
            modalContent.appendChild(modelList);
            modalContent.appendChild(buttonContainer);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 显示加载中提示的辅助函数
            const showLoading = (message) => {
                modelList.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                        <p style="color: #666;">${message}</p>
                    </div>
                `;
            };

            // 显示错误提示的辅助函数
            const showError = (message) => {
                modelList.innerHTML = `
                    <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
                        <p style="color: #ff4d4f;">${message}</p>
                    </div>
                `;
            };

            // 加载本地模型
            const loadLocalModels = () => {
                showLoading('正在加载本地模型...');
                
                try {
                    // 从localStorage获取模型缓存索引
                    const cacheIndex = localStorage.getItem('logicleap_sound_model_cache_index');
                    if (!cacheIndex) {
                        modelList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #666;">
                                暂无保存的本地声音模型
                            </div>
                        `;
                        return;
                    }
                    
                    const modelCache = JSON.parse(cacheIndex);
                    const models = Object.entries(modelCache).map(([number, data]) => ({
                        number,
                        name: data.modelName,
                        importTime: data.importTime,
                        labels: data.labels || []
                    }));
                    
                    if (models.length === 0) {
                        modelList.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #666;">
                                暂无保存的本地声音模型
                            </div>
                        `;
                        return;
                    }
                    
                    // 清空现有内容
                    modelList.innerHTML = '';
                    
                    // 按导入时间倒序排序
                    models.sort((a, b) => new Date(b.importTime) - new Date(a.importTime));
                    
                    // 渲染每个模型项
                    models.forEach(model => {
                        const modelItem = document.createElement('div');
                        modelItem.style.cssText = `
                            padding: 16px;
                            border: 1px solid #e8e8e8;
                            border-radius: 4px;
                            margin-bottom: 8px;
                            cursor: pointer;
                            transition: all 0.3s;
                            &:hover {
                                background: #f5f5f5;
                                border-color: #4766C2;
                            }
                        `;

                        const labelText = model.labels && model.labels.length > 0 
                            ? `包含 ${model.labels.length} 个类别的声音模型` 
                            : '声音模型';
                        
                        modelItem.innerHTML = `
                            <div style="font-weight: 500; margin-bottom: 8px;">
                                ${model.name} (编号: ${model.number})
                            </div>
                            <div style="color: #666; font-size: 14px; margin-bottom: 4px;">
                                ${labelText}
                            </div>
                            <div style="color: #999; font-size: 12px;">
                                导入时间: ${new Date(model.importTime).toLocaleString()}
                            </div>
                        `;

                        // 为每个模型项添加点击事件
                        modelItem.onclick = async () => {
                            try {
                                // 显示加载状态
                                modelItem.style.opacity = '0.5';
                                modelItem.style.pointerEvents = 'none';
                                
                                document.body.removeChild(modal);
                            } catch (error) {
                                console.error('处理本地模型失败:', error);
                                modelItem.style.opacity = '1';
                                modelItem.style.pointerEvents = 'auto';
                                notification.show('处理模型失败: ' + error.message, 'error');
                            }
                        };
                        
                        modelList.appendChild(modelItem);
                    });
                    
                } catch (error) {
                    console.error('加载本地模型失败:', error);
                    showError('加载本地模型失败: ' + error.message);
                }
            };

            // 加载云端模型
            const loadCloudModels = async () => {
                showLoading('正在加载云端模型列表...');
                
                try {
                    // 改用 workApi.getSoundModelsList()
                    const result = await workApi.getSoundModelsList();
                    
                    if (result && result.code === 200 && result.data) {
                        const models = result.data || [];
                        
                        if (models.length === 0) {
                            modelList.innerHTML = `
                                <div style="text-align: center; padding: 20px; color: #666;">
                                    暂无保存的云端声音模型
                                </div>
                            `;
                            return;
                        }
                        
                        // 清空现有内容
                        modelList.innerHTML = '';
                        
                        // 渲染云端模型列表
                        models.forEach(model => {
                            const modelItem = document.createElement('div');
                            modelItem.style.cssText = `
                                padding: 16px;
                                border: 1px solid #e8e8e8;
                                border-radius: 4px;
                                margin-bottom: 8px;
                                cursor: pointer;
                                transition: all 0.3s;
                                &:hover {
                                    background: #f5f5f5;
                                    border-color: #4766C2;
                                }
                            `;

                            const labelCount = model.labels ? model.labels.length : 0;
                            modelItem.innerHTML = `
                                <div style="font-weight: 500; margin-bottom: 8px;">${model.name}</div>
                                <div style="color: #666; font-size: 14px; margin-bottom: 4px;">
                                    ${model.description || '无描述'}
                                </div>
                                <div style="color: #999; font-size: 12px; display: flex; justify-content: space-between;">
                                    <span>类别数: ${labelCount}</span>
                                    <span>创建时间: ${new Date(model.createTime).toLocaleString()}</span>
                                    ${model.isPublic ? '<span style="color: #52c41a;">公开</span>' : '<span style="color: #ff4d4f;">私有</span>'}
                                </div>
                            `;

                            // 点击事件 - 导入选中的模型
                            modelItem.onclick = async () => {
                                try {
                                    // 显示加载中状态
                                    modelItem.style.opacity = '0.5';
                                    modelItem.style.pointerEvents = 'none';

                                    // 直接使用fileUrl下载模型文件
                                    const modelResponse = await fetch(model.fileUrl);
                                    if (!modelResponse.ok) {
                                        throw new Error('下载模型文件失败');
                                    }

                                    const modelBlob = await modelResponse.blob();
                                    if (!modelBlob || modelBlob.size === 0) {
                                        throw new Error('下载的模型文件为空');
                                    }

                                    const modelFile = new File([modelBlob], 'model.zip', { type: 'application/zip' });

                                    // 关闭弹窗
                                    document.body.removeChild(modal);
                                    
                                    // 创建加载动画
                                    const loadingAnimation = createLoadingAnimation(`正在保存云端模型"${model.name}"...`);
                                    
                                    try {
                                        // 更新模型编号 - 自增
                                        this.modelCount++;
                                        const modelNumberToUse = String(this.modelCount);
                                        
                                        // 获取类别标签
                                        const labels = model.labels || ['类别1', '类别2', '类别3'];
                                        
                                        // 更新内存中的模型索引信息
                                        this.modelCache[modelNumberToUse] = {
                                            modelName: model.name,
                                            importTime: new Date().toISOString(),
                                            labels: labels
                                        };
                                        
                                        // 将文件转换为Base64
                                        const reader = new FileReader();
                                        const fileData = await new Promise((resolve, reject) => {
                                            reader.onload = () => resolve(reader.result);
                                            reader.onerror = reject;
                                            reader.readAsDataURL(modelFile);
                                        });
                                        
                                        // 去除Data URL前缀，只保留Base64部分
                                        const base64Data = fileData.split(',')[1];
                                        
                                        // 保存到localStorage
                                        try {
                                            localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                                            localStorage.setItem(`logicleap_sound_model_zip_${modelNumberToUse}`, base64Data);
                                            
                                            // 触发模型缓存更新事件
                                            this.dispatchModelCacheUpdatedEvent();
                                            
                                            // 移除加载动画
                                            removeLoadingAnimation();
                                        } catch (storageError) {
                                            console.error('保存模型到浏览器缓存失败:', storageError);
                                            // 如果保存失败，尝试清理旧模型
                                            this.cleanupModelCache();
                                            
                                            // 再次尝试保存
                                            try {
                                                localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                                                localStorage.setItem(`logicleap_sound_model_zip_${modelNumberToUse}`, base64Data);
                                                
                                                // 触发模型缓存更新事件
                                                this.dispatchModelCacheUpdatedEvent();
                                                
                                                // 移除加载动画
                                                removeLoadingAnimation();
                                            } catch (err) {
                                                console.error('清理后仍然无法保存模型:', err);
                                                removeLoadingAnimation();
                                                notification.show('保存模型失败: ' + err.message, 'error');
                                            }
                                        }
                                    } catch (error) {
                                        console.error('保存云端模型失败:', error);
                                        removeLoadingAnimation();
                                        notification.show('保存模型失败: ' + error.message, 'error');
                                    }
                                } catch (error) {
                                    console.error('获取云端模型失败:', error);
                                    notification.show('获取模型失败: ' + error.message, 'error');
                                    // 恢复模型项的显示状态
                                    modelItem.style.opacity = '1';
                                    modelItem.style.pointerEvents = 'auto';
                                }
                            };

                            modelList.appendChild(modelItem);
                        });
                    } else {
                        throw new Error(result.message || '获取模型列表失败');
                    }
                } catch (error) {
                    console.error('获取模型列表失败:', error);
                    showError('获取模型列表失败: ' + error.message);
                }
            };

            // 标签切换事件
            localTab.addEventListener('click', () => {
                localTab.style.fontWeight = 'bold';
                localTab.style.color = '#4766C2';
                localTab.style.borderBottom = '2px solid #4766C2';
                
                cloudTab.style.fontWeight = 'normal';
                cloudTab.style.color = '#666';
                cloudTab.style.borderBottom = 'none';
                
                loadLocalModels();
            });
            
            cloudTab.addEventListener('click', () => {
                cloudTab.style.fontWeight = 'bold';
                cloudTab.style.color = '#4766C2';
                cloudTab.style.borderBottom = '2px solid #4766C2';
                
                localTab.style.fontWeight = 'normal';
                localTab.style.color = '#666';
                localTab.style.borderBottom = 'none';
                
                loadCloudModels();
            });

            // 绑定按钮事件
            uploadButton.onclick = async () => {
                document.body.removeChild(modal);
                await this.uploadModel();
            };

            cancelButton.onclick = () => {
                document.body.removeChild(modal);
            };

            // 默认加载本地模型
            loadLocalModels();

            return ;
        } catch (error) {
            console.error('导入模型出错:', error);
            notification.show('导入模型失败: ' + error.message, 'error');
            return ;
        }
    }

    /**
     * 内部方法：导入模型数据
     * @param {string} modelData - 模型数据
     * @param {string} modelName - 模型名称
     * @param {string} [modelNumber] - 可选的模型编号
     * @private
     */
    async _importModelData(modelFile, modelName, modelNumber = null, metadata = {}) {
        // 显示加载动画
        const loadingAnimation = createLoadingAnimation(`正在导入模型"${modelName}"...`);
        updateLoadingProgress('初始化...');
        
        try {
            updateLoadingProgress('解析模型数据...');
            
            // 使用loadModel函数加载模型
            const { model, metadata: modelMetadata } = await loadModel(modelFile);
            
            updateLoadingProgress('导入模型中...');
            
            // 如果有现有模型，先释放内存
            if (this.currentModelTF) {
                try {
                    this.currentModelTF.dispose();
                } catch (e) {
                    console.warn('清理旧模型时出错:', e);
                }
            }
            
            // 保存模型
            this.currentModelTF = model;
            this.modelImported = true;
            
            // 更新模型编号
            let modelNumberToUse;
            if (modelNumber) {
                modelNumberToUse = modelNumber;
            } else {
                this.modelCount++;
                modelNumberToUse = String(this.modelCount);
            }
            
            // 获取类别标签
            const labels = metadata.labels || 
                         modelMetadata?.userMetadata?.classes || 
                         modelMetadata?.labels || 
                         ['类别1', '类别2', '类别3'];
            
            // 更新内存中的模型索引信息
            this.modelCache[modelNumberToUse] = {
                modelName: modelName,
                importTime: new Date().toISOString(),
                labels: labels
            };
            
            // 保存当前模型编号
            this.currentModel = modelNumberToUse;
            
            // 保存模型元数据
            this.currentModelMetadata = {
                classes: labels,
                numFrames: modelMetadata?.userMetadata?.numFrames || 3
            };
            
            // 设置全局模型对象
            window.LogicLeapSoundModel = {
                model: model,
                metadata: {
                    classes: labels,
                    numFrames: modelMetadata?.userMetadata?.numFrames || 3
                }
            };
            
            // 保存模型文件到缓存
            try {
                // 将文件转换为Base64
                const reader = new FileReader();
                const fileData = await new Promise((resolve, reject) => {
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(modelFile);
                });
                
                // 去除Data URL前缀，只保留Base64部分
                const base64Data = fileData.split(',')[1];
                
                // 保存到localStorage
                localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                localStorage.setItem(`logicleap_sound_model_zip_${modelNumberToUse}`, base64Data);
                
                // 清理过多的缓存，仅保留最新的5个模型
                await this.cleanupModelCache();
            } catch (storageError) {
                console.error('保存模型文件到本地缓存失败:', storageError);
                await this.cleanupModelCache();
                localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
            }
            
            updateLoadingProgress('完成！');
            setTimeout(() => {
                removeLoadingAnimation();
            }, 500);
            
            notification.show(`模型"${modelName}"导入成功（编号：${modelNumberToUse}）`, 'success');
            return ;
            
        } catch (error) {
            console.error('导入模型失败:', error);
            removeLoadingAnimation();
            notification.show('导入模型失败: ' + error.message, 'error');
            throw error;
        }
    }

    /**
     * 从缓存获取模型文件
     * @private
     * @param {string} modelNumber - 模型编号
     * @returns {Promise<File|null>} 模型文件或null
     */
    async getModelFileFromCache(modelNumber) {
        try {
            // 首先尝试从IndexedDB直接加载模型
            try {
                const tf = require('@tensorflow/tfjs');
                const model = await tf.loadLayersModel(`indexeddb://sound-model-${modelNumber}`);
                
                if (model) {
                    // 从localStorage获取模型元数据
                    const modelDataJson = localStorage.getItem(`logicleap_sound_model_data_${modelNumber}`);
                    if (!modelDataJson) {
                        throw new Error('无法获取模型元数据');
                    }
                    
                    const modelData = JSON.parse(modelDataJson);
                    
                    // 创建完整的模型数据结构
                    const completeModelData = {
                        model: model,
                        metadata: modelData.metadata || {
                            classes: this.modelCache[modelNumber]?.labels || [],
                            numFrames: 3
                        }
                    };
                    
                    // 返回一个标记为已处理的对象
                    return {
                        __alreadyProcessed: true,
                        modelData: completeModelData
                    };
                }
            } catch (indexedDBError) {
                console.warn('从IndexedDB加载模型失败，尝试其他方法:', indexedDBError);
                // 继续尝试其他方法
            }
            
            // 优先尝试从localStorage获取ZIP格式的模型数据
            const modelZipData = localStorage.getItem(`logicleap_sound_model_zip_${modelNumber}`);
            if (modelZipData) {
                // 从Base64转换回Blob
                const binary = atob(modelZipData);
                const bytes = new Uint8Array(binary.length);
                for (let i = 0; i < binary.length; i++) {
                    bytes[i] = binary.charCodeAt(i);
                }
                
                // 创建File对象
                return new File([bytes.buffer], `sound-model-${modelNumber}.zip`, { type: 'application/zip' });
            }
            
            // 如果没有ZIP数据，尝试从localStorage获取JSON格式的模型数据
            const modelJsonData = localStorage.getItem(`logicleap_sound_model_data_${modelNumber}`);
            if (modelJsonData) {
                try {
                    const modelJson = JSON.parse(modelJsonData);
                    
                    // 如果是序列化的模型数据，尝试创建一个ZIP文件
                    const zip = new JSZip();
                    
                    // 添加model.json
                    zip.file('model.json', JSON.stringify(modelJson));
                    
                    // 如果有权重数据，添加weights.bin
                    if (modelJson.encodedWeights) {
                        const binary = atob(modelJson.encodedWeights);
                        const bytes = new Uint8Array(binary.length);
                        for (let i = 0; i < binary.length; i++) {
                            bytes[i] = binary.charCodeAt(i);
                        }
                        zip.file('weights.bin', bytes.buffer);
                    }
                    
                    // 生成ZIP文件
                    const zipBlob = await zip.generateAsync({ type: 'blob' });
                    return new File([zipBlob], `sound-model-${modelNumber}.zip`, { type: 'application/zip' });
                } catch (error) {
                    console.error('从JSON数据创建ZIP文件失败:', error);
                    return null;
                }
            }
            
            // 如果内存中有数据，尝试使用内存中的数据
            if (this.modelDataCache[modelNumber]) {
                try {
                    // 创建一个临时的ZIP文件
                    const zip = new JSZip();
                    
                    // 添加model.json
                    const modelData = typeof this.modelDataCache[modelNumber] === 'string' ? 
                                      JSON.parse(this.modelDataCache[modelNumber]) : 
                                      this.modelDataCache[modelNumber];
                    
                    zip.file('model.json', JSON.stringify(modelData));
                    
                    // 如果有权重数据，添加weights.bin
                    if (modelData.encodedWeights) {
                        const binary = atob(modelData.encodedWeights);
                        const bytes = new Uint8Array(binary.length);
                        for (let i = 0; i < binary.length; i++) {
                            bytes[i] = binary.charCodeAt(i);
                        }
                        zip.file('weights.bin', bytes.buffer);
                    }
                    
                    // 生成ZIP文件
                    const zipBlob = await zip.generateAsync({ type: 'blob' });
                    return new File([zipBlob], `sound-model-${modelNumber}.zip`, { type: 'application/zip' });
                } catch (error) {
                    console.error('从内存缓存创建ZIP文件失败:', error);
                }
            }
            
            return null;
        } catch (error) {
            console.error('从缓存获取模型文件失败:', error);
            return null;
        }
    }

    /**
     * 根据模型编号加载已缓存的模型
     * @param {object} args 包含模型编号的参数对象
     * @returns {string} 操作状态
     */
    async loadModelByNumber(args) {
        try {
            const modelNumber = Cast.toString(args.MODEL_NUMBER);
            
            if (!this.modelCache[modelNumber]) {
                notification.show(`找不到编号为 ${modelNumber} 的模型`, 'error');
                return ;
            }

            const cachedModel = this.modelCache[modelNumber];
            
            // 如果当前已经加载了这个编号的模型，避免重复加载
            if (this.currentModel === modelNumber && this.currentModelTF) {
                notification.show(`模型"${cachedModel.modelName}"已经加载`, 'info');
                return ;
            }
            
            // 重要：确保在加载新模型前停止所有正在进行的麦克风预览和录音操作
            if (this.isRecording) {
                this.stopRecording();
                // 确保完全停止录音后再继续
                await new Promise(resolve => setTimeout(resolve, 300));
            }
            
            // 如果recognizer正在监听，确保停止它
            if (this.recognizer && this.recognizer.isListening()) {
                try {
                    await this.recognizer.stopListening();
                    // 短暂延迟以确保完全释放资源
                    await new Promise(resolve => setTimeout(resolve, 300));
                } catch (stopError) {
                    console.warn('停止语音识别时出错，继续执行:', stopError);
                }
            }
            
            // 显示加载中提示
            notification.show(`正在加载模型"${cachedModel.modelName}"...`, 'info');
            
            try {
                // 尝试从IndexedDB加载模型
                const tf = require('@tensorflow/tfjs');
                
                // 创建一个新的scope，确保旧资源可以被释放
                tf.engine().startScope();
                
                try {
                    // 先尝试从IndexedDB加载模型
                    const model = await tf.loadLayersModel(`indexeddb://sound-model-${modelNumber}`);
                    
                    if (!model) {
                        throw new Error('无法从IndexedDB加载模型');
                    }
                    
                    // 从localStorage获取模型元数据
                    const modelDataJson = localStorage.getItem(`logicleap_sound_model_data_${modelNumber}`);
                    if (!modelDataJson) {
                        throw new Error('无法获取模型元数据');
                    }
                    
                    const modelData = JSON.parse(modelDataJson);
                    
                    // 确保元数据有效
                    const metadataClone = {
                        classes: [...(modelData.metadata?.classes || 
                                 cachedModel.labels || 
                                 ['类别1', '类别2', '类别3'])],
                        numFrames: modelData.metadata?.numFrames || 3
                    };
                    
                    // 使用安全模型切换函数
                    const success = this.safeModelSwitch(model, metadataClone, modelNumber);
                    
                    if (success) {
                        // 创建日志消息
                        console.log(`已从IndexedDB加载模型"${cachedModel.modelName}"`);
                        
                        // 触发模型缓存更新事件
                        this.dispatchModelCacheUpdatedEvent();
                        
                        // 结束scope
                        tf.engine().endScope();
                        
                        // 显示成功消息
                        notification.show(`已加载模型"${cachedModel.modelName}"`, 'success');
                        return ;
                    } else {
                        throw new Error('模型切换失败');
                    }
                } catch (indexedDBError) {
                    // 结束scope
                    tf.engine().endScope();
                    
                    console.error('从IndexedDB加载模型失败:', indexedDBError);
                    
                    // 尝试从文件系统加载模型
                    const modelFile = await this.getModelFileFromCache(modelNumber);
                    if (!modelFile) {
                        notification.show(`模型"${cachedModel.modelName}"文件不存在，请重新上传`, 'warning');
                        return ;
                    }
                    
                    // 检查是否是已处理的模型对象(从IndexedDB加载的训练模型)
                    if (modelFile.__alreadyProcessed && modelFile.modelData) {
                        const success = this.safeModelSwitch(
                            modelFile.modelData.model,
                            modelFile.modelData.metadata,
                            modelNumber
                        );
                        
                        if (success) {
                            // 触发模型缓存更新事件
                            this.dispatchModelCacheUpdatedEvent();
                            notification.show(`已加载模型"${cachedModel.modelName}"`, 'success');
                            return ;
                        } else {
                            throw new Error('模型切换失败');
                        }
                    }
                    
                    // 使用常规方法加载模型
                    const { model, metadata } = await loadModel(modelFile);
                    
                    try {
                        // 克隆元数据以避免引用问题
                        const metadataClone = {
                            classes: [...(metadata?.userMetadata?.classes || 
                                    cachedModel.labels || 
                                    ['类别1', '类别2', '类别3'])],
                            numFrames: metadata?.userMetadata?.numFrames || 3
                        };
                        
                        // 使用安全模型切换函数
                        const success = this.safeModelSwitch(model, metadataClone, modelNumber);
                        
                        if (success) {
                            // 触发模型缓存更新事件
                            this.dispatchModelCacheUpdatedEvent();
                            notification.show(`已加载模型"${cachedModel.modelName}"`, 'success');
                            return ;
                        } else {
                            throw new Error('模型切换失败');
                        }
                    } catch (setupError) {
                        console.error('设置模型时出错:', setupError);
                        // 尝试清理新加载的模型以避免内存泄漏
                        if (model) {
                            try {
                                this.safeDisposeModel(model);
                            } catch (e) {
                                console.warn('清理新模型时出错:', e);
                            }
                        }
                        throw new Error('设置模型时出错: ' + setupError.message);
                    }
                }
            } catch (error) {
                console.error('加载模型失败:', error);
                notification.show('加载模型失败: ' + error.message, 'error');
                return ;
            }
        } catch (error) {
            console.error('加载缓存模型失败:', error);
            notification.show('加载缓存模型失败: ' + error.message, 'error');
            return ;
        }
    }

    /**
     * 安全地释放模型资源
     * @private
     * @param {tf.LayersModel} model - 要释放的TensorFlow模型
     */
    safeDisposeModel(model) {
        if (!model) return;
        
        try {
            // 检查模型是否已经被释放
            if (model.disposed === true) {
                console.warn('模型已经被释放，跳过');
                return;
            }
            
            // 使用更安全的方式去释放模型
            const tf = require('@tensorflow/tfjs');
            
            // 创建一个新的scope来保护当前操作
            tf.engine().startScope();
            
            try {
                // 检查模型是否有dispose方法
                if (typeof model.dispose === 'function') {
                    model.dispose();
                }
            } catch (e) {
                console.warn('释放模型时出错:', e);
            } finally {
                // 无论如何都要结束scope
                tf.engine().endScope();
            }
            
            // 最后确保所有不再使用的张量被释放
            tf.tidy(() => {});  // 使用tidy来清理未使用的张量
        } catch (e) {
            console.warn('释放模型资源时出错:', e);
        }
    }

    /**
     * 获取当前模型编号
     * @returns {string} 当前模型编号
     */
    getModelNumber() {
        // 返回当前加载的模型编号，如果没有则返回0
        
        if(this.currentModel === null || this.currentModel === undefined){
            notification.show('未加载模型', 'error');
            return '';
        }
        return this.currentModel;
    }

    /**
     * 清理模型缓存，移除最旧的模型，只保留最新的5个
     */
    cleanupModelCache() {
        try {
            // 从 localStorage 读取最新的缓存索引
            const cacheIndex = localStorage.getItem('logicleap_sound_model_cache_index');
            if (!cacheIndex) {
                return;
            }
            
            // 解析缓存索引
            const modelCache = JSON.parse(cacheIndex);
            
            // 将缓存转换为数组并按照导入时间排序（从旧到新）
            const cacheEntries = Object.entries(modelCache)
                .sort((a, b) => new Date(a[1].importTime) - new Date(b[1].importTime));
            
            // 如果缓存条目数量超过5个，移除最旧的条目，只保留最新的5个
            while (cacheEntries.length > 5) {
                const [oldestKey] = cacheEntries.shift();
                // 从模型缓存中移除
                delete modelCache[oldestKey];
                // 从本地存储中移除模型数据
                localStorage.removeItem(`logicleap_sound_model_zip_${oldestKey}`);
                localStorage.removeItem(`logicleap_sound_model_data_${oldestKey}`);
            }
            
            // 更新本地存储中的缓存索引
            localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(modelCache));
            
            // 同步更新内存中的缓存
            this.modelCache = modelCache;
        } catch (error) {
            console.error('清理模型缓存失败:', error);
            
            // 如果清理过程出错，尝试移除全部缓存
            try {
                // 获取所有 localStorage 中的键
                const keys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.startsWith('logicleap_sound_model_data_') || 
                               key.startsWith('logicleap_sound_model_zip_'))) {
                        keys.push(key);
                    }
                }
                
                // 移除所有模型数据
                keys.forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // 移除缓存索引
                localStorage.removeItem('logicleap_sound_model_cache_index');
                
                // 重置内存中的缓存
                this.modelCache = {};
                this.modelDataCache = {};
            } catch (err) {
                console.error('清空模型缓存失败:', err);
            }
        }
    }

    /**
     * 列出所有缓存的模型
     * @returns {string} 缓存的模型编号列表
     */
    listCachedModels() {
        const modelNumbers = Object.keys(this.modelCache);
        if (modelNumbers.length === 0) {
            return ;
        }
        
        const modelList = modelNumbers.map(num => {
            const model = this.modelCache[num];
            const importDate = new Date(model.importTime);
            const dateStr = `${importDate.getFullYear()}-${importDate.getMonth()+1}-${importDate.getDate()}`;
            const status = localStorage.getItem(`logicleap_sound_model_zip_${num}`) || 
                          localStorage.getItem(`logicleap_sound_model_data_${num}`) ? 
                          '可用' : '需重新上传';
            return `${num}: ${model.modelName} (${dateStr}) [${status}]`;
        }).join('\n');
        
        return modelList;
    }

    /**
     * 使用麦克风预测声音
     * @param {object} args - 积木参数
     * @returns {Promise<string>} 预测结果
     */
    async predictSound(args) {
        try {
            // 检查模型是否已导入 - 同时检查全局模型和实例模型
            const modelLoaded = (window.LogicLeapSoundModel && window.LogicLeapSoundModel.model) || 
                               (this.modelImported && this.currentModelTF);
            
            if (!modelLoaded) {
                // notification.show('请先导入或训练声音模型', 'error');
                return '';
            }

            // 获取用户选择的属性
            const property = args.PROPERTY || 'label';

            // 初始化TensorFlow.js
            const tf = require('@tensorflow/tfjs');
            
            // 如果此前有模型层错误，先尝试重置状态
            if (this._hadModelLayerError) {
                try {
                    this.stopRecording();
                    this.recognizer = null;
                    this._hadModelLayerError = false;
                    await new Promise(resolve => setTimeout(resolve, 300));
                } catch (e) {
                    console.warn('重置模型状态时出错:', e);
                }
            }
            
            // 验证模型状态是否正常
            let modelValid = false;
            try {
                // 获取模型引用
                let model = null;
                
                // 检查全局模型是否可用
                if (window.LogicLeapSoundModel && window.LogicLeapSoundModel.model) {
                    model = window.LogicLeapSoundModel.model;
                    
                    // 尝试检查模型是否有效，确保未被释放
                    if (model.disposed === true) {
                        throw new Error('模型已被释放');
                    }
                    
                    // 检查layers属性是否存在
                    if (!model.layers || model.layers.length === 0) {
                        throw new Error('模型层结构不完整');
                    }
                    
                    // 检查第一层是否有效
                    const firstLayer = model.layers[0];
                    if (firstLayer.disposed === true) {
                        throw new Error('模型层已被释放');
                    }
                    
                    modelValid = true;
                }
                // 如果全局模型不可用，使用实例模型
                else if (this.modelImported && this.currentModelTF) {
                    model = this.currentModelTF;
                    
                    // 与上面相同的检查
                    if (model.disposed === true) {
                        throw new Error('模型已被释放');
                    }
                    
                    if (!model.layers || model.layers.length === 0) {
                        throw new Error('模型层结构不完整');
                    }
                    
                    const firstLayer = model.layers[0];
                    if (firstLayer.disposed === true) {
                        throw new Error('模型层已被释放');
                    }
                    
                    modelValid = true;
                }
                
                if (!modelValid) {
                    throw new Error('模型状态无效');
                }
            } catch (modelCheckError) {
                console.error('模型状态检查失败:', modelCheckError);
                this._hadModelLayerError = true;
                
                // 重置状态
                if (this.isRecording) {
                    this.stopRecording();
                }
                
                // 如果是模型层已释放，建议重新加载模型
                if (modelCheckError.message && (
                    modelCheckError.message.includes('已被释放') || 
                    modelCheckError.message.includes('disposed'))
                ) {
                    notification.show('模型层已被释放，请重新加载模型', 'error');
                    return ;
                }
                
                notification.show('模型状态异常，请重新加载模型', 'error');
                return;
            }

            // 确保模型已加载
            if (!this.recognizer) {
                try {
                    // 尝试加载语音命令模型
                    const speechCommands = await loadSpeechCommands();
                    if (!speechCommands) {
                        notification.show('语音命令模型加载失败，请刷新页面重试', 'error');
                        return ;
                    }
                    
                    try {
                        const modelURL = `${window.location.origin}/static/utils/sound_train/model.json`;
                        const metadataURL = `${window.location.origin}/static/utils/sound_train/metadata.json`;
                        this.recognizer = await speechCommands.create(
                            'BROWSER_FFT',
                            null,
                            modelURL,
                            metadataURL
                        );
                        await this.recognizer.ensureModelLoaded();
                    } catch (initError) {
                        console.error("初始化语音命令模型失败:", initError);
                        notification.show('无法初始化语音识别器，请确保浏览器支持麦克风', 'error');
                        return;
                    }
                } catch (loadError) {
                    console.error('加载语音命令模型失败:', loadError);
                    notification.show('无法加载语音命令模型: ' + loadError.message, 'error');
                    return ;
                }
            }

            // 如果没有正在录音，开始新的录音
            if (!this.isRecording) {
                try {
                    // 持续预测模式
                    this.isRecording = true;
                    
                    // 创建一个模型引用
                    let model = null;
                    let classLabels = [];
                    
                    // 检查全局模型是否可用
                    if (window.LogicLeapSoundModel && window.LogicLeapSoundModel.model) {
                        model = window.LogicLeapSoundModel.model;
                        classLabels = window.LogicLeapSoundModel.metadata.classes || [];
                    }
                    // 如果全局模型不可用，使用实例模型
                    else if (this.modelImported && this.currentModelTF) {
                        model = this.currentModelTF;
                        classLabels = this.currentModelMetadata.classes || [];
                    }
                    
                    if (!model) {
                        throw new Error('模型不可用，请重新加载模型');
                    }
                    
                    // 开始持续监听
                    this.recognizer.listen(({spectrogram: {frameSize, data}}) => {
                        try {
                            // 检查模型是否仍然有效
                            if (model.disposed === true) {
                                console.error('检测到模型已被释放，停止监听');
                                this.stopRecording();
                                this._hadModelLayerError = true;
                                return;
                            }
                            
                            // 准备数据
                            const NUM_FRAMES = 3;
                            const vals = this.normalize(data.subarray(-frameSize * NUM_FRAMES));
                            
                            // 转换为张量并进行预测
                            const input = tf.tensor(vals).reshape([1, NUM_FRAMES, 232, 1]);
                            
                            // 使用tidy确保预测过程中创建的中间张量被正确释放
                            const result = tf.tidy(() => {
                                // 进行预测，并捕获可能的错误
                                try {
                                    const probs = model.predict(input);
                                    const classIndex = probs.argMax(-1).dataSync()[0];
                                    const probValues = probs.dataSync();
                                    const className = classLabels[classIndex] || `类别 ${classIndex + 1}`;
                                    const confidence = probValues[classIndex] * 100;
                                    
                                    return {
                                        className,
                                        confidence
                                    };
                                } catch (predictError) {
                                    console.error('模型预测出错:', predictError);
                                    if (predictError.message && predictError.message.includes('disposed')) {
                                        this._hadModelLayerError = true;
                                        throw new Error('模型层已被释放，请重新加载模型');
                                    }
                                    throw predictError;
                                }
                            });
                            
                            // 清理输入张量
                            input.dispose();
                            
                            // 保存预测结果
                            this.lastPrediction = {
                                label: result.className,
                                confidence: result.confidence,
                                timestamp: Date.now() // 添加时间戳，用于追踪最新结果
                            };
                            
                        } catch (error) {
                            console.error('处理音频数据时出错:', error);
                            
                            // 如果是模型层错误，停止监听
                            if (error.message && (
                                error.message.includes('disposed') || 
                                error.message.includes('释放') ||
                                error.message.includes('Layer')
                            )) {
                                this.stopRecording();
                                this._hadModelLayerError = true;
                            }
                        }
                    }, {
                        overlapFactor: 0.999,
                        includeSpectrogram: true,
                        probabilityThreshold: 0.75,
                        invokeCallbackOnNoiseAndUnknown: true
                    });
                } catch (error) {
                    console.error('预测声音失败:', error);
                    this.isRecording = false;
                    this._hadModelLayerError = true;
                    
                    // 对"Cannot stop streaming"错误进行特殊处理
                    if (error.message && error.message.includes('Cannot stop streaming')) {
                        console.warn('检测到流已停止错误，已自动处理');
                        notification.show('语音识别已重置', 'info');
                    } else {
                        notification.show('预测声音失败: ' + error.message, 'error');
                    }
                    notification.show('预测失败', 'error');
                    return;
                }
            }
            
            // 如果有模型层错误，返回错误信息
            if (this._hadModelLayerError) {
                notification.show('模型异常，请重新加载模型', 'error');
                return;
            }
            
            // 无论是否刚开始录音，都返回最新的预测结果
            if (this.lastPrediction) {
                switch (property) {
                    case 'label':
                        return this.lastPrediction.label;
                    case 'confidence':
                        return `${this.lastPrediction.confidence.toFixed(1)}%`;
                    case 'both':
                        return `${this.lastPrediction.label} (${this.lastPrediction.confidence.toFixed(1)}%)`;
                    default:
                        return this.lastPrediction.label;
                }
            }
            return ;
        } catch (error) {
            console.error('预测声音失败:', error);
            // 标记模型异常
            this._hadModelLayerError = true;
            notification.show( error.message, 'error');
            return;
        }
    }

    /**
     * 预测音频数据
     * @param {object} args - 积木参数
     * @returns {Promise<string>} 预测结果
     */
    async predictAudioData(args) {
        try {
            let audioData = args.AUDIO_PATH;
            
            // 新增：处理Uint8Array类型的音频数据
            if (audioData instanceof Uint8Array) {
                console.log('检测到Uint8Array格式的音频数据，长度:', audioData.length);
                try {
                    // 导入mediaHandler
                    const mediaHandler = require('../utils/mediaHandler');
                    
                    // 将Uint8Array转换为适合处理的对象格式
                    const audioObject = await mediaHandler.convertUint8ArrayToAudioObject(audioData, {
                        sampleRate: 16000, // 假设采样率为16kHz
                        numChannels: 1,    // 假设单声道
                        bitsPerSample: 16, // 假设16位
                        format: 'mp3'      // 使用WAV格式
                    });
                    
                    // 更新audioData为转换后的对象
                    audioData = audioObject;
                    console.log('Uint8Array已转换为音频对象:', audioData);
                } catch (error) {
                    console.error('Uint8Array转换失败:', error);
                    throw new Error('音频数据转换失败: ' + error.message);
                }
            }
            
            // 处理对象格式的音频数据（新格式）
            if (typeof audioData === 'object' && audioData !== null) {
                // 新格式：包含base64Data和format字段
                if (audioData.base64Data && typeof audioData.base64Data === 'string') {
                    // 检查是否是data:开头的完整URL，如果不是则添加前缀
                    if (!audioData.base64Data.startsWith('data:')) {
                        const format = audioData.format || 'mp3';
                        audioData = `data:audio/${format};base64,${audioData.base64Data}`;
                    } else {
                        // 已经是完整URL格式，直接使用
                        audioData = audioData.base64Data;
                    }
                } 
                // 旧格式：可能包含wav或pcm字段
                else {
                    audioData = audioData.wav || audioData.pcm || audioData.mp3 || audioData;
                }
            } else {
                // 如果是字符串或其他类型，转换为字符串
                audioData = Cast.toString(audioData);
            }

            const result = await predictAudioData(audioData, args.PROPERTY, this.NUM_FRAMES);
            return result;
        } catch (error) {
            console.error('预测音频数据失败:', error);
            notification.show(error.message, 'error');
            return;
        }
    }

    /**
     * 展平张量
     * @private
     * @param {Array} tensors - 张量数组
     * @returns {Float32Array} 展平后的数据
     */
    flatten(tensors) {
        const size = tensors[0].length;
        const result = new Float32Array(tensors.length * size);
        tensors.forEach((arr, i) => result.set(arr, i * size));
        return result;
    }

    /**
     * 数据归一化函数
     * @private
     * @param {Float32Array} x - 输入数据
     * @returns {number[]} 归一化后的数据
     */
    normalize(x) {
        const mean = -100;
        const std = 10;
        return Array.from(x).map(val => (val - mean) / std);
    }

    /**
     * 停止录音并清理资源
     * @private
     */
    stopRecording() {
        if (this.recognizer) {
            try {
                // 首先检查recognizer是否正在监听
                if (this.recognizer.isListening()) {
                    this.recognizer.stopListening();
                }
            } catch (e) {
                // 捕获错误但不显示给用户，只在控制台记录
                console.warn('停止监听时出错(已自动处理):', e);
                // 可以在这里添加温和的提示，但不显示错误内容
                try {
                    notification.show('语音识别已重置', 'info');
                } catch (notifyError) {
                    // 忽略通知错误
                }
            }
        }
        this.isRecording = false;
    }

    // 触发模型缓存更新事件
    dispatchModelCacheUpdatedEvent() {
        try {
            const event = new CustomEvent('logicleap_sound_model_cache_updated', {
                detail: {
                    modelCache: this.modelCache,
                    modelsCount: Object.keys(this.modelCache).length
                }
            });
            window.dispatchEvent(event);
        } catch (error) {
            console.error('触发模型缓存更新事件失败:', error);
        }
    }

    /**
     * 初始化模型数据
     * 从后端获取当前作品的所有声音模型数据
     */
    async initializeModelData() {
        try {
            const workId = await this.getWorkId();
            
            if (!workId || workId === 'null') {
                console.warn('未能获取作品ID，跳过模型数据初始化');
                return;
            }
            const token = localStorage.getItem('token');
            
            // 使用 workApi.getWorkModels 获取模型
            const result = await workApi.getWorkModels(workId);

            if (result && result.code === 200 && result.data) {
                // 只获取声音类型的模型
                // 确保过滤类型正确，使用 'audio_classifier' 或 'sound'
                const soundModels = result.data.filter(model => 
                    model.modelType === 'sound' || model.modelType === 'audio_classifier'
                );
                // 初始化模型数据
                this.modelData = soundModels;
                // 设置初始modelCount为已有模型的最大编号
                const existingModelNumbers = soundModels.map(m => parseInt(m.modelNumber) || 0);
                this.modelCount = existingModelNumbers.length > 0 ? Math.max(...existingModelNumbers) : 0;

                // 如果有模型数据，开始下载和处理每个模型
                if (this.modelCount > 0) {
                    
                    // 清空现有的缓存
                    this.modelCache = {};
                    this.modelDataCache = {};
                    localStorage.removeItem('logicleap_sound_model_cache_index');
                    
                    // 处理每个模型
                    for (const model of this.modelData) {
                        try {
                            // 从URL下载ZIP文件
                            const modelResponse = await fetch(model.modelUrl);
                            if (!modelResponse.ok) {
                                throw new Error(`下载模型失败: ${modelResponse.statusText}`);
                            }
                            
                            // 获取ZIP数据
                            const modelBlob = await modelResponse.blob();
                            const modelFile = new File([modelBlob], `sound-model-${model.modelNumber}.zip`, { type: 'application/zip' });
                            
                            try {
                                // 使用loadModel函数加载和验证模型
                                const { model: loadedModel, metadata } = await loadModel(modelFile);
                                
                                // 获取类别标签
                                const labels = metadata?.userMetadata?.classes || 
                                             metadata?.labels || 
                                             ['类别1', '类别2', '类别3'];
                                
                                // 更新模型缓存
                                this.modelCache[model.modelNumber] = {
                                    modelName: model.description || `声音模型${model.modelNumber}`,
                                    importTime: new Date().toISOString(),
                                    labels: labels
                                };
                                
                                // 将文件转换为Base64存储
                                const reader = new FileReader();
                                const base64Data = await new Promise((resolve, reject) => {
                                    reader.onload = () => resolve(reader.result.split(',')[1]);
                                    reader.onerror = reject;
                                    reader.readAsDataURL(modelFile);
                                });
                                
                                // 保存到localStorage
                                try {
                                    localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                                    localStorage.setItem(`logicleap_sound_model_zip_${model.modelNumber}`, base64Data);
                                } catch (storageError) {
                                    console.error('保存模型到localStorage失败:', storageError);
                                    // 尝试清理缓存后重新保存
                                    this.cleanupModelCache();
                                    localStorage.setItem('logicleap_sound_model_cache_index', JSON.stringify(this.modelCache));
                                    localStorage.setItem(`logicleap_sound_model_zip_${model.modelNumber}`, base64Data);
                                }
                                
                                // 释放模型内存
                                if (loadedModel) {
                                    try {
                                        loadedModel.dispose();
                                    } catch (e) {
                                        console.warn('释放模型内存时出错:', e);
                                    }
                                }
                                
                            } catch (jsonError) {
                                console.error(`模型 ${model.modelNumber} 验证失败:`, jsonError);
                                continue;
                            }
                            
                        } catch (modelError) {
                            console.error(`处理模型 ${model.modelNumber} 失败:`, modelError);
                            continue;
                        }
                    }
                    
                    // 触发模型缓存更新事件
                    this.dispatchModelCacheUpdatedEvent();
                    
                    // 如果有模型，默认加载第一个
                    if (Object.keys(this.modelCache).length > 0) {
                        const firstModelNumber = Object.keys(this.modelCache)[0];
                        try {
                            await this.loadModelByNumber({ MODEL_NUMBER: firstModelNumber });
                        } catch (error) {
                            console.error('加载第一个模型失败:', error);
                        }
                    }
                }
            } else {
                console.warn('获取模型数据失败:', result ? result.message : '获取模型数据失败');
            }
        } catch (error) {
            console.error('初始化模型数据时发生错误:', error);
            notification.show('加载模型数据失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取当前作品ID
     */
    async getWorkId() {
        try {
            // 从localStorage获取workId
            const workId = localStorage.getItem('modelWorkId');
            if (workId) {
                return workId;
            }else{
                localStorage.removeItem('modelWorkId');
            }

            // 如果localStorage中没有,尝试从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('workId');
        } catch (error) {
            console.error('获取作品ID失败:', error);
            return null;
        }
    }

    /**
     * 安全地从一个模型切换到另一个模型
     * @private
     * @param {Object} newModel - 新的模型对象
     * @param {Object} newMetadata - 新的模型元数据
     * @param {string} modelNumber - 模型编号
     * @returns {boolean} - 切换是否成功
     */
    safeModelSwitch(newModel, newMetadata, modelNumber) {
        try {
            // 0. 先确保新模型有效
            if (!newModel) {
                console.error('新模型无效');
                return false;
            }
            
            // 要处理的新模型元数据
            const processedMetadata = {
                classes: Array.isArray(newMetadata?.classes) ? [...newMetadata.classes] : ['类别1', '类别2'],
                numFrames: newMetadata?.numFrames || 3
            };
            
            // 1. 设置全局变量和实例变量，这样后面就不用再引用newModel
            const newGlobalModel = {
                model: newModel,
                metadata: processedMetadata
            };
            
            // 2. 保存旧模型的引用，以便后续清理
            const oldGlobalModel = window.LogicLeapSoundModel?.model;
            const oldInstanceModel = this.currentModelTF;
            
            // 3. 先设置新的实例变量和全局变量
            this.currentModelTF = newModel;
            this.currentModel = modelNumber;
            this.modelImported = true;
            this.currentModelMetadata = processedMetadata;
            window.LogicLeapSoundModel = newGlobalModel;
            
            // 4. 在设置完新模型后，再释放旧模型
            try {
                // 清理全局模型，但确保不是新模型
                if (oldGlobalModel && oldGlobalModel !== newModel) {
                    this.safeDisposeModel(oldGlobalModel);
                }
                
                // 清理实例模型，但确保不是新模型
                if (oldInstanceModel && oldInstanceModel !== newModel) {
                    this.safeDisposeModel(oldInstanceModel);
                }
            } catch (e) {
                console.warn('清理旧模型时出错，但不影响新模型使用:', e);
                // 继续执行，不影响返回结果
            }
            
            return true;
        } catch (error) {
            console.error('安全切换模型时出错:', error);
            return false;
        }
    }
}

// eslint-disable-next-line import/no-commonjs
module.exports = function (runtime) {
    return new LogicLeapSoundTrainBlocks(runtime);
};
