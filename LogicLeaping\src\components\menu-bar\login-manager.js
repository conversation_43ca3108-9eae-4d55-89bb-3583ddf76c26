import { message, Modal } from 'antd';
// 更新导入路径为新的 TypeScript 服务
import userService from '@api/user-service.ts'; 
// 移除 API_URL 导入, commApi 似乎也与用户认证相关，暂时保留，稍后确认是否也需要整合或移除
// import { API_URL, API_HOST } from '../../config/config.js';
import commApi from '@api/comm_api.ts';
// 导入网络检测工具
import { getNetworkChecker, LogLevel } from '@utils/NetworkChecker.js';

// 删除旧的NetworkErrorNotification对象，使用新的NetworkChecker替代

class LoginManager {
    constructor(menuBar) {
        this.menuBar = menuBar;
        this.state = menuBar.state;
        this.setState = menuBar.setState.bind(menuBar);
        this.userService = userService;
        this._loginExpiredModalShown = false;
        this._checkingStatus = false; // 防抖标志
        
        // 初始化网络检测器 - 支持详细的日志输出
        this.networkChecker = getNetworkChecker({
            pingUrl: '/api/web/user/info/ping', // 使用心跳检测接口
            pingInterval: 30000, // 30秒检测一次
            maxConsecutiveFailures: 2, // 连续2次失败才判断为离线
            logLevel: LogLevel.DEBUG // 设置日志级别为DEBUG，获取更详细的日志
        });
        
        console.log('登录管理器: 已初始化网络检测器');
        
        // 绑定方法
        this.handleLogin = this.handleLogin.bind(this);
        this.handleLoginModalCancel = this.handleLoginModalCancel.bind(this);
        this.handleLogout = this.handleLogout.bind(this);
        this.handleLogoutConfirm = this.handleLogoutConfirm.bind(this);
        this.handleLogoutCancel = this.handleLogoutCancel.bind(this);
        this.handleLoginSubmit = this.handleLoginSubmit.bind(this);
        this.handleLogoutDetected = this.handleLogoutDetected.bind(this);
        this.handleAutoLogout = this.handleAutoLogout.bind(this);
        this.showLoginExpiredModal = this.showLoginExpiredModal.bind(this);
        this.clearLoginState = this.clearLoginState.bind(this);
        this.syncUserLoginState = this.syncUserLoginState.bind(this);
        this.checkLoginStatus = this.checkLoginStatus.bind(this);
        this.handleNetworkError = this.handleNetworkError.bind(this);
        this.handleNetworkRecovery = this.handleNetworkRecovery.bind(this);
        
        // 添加网络状态变化监听
        this.networkChecker.addListener(this.handleNetworkStatusChange.bind(this));
        console.log('登录管理器: 已添加网络状态变化监听');
    }
    
    // 处理网络状态变化
    handleNetworkStatusChange(isOnline) {
        console.log(`登录管理器: 网络状态变化为 ${isOnline ? '在线' : '离线'}`);
        if (isOnline) {
            console.log('登录管理器: 检测到网络恢复，准备执行恢复操作');
            this.handleNetworkRecovery();
        } else {
            console.log('登录管理器: 检测到网络断开，UI提示由NetworkChecker处理');
            // 网络断开不需要特殊处理，NetworkChecker会自动显示提示
        }
    }
    
    // 处理网络恢复
    handleNetworkRecovery() {
        console.log('登录管理器: 网络恢复，正在检查登录状态...');
        // 网络恢复后，检查登录状态
        this.checkLoginStatus().then(status => {
            console.log(`登录管理器: 网络恢复后登录状态检查结果: ${status === true ? '已登录' : status === false ? '未登录' : '检查失败'}`);
        });
    }

    handleLogin() {
        this.setState({ loginModalVisible: true });
    }

    handleLoginModalCancel() {
        this.setState({ 
            loginModalVisible: false,
            loginError: ''
        });
    }

    handleLogout() {
        this.setState({ logoutModalVisible: true });
    }

    handleLogoutConfirm() {
        this.clearLoginState();
        this.setState({ logoutModalVisible: false });
        message.success('退出登录成功');
    }

    handleLogoutCancel() {
        this.setState({ logoutModalVisible: false });
    }

    async handleLoginSubmit(values) {
        try {
            const { type, mode, data } = values;
            let apiResponse; // 将 response 重命名为 apiResponse 以匹配新服务返回的类型
            let tokenFromStorage; 

            if (type === 'normal') {
                if (mode === 'password'){ 
                    apiResponse = await this.userService.login(data.phone, data.password);
                    // 使用 apiResponse.code 和 apiResponse.data
                    if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.token) {
                        localStorage.setItem('token', apiResponse.data.token);
                        if (apiResponse.data.refreshToken) {
                            localStorage.setItem('refreshToken', apiResponse.data.refreshToken);
                        }
                        tokenFromStorage = apiResponse.data.token; 
                    } else {
                        this.setState({
                            loginError: apiResponse.message || '账号或密码错误，请重试'
                        });
                        return; 
                    }
                } else if (mode === 'verify') { 
                    // verifyCodeLogin 现在由 user-service.ts 处理 token 存储
                    // 我们仍然需要调用它，并检查结果
                    apiResponse = await this.userService.verifyCodeLogin(data.phone, data.verifyCode);
                    if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.token) {
                        tokenFromStorage = apiResponse.data.token; // Token 应该已由 service 存储
                         // isNewUser 也由 service 处理
                    } else {
                        this.setState({
                            loginError: apiResponse.message || '验证码登录失败，请重试'
                        });
                        return;
                    }
                } else {
                    console.error('LoginManager: Unknown login mode for normal type:', mode);
                    this.setState({ loginError: '未知的登录模式' });
                    return;
                }
            } else if (type === 'student') { 
                apiResponse = await this.userService.studentLogin(
                    data.province,
                    data.city,
                    data.district,
                    data.schoolName,
                    data.studentNumber,
                    data.password
                );
                if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.token) {
                    localStorage.setItem('token', apiResponse.data.token);
                    if (apiResponse.data.refreshToken) {
                        localStorage.setItem('refreshToken', apiResponse.data.refreshToken);
                    }
                    tokenFromStorage = apiResponse.data.token; 
                } else {
                    this.setState({
                        loginError: apiResponse.message || '学生登录失败，请检查信息后重试'
                    });
                    return; 
                }
            } else {
                console.error('LoginManager: Unknown login type:', type);
                this.setState({ loginError: '未知的登录类型' });
                return;
            }

            if (!tokenFromStorage) {
                console.error('LoginManager: Token is still missing before getUserInfo.');
                this.setState({ loginError: '获取登录凭证失败' });
                return;
            }
            const userId = Number(localStorage.getItem('userId'))

            // getUserInfo 现在不需要 token 参数，它会从 localStorage 读取或由 request.ts 处理
            const userApiResponse = await this.userService.getUserInfo(userId); 
            
            // 使用 userApiResponse.code 和 userApiResponse.data
            if (userApiResponse.code === 200 && userApiResponse.data) {
                const userData = {
                    userId: userApiResponse.data.id,
                    nickName: userApiResponse.data.nickName,
                    avatarUrl: userApiResponse.data.avatarUrl,
                    phone: userApiResponse.data.phone,
                    roleId: userApiResponse.data.roleId
                };

                localStorage.setItem('user', JSON.stringify(userData));
                
                this.setState({
                    isLoggedIn: true,
                    loginModalVisible: false,
                    loginError: null,
                    userId: userData.userId,
                    nickName: userData.nickName,
                    avatarUrl: userData.avatarUrl,
                    phone: userData.phone,
                    roleId: userData.roleId
                });

                message.success('登录成功');
                
                this.menuBar.initWebSocket();
                await this.menuBar.fetchUserPoints();
                this.menuBar.cleanExpiredDrafts(userData.userId);
                await this.menuBar.restoreDraft();
            } else {
                this.setState({
                    loginError: userApiResponse.message || '获取用户信息失败，请重试'
                });
            }
        } catch (error) {
            console.error('登录处理流程失败:', error);
            // error 可能已经是 ApiResponse 结构，也可能是其他网络错误
            const errorMessage = error.message || (error.response && error.response.data && error.response.data.message) || '登录过程中发生错误，请重试';
            this.setState({
                loginError: errorMessage
            });
        }
    }

    handleLogoutDetected(type, message) {
        const title = type === 'expired' ? '登录已过期' : '账号在其他设备登录';
        const content = message || (type === 'expired' ? '您的登录已过期，请重新登录' : '您的账号在其他设备登录，如非本人操作，请及时修改密码');
        this.handleAutoLogout(title, content, type);
    }

    handleAutoLogout(title, content, type) {
        this.setState({
            autoLogoutModalVisible: true,
            autoLogoutModalTitle: title,
            autoLogoutModalContent: content,
            autoLogoutType: type
        });
    }

    showLoginExpiredModal() {
        if (this._loginExpiredModalShown) return;
        
        this._loginExpiredModalShown = true;
        Modal.warning({
            title: '登录已过期',
            content: '您的登录已过期，请重新登录',
            okText: '重新登录',
            onOk: () => {
                this._loginExpiredModalShown = false;
                this.clearLoginState();
                window.location.href = `/home`;
            },
            onCancel: () => {
                this._loginExpiredModalShown = false;
            }
        });
    }

    clearLoginState() {
        // 清除用户信息
        this.setState({
            isLoggedIn: false,
            userId: null,
            nickName: null,
            avatarUrl: null,
            phone: null,
            points: 0,
            availablePoints: 0,
            roleId: null,
            wsConnected: false,
            ws: null,
            userInfo: null,
            loginError: null
        });
        
        // 清除本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('refreshToken');
        
        // 清除 Redux store 中的用户数据
        this.menuBar.props.onSetUser(null);
        
        // 关闭WebSocket连接
        if (this.menuBar.ws) {
            this.menuBar.ws.close();
            this.menuBar.ws = null;
        }

        // 清除重连定时器
        if (this.menuBar._wsReconnectTimer) {
            clearTimeout(this.menuBar._wsReconnectTimer);
            this.menuBar._wsReconnectTimer = null;
        }

        // 清除登录检查定时器
        if (this.menuBar.loginCheckInterval) {
            clearInterval(this.menuBar.loginCheckInterval);
            this.menuBar.loginCheckInterval = null;
        }
        
        // 清理所有草稿
        this.menuBar.cleanAllDrafts();
    }

    // 同步用户登录状态
    async syncUserLoginState() {
        try {
            console.log('登录管理器: 开始同步用户登录状态');
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');

            if (!token || !user) {
                console.log('登录管理器: 本地存储中未找到有效token或用户信息，尝试使用mainToken');
                const mainToken = localStorage.getItem('mainToken');
                if (mainToken) {
                    // 使用 commApi 获取用户信息
                    try {
                        console.log('登录管理器: 尝试使用mainToken获取用户信息');
                        const userInfo = await commApi.getUserInfoByToken(mainToken);
                        if (userInfo) {
                            console.log('登录管理器: 使用mainToken成功获取用户信息');
                            const userData = {
                                userId: userInfo.id,
                                nickName: userInfo.nickName,
                                avatarUrl: userInfo.avatarUrl,
                                phone: userInfo.phone,
                                roleId: userInfo.roleId
                            };

                            localStorage.setItem('token', mainToken);
                            localStorage.setItem('user', JSON.stringify(userData));

                            this.setState({
                                isLoggedIn: true,
                                userId: userData.userId,
                                nickName: userData.nickName,
                                avatarUrl: userData.avatarUrl,
                                phone: userData.phone,
                                roleId: userData.roleId
                            }, () => {
                                console.log('登录管理器: 用户状态已更新，初始化WebSocket和获取积分');
                                this.menuBar.initWebSocket();
                                this.menuBar.fetchUserPoints();
                                if (userData.roleId === 1) {
                                    this.menuBar.fetchStudentAvailablePoints();
                                }
                            });
                            return;
                        } else {
                            console.warn('登录管理器: 使用mainToken获取用户信息失败');
                        }
                    } catch (error) {
                        console.error('登录管理器: 使用mainToken获取用户信息时发生异常:', error);
                        // 如果是网络错误，使用网络检测器处理
                        if (error.message === 'NetworkError') {
                            console.log('登录管理器: 检测到网络错误，启动网络错误处理');
                            this.handleNetworkError();
                        }
                    }
                }

                // 如果没有有效的 token 或获取失败，清除状态
                console.log('登录管理器: 无法恢复登录状态，清除登录信息');
                this.clearLoginState();
                return;
            }

            // 如果有本地用户信息，直接使用
            try {
                console.log('登录管理器: 从本地存储恢复用户信息');
                const userData = JSON.parse(user);
                this.setState({
                    isLoggedIn: true,
                    userId: userData.userId,
                    nickName: userData.nickName,
                    avatarUrl: userData.avatarUrl,
                    phone: userData.phone,
                    roleId: userData.roleId
                }, () => {
                    // 初始化其他功能
                    console.log('登录管理器: 用户状态已恢复，初始化WebSocket和获取积分');
                    this.menuBar.initWebSocket();
                    this.menuBar.fetchUserPoints();
                    if (userData.roleId === 1) {
                        this.menuBar.fetchStudentAvailablePoints();
                    }
                });

                // 启动自动保存定时器
                console.log('登录管理器: 启动草稿自动保存定时器');
                this.menuBar.autoSaveTimer = setInterval(this.menuBar.autoSaveDraft, this.menuBar.AUTO_SAVE_INTERVAL);

                // 启动登录状态检查
                console.log('登录管理器: 启动登录状态定期检查 (3分钟间隔)');
                this.menuBar.loginCheckInterval = setInterval(this.checkLoginStatus, 180000); // 3分钟检查一次

            } catch (error) {
                console.error('登录管理器: 解析用户数据失败:', error);
                this.clearLoginState();
            }
        } catch (error) {
            console.error('登录管理器: 同步用户登录状态失败:', error);
            this.clearLoginState();
        }
    }

    // 检查登录状态
    async checkLoginStatus() {
        if (this._loginExpiredModalShown || this._checkingStatus) return;
        this._checkingStatus = true;

        try {
            // 先检查网络状态
            const networkStatus = this.networkChecker.getStatus();
            console.log(`登录管理器: 检查登录状态，当前网络状态: ${networkStatus ? '在线' : '离线'}`);
            
            if (!networkStatus) {
                console.log('登录管理器: 网络离线，跳过登录状态检查');
                this._checkingStatus = false;
                return undefined; // 网络已断开，跳过检查
            }
            
            const token = localStorage.getItem('token');
            if (!token) {
                console.log('登录管理器: 未找到token，显示登录过期提示');
                this._checkingStatus = false;
                this.showLoginExpiredModal();
                return false;
            }

            // 使用 commApi 检查登录状态
            console.log('登录管理器: 发送API请求验证token有效性');
            try {
                const userInfo = await commApi.getUserInfoByToken(token);
                console.log('登录管理器: 用户信息', userInfo);
                this._checkingStatus = false;

                if (userInfo && userInfo.id) {
                    console.log('登录管理器: token验证成功，用户仍处于登录状态');
                    // 如果已显示网络错误提示，则隐藏
                    this.networkChecker.hideErrorUI();
                    return true;
                } else {
                    console.log('登录管理器: token无效，显示登录过期提示');
                    // Token 无效 (commApi 返回 null)
                    return false;
                }
            } catch (error) {
                this._checkingStatus = false;
                // 捕获 commApi 抛出的错误 (例如 NetworkError)
                if (error.message === 'NetworkError') {
                    console.log('登录管理器: API请求过程中检测到网络错误');
                    this.handleNetworkError();
                    return undefined; // 表示网络错误
                } else {
                    // 其他未知错误
                    console.error('登录管理器: 检查登录状态时发生未知异常:', error);
                    // 防止非网络错误导致的误判
                    if (error.status === 401 || error.status === 403) {
                        this.showLoginExpiredModal(); // 只有身份验证错误才显示登录过期
                    }
                    return false;
                }
            }

        } catch (error) {
            this._checkingStatus = false;
            console.error('登录管理器: 检查登录状态过程中发生意外错误:', error);
            this.handleNetworkError(); // 异常视为网络错误
            return undefined;
        }
    }

    // 处理网络错误
    handleNetworkError() {
        console.log('登录管理器: 处理网络错误，显示网络错误提示');
        // 使用网络检测器显示网络错误提示
        this.networkChecker.showErrorUI();
    }
}

export default LoginManager; 