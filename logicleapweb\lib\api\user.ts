import { UserRole } from '@/types/user';
import request from '../request'
import { Encrypted } from 'logic-common';
import { API_URL } from '../../config/config';

export interface UserInfo {
  id: number;
  gender: number;
  phone: string;
  nickName: string;
  avatarUrl: string;
  introduction: string;
  createTime: string;
  roleId?: number;
  role: UserRole;
  roles: UserRole[];
}

interface ApiResponse<T> {
  code: number;
  message: string;
  data?: T;
}

// 创建加密服务实例
const encryptionService = Encrypted.create(API_URL);

export const userApi = {


  checkHasBindPhone: async (
    selectedStudents: number[]
  ) => {
    try {
      const response = await request.post(`/api/web/user/info/checkHasBindPhone`, selectedStudents);
      return response.data;
    } catch (error) {
      return {
        code: 500,
        message: '检测是否有绑定手机号失败',
      };
    }
  },


  initPwd: async (data: {
    userId: number, password: string
  }) => {
    try {
      const response = await request.post(`/api/user-auth/setPasswordByUserId`, data);
      return response.data;
    } catch (error) {
      return {
        code: 500,
        message: '获取用户信息失败',
      };
    }

  },

  // 根据ID获取用户信息  okok
  getUserInfo: async (userId: number) => {
    try {
      // 使用加密服务发送请求，不需要手动传递token
      if (!userId || userId === 0 ) {
        console.error('尝试获取用户信息时userId无效:', userId);
        // 可以尝试从其他来源获取userId
        const storedUser = localStorage.getItem('user');
        console.log("zww加密获取userInfo从本地存储拿的userInfo",storedUser);
        
        if (storedUser) {
          try {
            const parsedUser = JSON.parse(storedUser);
            userId = parsedUser.id || parsedUser.userId;
          } catch (e) {
            console.error('解析存储的用户信息失败:', e);
          }
        }
        // 如果本地存储的user不存在，获取id
        else{
          userId = Number(localStorage.getItem('userId'));
        }
      }
      const response = await encryptionService.request.getEncrypted<any>(`/api/web/user/info/${userId}`);
      if (response.success) {
        return {
          code: 200,
          data: response.data
        };
      }
      return {
        code: 500,
        message: response.message || '获取用户信息失败'
      };

    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 更新用户基本信息  okok
  updateProfile: (userId: number, data: {
    id: number,
    nickName?: string
    introduction?: string
    avatarUrl?: string
    phone?: string
  }) => {
    return request.patch(`/api/user-info/${userId}`, data)
  },

  // 更新头像  okok
  updateAvatar: (userId: number, data: {
    id: number,
    avatarUrl?: string
  }) => {
    return request.patch(`/api/user-info/${userId}`, data)
  },
  //查询该手机号下的所有绑定
  findAllBindByPhone: (phone: string) => {
    return request.get('/api/user-auth/findAllBindByPhone', { params: { phone } })
  },


  // okok
  bindPhone: (data: { phone: string, code: string, replaceUserId?: number }) => {
    return request.post('/api/user-auth/bindPhone', data)
  },

  // 换绑后更新数据
  updateBindingAfterRebind: (data: { oldUserId: number, newUserId: number, phone: string }) => {
    return request.post('/api/user-auth/updateBindingAfterRebind', data)
  },

  // 登录页面发送验证码   okok
  sendVerifyCode: async (phone: string) => {
    return request.post('/api/user-auth/smsSend', { phone });
  },
  // 换绑手机号发送验证码   okok
  sendVerifyCodeForUpdatePhone: async (phone: string) => {
    return request.post('/api/user-auth/smsSendUpdatePhone', { phone });
  },
  // 验证验证码   okok
  verifyResponse: async (params: { phone: string; code: string }) => {
    return request.post('/api/user-auth/verify-sms-code', {
      phone: params.phone,
      code: params.code
    });
  },



  // 绑定社交账号  没用到
  bindSocial: (data: {
    type: 'xiaohongshu' | 'douyin' | 'bilibili'
    account: string
  }) => {
    return request.post('/app/user/info/bindSocial', data)
  },

  // 更新密码  okok
  updatePassword: async (oldPassword: string, newPassword: string, id: number) => {
    // const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();
    // 使用加密服务发送PUT请求，不需要手动传递token
    const response = await encryptionService.request.putEncryptedWithBody<any>('/api/web/user/info/update/password',
      {
        oldPassword,
        newPassword,
        id
      },
      {
        useSecureSession: true // 直接使用secureHeaders对象，不需要展开
      }
    );
    if (response.success) {
      return {
        code: 200,
        data: response.data
      };
    }
    return {
      code: 500,
      message: response.message || '更新密码失败'
    };
  },

  // 根据手机号重置所有用户密码
  resetPasswordByPhone: async (oldPassword: string, newPassword: string, phone: string) => {
    // 使用加密服务发送POST请求并加密请求体
    // 创建安全会话
    // const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();
    // console.log('secureHeaders', secureHeaders);
    const response = await encryptionService.request.postEncryptedWithBody<any>('/api/user-auth/reset-password-by-phone',
      {
        oldPassword,
        newPassword,
        phone
      },
      {
        useSecureSession: true // 直接使用secureHeaders对象，不需要展开
      }
    );
    if (response.success) {
      return {
        code: 200,
        data: response
      };
    }
    return {
      code: 500,
      message: response.message || '重置密码失败'
    };
  },

  // 获取用户列表  okok
  getUserList: (params?: {
    roleId?: number;  // 添加 roleId 参数
    keyword?: string;
    page?: number;
    size?: number;
  }) => {
    return request.get('/user-info/condition/search', { params })
  },

  // 变更用户角色   okok
  assignRole: (userId: number, roleId: number) => {
    // 参数验证
    if (!userId || isNaN(userId) || userId <= 0) {
      // 从localStorage里拿
      userId=Number(localStorage.getItem("userId"))
    }
    
    if (!roleId || isNaN(roleId) || roleId <= 0) {
      return Promise.reject({ code: 400, message: '无效的角色ID' });
    }
    
    return request.patch(`/api/user-info/${userId}/role/${roleId}`);
  },



  //  okok 
  addUser: (data: {
    phone: string;
    roleId: number;
    nickName?: string;  // 添加可选的昵称参数
  }) => {
    return request.post('api/user-info', data);
  },

  /**
   * 根据用户ID获取用户信息
   */
  // getByUserId: (userId: number) => {
  //   return request.get<{
  //     code: number;
  //     data: UserInfo;
  //   }>('/api/web/user/info/getUserInfoById', {
  //     params: { userId }
  //   });
  // },

  // 修改手机号  okok
  updatePhone: (newPhone: string, verifyCode: string) => {
    return request.post('/api/user-info/updatePhone', {
      newPhone,
      verifyCode
    });
  },

  // 选择身份
  selectIdentity: async (userId: number, sessionId: string) => {
    try {
      const response = await request.post('/api/user-auth/select-identity', {
        userId,
        sessionId
      });
      return response.data;
    } catch (error) {
      return {
        code: 500,
        message: '账号切换失败',
      };
    }
  },

  // 已登录用户切换到其他用户ID
  switchToUser: async (userId: number) => {
    try {
      // 这里直接复用select-identity接口，但不传入sessionId
      const response = await request.post('/api/user-auth/select-identity', {
        userId
      });
      console.log('用户切换响应:', response);
      return response.data;
    } catch (error) {
      console.error('用户切换失败:', error);
      return {
        code: 500,
        message: '用户切换失败',
      };
    }
  },

  // 教师认证申请
  teacherAuth: async (formData: FormData) => {
    try {
      const response = await request.post('/api/teacher-auth/submit', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('教师认证提交失败:', error);
      return {
        code: 500,
        message: '教师认证提交失败，请稍后重试'
      };
    }
  },

  // 绑定微信到用户
  bindWeixinToUser: async (data: { openid: string, userId: number, scene_str?: string }) => {
    try {
      const response = await request.post('/api/user-auth/bind-weixin-to-user', data);
      return response;
    } catch (error) {
      console.error('微信绑定账号失败:', error);
      return {
        code: 500,
        message: '微信绑定账号失败，请稍后重试'
      };
    }
  },

  // 转移微信openid到指定用户
  transferWeixinOpenid: async (targetUserId: number, verifyCode: string, bindPhone: string) => {
    try {
      const response = await request.post('/api/user-auth/transfer-weixin-openid', {
        targetUserId,
        verifyCode,
        bindPhone
      });
      return response;
    } catch (error) {
      console.error('微信账号转移失败:', error);
      return {
        code: 500,
        message: '微信账号转移失败，请稍后重试'
      };
    }
  },
  
  // 根据用户ID自动登录并获取token
  autoLoginByUserId: async (userId: number, loginToken: string) => {
    try {
      const response = await request.post('/api/user-auth/auto-login-by-userid', {
        userId,
        loginToken
      });
      return response;
    } catch (error) {
      console.error('自动登录失败:', error);
      return {
        code: 500,
        message: '自动登录失败，请稍后重试'
      };
    }
  },
}

export default userApi 