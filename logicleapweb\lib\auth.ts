import request, { hasShownModal } from './request';
import { clearUser, setUser } from './store';
import { store } from './store';
import userApi, { UserInfo } from './api/user';
import { getNetworkDetector } from './utils/NetworkDetector';
import { message, Modal } from 'antd';
// import jwt from 'jsonwebtoken';



// 确保全局单例
const NetworkErrorNotification = {
  modalInstance: null as any,
  isShowing: false,
  notificationElement: null as HTMLElement | null,

  // 显示网络错误提示
  show() {
    // 如果已经在显示，则不执行任何操作
    if (this.isShowing) return;

    // 创建自定义样式的提示，而不是使用Modal
    if (!this.notificationElement) {
      this.notificationElement = document.createElement('div');
      this.notificationElement.className = 'network-error-notification';
      document.body.appendChild(this.notificationElement);

      // 设置样式
      this.notificationElement.style.position = 'fixed';
      this.notificationElement.style.top = '20px';
      this.notificationElement.style.right = '20px';
      this.notificationElement.style.zIndex = '9999';
      this.notificationElement.style.padding = '8px 12px';
      this.notificationElement.style.borderRadius = '4px';
      this.notificationElement.style.backgroundColor = '#ff4d4f';
      this.notificationElement.style.color = 'white';
      this.notificationElement.style.fontSize = '14px';
      this.notificationElement.style.fontWeight = '500';
      this.notificationElement.style.display = 'flex';
      this.notificationElement.style.alignItems = 'center';
      this.notificationElement.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';

      // 创建旋转加载图标的样式
      const style = document.createElement('style');
      style.innerHTML = `
        @keyframes network-error-spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .network-error-spinner {
          display: inline-block;
          width: 16px;
          height: 16px;
          border: 2px solid transparent;
          border-top-color: white;
          border-radius: 50%;
          animation: network-error-spin 1s linear infinite;
        }
      `;
      document.head.appendChild(style);

      // 添加旋转加载图标
      const spinnerSpan = document.createElement('span');
      spinnerSpan.className = 'network-error-spinner';
      spinnerSpan.style.marginRight = '8px';

      // 添加文本
      const textSpan = document.createElement('span');
      textSpan.textContent = '您当前网络连接不稳定，请检查网络设置';

      this.notificationElement.appendChild(spinnerSpan);
      this.notificationElement.appendChild(textSpan);
    }

    // 显示提示
    this.notificationElement.style.display = 'flex';

    this.isShowing = true;
    console.log('网络错误提示已显示');
  },

  // 隐藏网络错误提示
  hide() {
    // 如果没有显示，则不执行任何操作
    if (!this.isShowing) return;

    // 隐藏自定义提示
    if (this.notificationElement) {
      this.notificationElement.style.display = 'none';
    }

    this.isShowing = false;
    console.log('网络错误提示已关闭');
  },

  // 清理资源
  cleanup() {
    if (this.notificationElement) {
      document.body.removeChild(this.notificationElement);
      this.notificationElement = null;
    }
    this.isShowing = false;
  }
};

// 添加页面卸载时的清理操作
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    NetworkErrorNotification.cleanup();
  });
}

// 全局变量，确保在整个应用范围内共享状态
let globalNetworkMessageKey: any = null;
let globalHasShownNetworkError = false;



// 初始化网络检测器
let networkDetector: ReturnType<typeof getNetworkDetector> | null = null;

if (typeof window !== 'undefined') {
  networkDetector = getNetworkDetector({
    pingUrl: '/api/web/user/info/ping', // 使用专用心跳检测API
    pingIntervalMs: 30000, // 每30秒检测一次
    maxConsecutiveFailures: 3, // 连续失败3次才判断网络有问题
    passThreshold: 0.5, // 得分率50%以上判定为网络正常
    levelWeights: {
      high: 5,    // 浏览器API检测权重加大
      medium: 3,  // WebSocket连接状态权重较中等
      low: 2      // 心跳检测权重较低
    }
  });

  // 监听网络状态变化
  networkDetector.addListener((isOnline) => {
    if (!isOnline) {
      NetworkErrorNotification.show();
    } else {
      NetworkErrorNotification.hide();
    }
  });
}
// 添加角色类型定义
interface Role {
  userId: number;
  roleId: number;
  roleName: string;
  nickName: string;
}
// 定义登录响应的数据结构
interface LoginData {
  expire: number;
  token: string;
  refreshExpire: number;
  refreshToken: string;
  userInfo?: {
    id: number;
    nickName: string;
    avatarUrl: string;
    phone: string;
    gender: number;
    roleId: number;
    studentNumber?: string;
    schoolInfo?: {
      id: number;
      name: string;
      province: string;
      city: string;
      district: string;
    };
  };
  roleList: Role[];  // 修改为具体的 Role 类型
  isNewUser?: boolean; // 标识是否为新注册用户
  sessionId?: string;
}

// 定义API响应的通用结构
interface ApiResponse<T> {
  code: number;
  message?: string;
  msg?: string;  // 添加msg字段
  data?: T;
}


interface StudentLoginParams {
  province: string;
  city: string;
  district: string;
  schoolName: string;
  studentNumber: string;
  password: string;
}

// 定义教师认证请求参数接口
interface TeacherAuthParams {
  teacherId: number;
  teacherName: string;
  schoolInfo: string;
  operationIp?: string;
  deviceInfo?: string;
  attachments?: Array<{
    url: string;
    name: string;
    type: string;
    size: number;
  }>;
}

// 定义教师认证响应接口
interface TeacherAuthResponse {
  code: number;
  message: string;
  data?: any;
}

// okok
export const auth = {
  _checkingStatus: false,
  _isLoginCheckStarted: false, // 添加静态标志，防止多次调用startLoginCheck

  async studentLogin(params: StudentLoginParams) {
    try {
      const response = await request.post<ApiResponse<LoginData>>('/api/user-auth/student', params);
      console.log(response);

      // 非200状态码显示错误信息
      if (response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '学生登录失败');
        return response.data;
      }

      if (response.data.data) {
        localStorage.setItem('token', response.data.data.token || '');
        localStorage.setItem('refreshToken', response.data.data.refreshToken);
        if (response.data.data.userInfo) {
          const userData = {
            id: response.data.data.userInfo.id,
            userId: response.data.data.userInfo.id,
            nickName: response.data.data.userInfo.nickName || '',
            avatarUrl: response.data.data.userInfo.avatarUrl || '',
            gender: response.data.data.userInfo.gender || 0,
            phone: response.data.data.userInfo.phone || '',
            isLoggedIn: true,
            roleId: response.data.data.userInfo.roleId,
          };
          store.dispatch(setUser(userData));
        }
      }
      console.log('token', localStorage.getItem('token'))
      return response.data;
    } catch (error: any) {
      console.error('Student login error:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '学生登录失败，请稍后重试');
      return {
        code: 500,
        message: '登录失败，请稍后重试',
      };
    }
  },

  async sendLoginCode(phone: string): Promise<ApiResponse<null>> {
    try {
      const response = await request.post<ApiResponse<null>>('/api/user-auth/code', {
        phone,
        scene: 'login' // 'login' or 'reset_password'
      });

      // 非200状态码显示错误信息
      if (response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '发送验证码失败');
      }

      return response.data;
    } catch (error: any) {
      console.error('Send login code error:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '发送验证码失败，请稍后重试');
      return { code: 500, message: '发送验证码失败' };
    }
  },

  async loginByCode(phone: string, code: string): Promise<ApiResponse<LoginData>> {
    try {
      const response = await request.post<ApiResponse<LoginData>>('/api/user-auth/login-by-code', {
        phone,
        code,
      });

      // 非200状态码显示错误信息
      if (response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '验证码登录失败');
        return response.data;
      }

      if (response.data.data) {
        localStorage.setItem('token', response.data.data.token || '');
        localStorage.setItem('refreshToken', response.data.data.refreshToken || '');
        if (response.data.data.userInfo) {
          store.dispatch(setUser(response.data.data.userInfo));
        }
      }
      return response.data;
    } catch (error: any) {
      console.error('Login by code error:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '登录失败，请稍后重试');
      return { code: 500, message: '登录失败，请稍后重试' };
    }
  },

  // okok

  async login(phone: string, password: string): Promise<ApiResponse<LoginData>> {
    try {
      const response = await request.post<ApiResponse<LoginData>>('/api/user-auth/password', {
        phone,
        password,
      });

      console.log("登录成功后返回的res", response);

      // 确保响应数据格式正确
      if (!response.data) {
        throw new Error('Invalid response format');
      }

      // 非200状态码显示错误信息
      if (response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '登录失败');
        return response.data;
      }

      console.log("登录成功后返回的res", response);

      // 如果返回了角色列表和sessionId，将sessionId保存到返回数据中
      if (response.data.data?.roleList && response.data.data.roleList.length > 0 && response.data.data.sessionId) {
        console.log("检测到多角色和sessionId:", response.data.data.sessionId);
        // sessionId已经在response.data.data中，不需要额外处理
      } else if (response.data.data?.token) {
        // 单角色情况，直接保存token和refreshToken
        localStorage.setItem('token', response.data.data.token || '');
        localStorage.setItem('refreshToken', response.data.data.refreshToken || '');
      }

      // 获取用户信息并更新状态
      if (response.data.data?.userInfo) {
        store.dispatch(setUser(response.data.data.userInfo));
      } else if (response.data.data?.roleList && response.data.data.roleList.length > 0) {
        // 如果没有直接返回userInfo，但有roleList，可能需要进一步处理
        // 例如，可以默认使用第一个角色信息，或引导用户选择
        const defaultRole = response.data.data.roleList[0];
        const userInfo = {
          id: defaultRole.userId,
          nickName: defaultRole.nickName,
          roleId: defaultRole.roleId,
          // ... 其他可以从roleList获取或设为默认值的信息
        };
        store.dispatch(setUser(userInfo));
      }

      return response.data;

    } catch (error: any) {
      console.error('Login error:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '登录失败，请稍后重试');
      return {
        code: 500,
        message: error instanceof Error ? error.message : '登录失败，请稍后重试',
      };
    }
  },

  // okok
  async logout() {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        await request.post('/api/user-auth/logout', {}, {
          headers: { Authorization: token }
        });
      }
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // localStorage.removeItem('refreshToken');
    }
  },

  isLoggedIn(): boolean {
    return !!localStorage.getItem('token');
  },

  /**
   * 获取网络状态
   * @returns 当前网络连接状态
   */
  getNetworkStatus(): boolean {
    if (networkDetector) {
      return networkDetector.getStatus();
    }
    // 如果网络检测器未初始化，使用浏览器API判断
    return typeof navigator !== 'undefined' ? navigator.onLine : true;
  },

  /**
   * 手动检查网络状态
   * @returns Promise<boolean> 网络状态检测的结果
   */
  async checkNetworkStatus(): Promise<boolean> {
    if (networkDetector) {
      return await networkDetector.checkNow();
    }
    return typeof navigator !== 'undefined' ? navigator.onLine : true;
  },

  // 根据id获取用户信息
  async getUserInfo(userId: number): Promise<ApiResponse<UserInfo>> {
    return await userApi.getUserInfo(userId);
  },

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      const token = localStorage.getItem('token');
      console.log("zww: 检查登录状态,当前本地存储的token为", token)
      if (!token) return false;

      // 添加防抖，避免频繁请求，已经检测过一次了，就不要再检测了，避免重复检测
      if (this._checkingStatus) return true;
      this._checkingStatus = true;

      // 调用 getUserInfo 时传入当前用户的 userId 或 0 作为默认值
      const userId = store.getState().user.userState.userId || 0;
      const response = await this.getUserInfo(userId);

      this._checkingStatus = false;

      if (response.code !== 200) {
        return false;
      }
      return true;
    } catch (error: any) {
      // 检查是否是401错误，表示登录已失效
      if (error.message.includes('401')) {
        console.log("zww: 登录过期!")
        return false
      }

      this._checkingStatus = false;
      return undefined; // 其他网络错误时返回undefined
    }
  },

  /**
   * 开始定时检查登录状态
   */

  startLoginCheck(interval = 60000) {
    // 如果已经启动过检查，则不再重复启动
    // if (this._isLoginCheckStarted) {
    //   return () => { };
    // }

    // this._isLoginCheckStarted = true;
    console.log("zww：开始网络检查！11");

    if (hasShownModal) return () => { };

    let checkTimer: NodeJS.Timeout | null = null;
    let isFirstCheck = true; // 添加标志位
    let timerIdLog = Math.floor(Math.random() * 10000); // 为每个定时器生成唯一ID，便于日志区分

    // console.log(`开始创建登录检查定时器 [${timerIdLog}]`);

    const check = async () => {
      // 执行检查函数
      const performCheck = async () => {


        // 先检查网络状态
        const networkOnline = networkDetector ? networkDetector.getStatus() : true;

        // 如果网络本身有问题，不需要执行登录状态检查
        if (!networkOnline) {
          if (!NetworkErrorNotification.isShowing) {
            NetworkErrorNotification.show();
          }

          // 设置下一次检查
          clearTimeout(checkTimer as NodeJS.Timeout);
          checkTimer = setTimeout(check, interval);
          console.log(`zww:网络状态不佳，重新设置定时器 [${timerIdLog}]`);
          return;
        }

        // 网络正常，检查登录状态，undefined为网络异常，false为登录过期
        const isValid = await this.checkLoginStatus();

        // 处理网络错误提示, 因为undefined是针对网络请求失败的情况
        if (isValid === undefined && !NetworkErrorNotification.isShowing) {
          // 网络断开且模态框未显示，显示模态框
          console.log("网络有问题");

          NetworkErrorNotification.show();
        } else if (isValid !== undefined && NetworkErrorNotification.isShowing) {
          // 网络恢复且模态框正在显示，关闭模态框
          NetworkErrorNotification.hide();
        }

        // 处理登录状态
        if (isValid === false && !hasShownModal) {


          // message.error("登录状态已过期！请重新登录！")
          clearTimeout(checkTimer as NodeJS.Timeout);
          checkTimer = null;
          // // 静默处理登录失效
          localStorage.removeItem('token');
          localStorage.removeItem('user');

          store.dispatch(clearUser());
          return;
        }

        // 清除之前的定时器（如果有）并设置新的定时器,进行下一次递归检查
        clearTimeout(checkTimer as NodeJS.Timeout);
        checkTimer = setTimeout(check, interval);
        // console.log(`设置下一次登录检查定时器 [${timerIdLog}]`);
      };

      if (isFirstCheck) {
        isFirstCheck = false;
        // 为第一次检查添加 1.5 秒延迟，避免应用启动时的资源竞争，刚启动时网络连接可能还在建立过程中，立即检查可能会得到错误的网络状态
        console.log(`首次检查，延迟1.5秒 [${timerIdLog}]`);
        setTimeout(performCheck, 1500);
      } else {
        performCheck();
      }
    };

    // 初始调用
    check();

    // 返回清理函数
    return () => {
      if (checkTimer) {
        clearTimeout(checkTimer);
        checkTimer = null;
        console.log(`zww:清理登录检查定时器 [${timerIdLog}]`);
      }

      NetworkErrorNotification.hide(); // 确保在卸载时隐藏提示
    };
  },




  // 使用验证码登录
  // okok
  verifyCodeLogin: async (phone: string, code: string) => {
    try {
      const response = await request.post('/api/user-auth/smsLogin', { phone, code });
      // 确保响应数据格式正确
      if (!response.data) {
        throw new Error('Invalid response format');
      }

      // 非200状态码显示错误信息
      if (response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '验证码登录失败');
        return response.data;
      }

      if (response.data.data) {
        localStorage.setItem('token', response.data.data.token || '');

        // 检查是否为新用户，记录到localStorage中
        if (response.data.data.isNewUser) {
          localStorage.setItem('isNewUser', 'true');
        }
      }

      return response.data;
    } catch (error: any) {
      console.error('验证码登录失败:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '验证码登录失败，请稍后重试');
      return {
        code: 500,
        message: '验证码登录失败',
      };
    }
  },

  // 简单设置密码
  setPassword: async (phone: string, password: string) => {
    try {
      const response = await request.post('/api/user-auth/setPasswordByPhone', { phone, password });

      // 非200状态码显示错误信息
      if (response.data && response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '设置密码失败');
      }

      return response.data;
    } catch (error: any) {
      console.error('设置密码失败:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '设置密码失败，请稍后重试');
      return {
        code: 500,
        message: error instanceof Error ? error.message : '设置密码失败，请稍后重试',
      };
    }
  },

  // 使用验证码修改密码
  // okok
  resetPasswordByCode: async (params: { phone: string, code: string, newPassword: string }) => {
    try {
      const response = await request.post('/api/user-auth/resetPasswordByCode', params);

      // 非200状态码显示错误信息
      if (response.data && response.data.code !== 200) {
        message.error(response.data.message || response.data.msg || '重置密码失败');
      }

      return response.data;
    } catch (error: any) {
      console.error('重置密码失败:', error);
      message.error(error.response?.data?.message || error.response?.data?.msg || error instanceof Error ? error.message : '重置密码失败，请稍后重试');
      return {
        code: 500,
        message: error instanceof Error ? error.message : '重置密码失败，请稍后重试',
      };
    }
  },

  // 教师认证提交
  async submitTeacherAuth(params: TeacherAuthParams): Promise<TeacherAuthResponse> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return {
          code: 401,
          message: '未登录，请先登录'
        };
      }

      // 获取设备信息
      if (!params.deviceInfo && typeof window !== 'undefined') {
        params.deviceInfo = window.navigator.userAgent;
      }

      // 发送教师认证请求
      const response = await request.post<TeacherAuthResponse>(
        '/api/teacher-audit',
        params,
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      console.log('教师认证提交结果:', response);

      if (response.data && response.data.code === 200) {
        return {
          code: 200,
          message: '教师认证申请提交成功',
          data: response.data.data
        };
      }

      return response.data || {
        code: 500,
        message: '教师认证申请提交失败'
      };
    } catch (error: any) {
      console.error('教师认证申请提交失败:', error);
      return {
        code: 500,
        message: error instanceof Error ? error.message : '教师认证申请提交失败，请稍后重试'
      };
    }
  },

  // 获取教师认证状态
  async getTeacherAuthStatus(): Promise<TeacherAuthResponse> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return {
          code: 401,
          message: '未登录，请先登录'
        };
      }

      // 获取当前教师认证状态
      const response = await request.get<TeacherAuthResponse>(
        '/api/teacher-audit/teacher',
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      if (response.data && response.data.code === 200) {
        return {
          code: 200,
          message: '获取教师认证状态成功',
          data: response.data.data
        };
      }

      return response.data || {
        code: 404,
        message: '未找到教师认证记录'
      };
    } catch (error: any) {
      // 如果是404错误(未找到记录)，返回特定的响应
      if (error.response && error.response.status === 404) {
        return {
          code: 404,
          message: '您尚未提交教师认证申请'
        };
      }

      console.error('获取教师认证状态失败:', error);
      return {
        code: 500,
        message: error instanceof Error ? error.message : '获取教师认证状态失败，请稍后重试'
      };
    }
  },
};
//11

export default auth; 