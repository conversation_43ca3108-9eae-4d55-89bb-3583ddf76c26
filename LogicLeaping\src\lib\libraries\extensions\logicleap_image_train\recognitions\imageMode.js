const JSZip = require('jszip');

// 导入UI更新模块
const updateUIWithModelLabels = require('./updateUIWithModelLabels');
const importModelData = require('./importModelData');
const importModel = require('./importModel');

// ---- 导入新的 API 模块 ----
const ossApi = require('@api/OSS_Upload.ts').default; // 使用 require 并取 default
const workApi = require('@api/work_api.ts').default; // 使用 require 并取 default
// -------------------------

// 导入所需的依赖
const notification = require('../../utils/notification').default;
const Notification = require('../../utils/trainNotification');
// 导入加载动画组件

class ImageMode {
    // eslint-disable-next-line no-useless-constructor
    constructor () {
        // 初始化变量
        this.visible = false;
        this.container = null;
        this.canvas = null;
        this.overlay = null;
        this.currentClass = -1;
        this.currentRecordIndex = -1;
        this.videoPlaying = false;
        this.infoTexts = {};
        this.connections = [];
        this.nodeData = {
            classNodes: [],
            trainNode: null,
            previewNode: null
        };
        this.IMAGE_SIZE = 224;
        this.classInfo = {}; // 初始化类别信息对象
        this.modelData = null; // 初始化模型数据对象
        
        // 导入组件
    }

    // 修改方法签名以接受直接参数
    show ({container}) {
        this.container = container;
        
        // 添加全局引用，便于其他部分访问
        window._imageModeInstance = this;
        
        // 获取父实例，并同步模型计数
        const parentInstance = window._logicleapImageTrainInstance;
        if (parentInstance) {
           
            // 如果没有模型计数，初始化为0
            if (typeof parentInstance.modelCount === 'undefined' || parentInstance.modelCount === null) {
                parentInstance.modelCount = 0;
            }
           
            console.warn('未找到父实例，无法同步模型计数');
        }
        
        if (this.visible) {
            // 如果已经显示，先关闭现有对话框
            this.close();
        }

        // 检查 container 是否为 DOM 元素
        if (!container || typeof container.appendChild !== 'function') {
            console.error('无效的容器元素:', container);
            return;
        }

        // 设置可见状态
        this.visible = true;

        // 重置状态
        this.currentClass = -1;
        this.currentRecordIndex = -1;

        // 清理之前的节点和连接
        this.connections = [];

        // 创建主要内容区域（画布）
        this.canvas = document.createElement('div');
        this.canvas.style.cssText = `
            flex: 1;
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
        `;

        // 创建SVG层用于绘制连接线
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        `;
        this.canvas.appendChild(svg);

        // 添加窗口缩放事件监听器
        const resizeHandler = () => {
            requestAnimationFrame(() => {
                this.updateConnectionsAfterDOMChange();
            });
        };
        window.addEventListener('resize', resizeHandler);
        
        // 保存引用以便在关闭时移除
        this.resizeHandler = resizeHandler;

        // 创建主容器 - 水平居中
        const mainContainer = document.createElement('div');
        mainContainer.style.cssText = `
            width: 90%;
            max-width: 1600px;
            height: 100%;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-between;
        `;

        // 创建五个区域
        // 1. 类别列表区域 (45%)
        const classesArea = document.createElement('div');
        classesArea.style.cssText = `
            width: 45%;
            height: 100%;
            position: relative;
        `;

        // 2. 第一个间隙 (7.5%)
        const gap1 = document.createElement('div');
        gap1.style.cssText = `
            width: 7.5%;
            height: 100%;
        `;

        // 3. 训练节点区域 (15%)
        const trainArea = document.createElement('div');
        trainArea.style.cssText = `
            width: 15%;
            height: 100%;
            position: relative;
        `;

        // 4. 第二个间隙 (7.5%)
        const gap2 = document.createElement('div');
        gap2.style.cssText = `
            width: 7.5%;
            height: 100%;
        `;

        // 5. 预览节点区域 (25%)
        const previewArea = document.createElement('div');
        previewArea.style.cssText = `
            width: 25%;
            height: 100%;
            position: relative;
        `;

        // 将五个区域添加到主容器
        mainContainer.appendChild(classesArea);
        mainContainer.appendChild(gap1);
        mainContainer.appendChild(trainArea);
        mainContainer.appendChild(gap2);
        mainContainer.appendChild(previewArea);

        // 将主容器添加到画布
        this.canvas.appendChild(mainContainer);

        // 修改类别容器的创建
        const classNodesContainer = document.createElement('div');
        classNodesContainer.style.cssText = `
            width: 100%;
            max-height: 100vh;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            position: relative;
            top: 50%;
            transform: translateY(-50%);
        `;

        // 创建类别节点容器
        const classesContainer = document.createElement('div');
        classesContainer.className = 'classes-container'; // 添加类名以便后续查找
        classesContainer.style.cssText = `
            flex: 1;
            overflow-y: auto;
            width: 100%;
            padding: 0 10px;
        `;

        // 添加滚动事件监听器
        classesContainer.addEventListener('scroll', () => {
            // 更新连接线
            requestAnimationFrame(() => {
                this.updateConnections();
            });
        });

        // 添加鼠标滚轮事件监听器
        classesContainer.addEventListener('wheel', () => {
            // 更新连接线
            requestAnimationFrame(() => {
                this.updateConnections();
            });
        });
        
        // 创建添加类别按钮容器
        const addButtonContainer = document.createElement('div');
        addButtonContainer.style.cssText = `
            padding: 16px;
            display: flex;
            justify-content: center;
        `;

        // 创建添加类别按钮
        const addButton = document.createElement('button');
        addButton.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            background: #f0f0f0;
            border: 1px dashed #ccc;
            border-radius: 8px;
            padding: 12px 24px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
        `;
        addButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            添加类别
        `;
        
        // 添加类别按钮点击事件
        addButton.onclick = (e) => {
            e.preventDefault();
            
            // 在添加新类别前首先清理现有的摄像头流（如果有）
            if (typeof this.cleanupWebcamStreams === 'function') {
                this.cleanupWebcamStreams();
            }
            
            // 生成一个新的唯一索引
            let newIndex = 0;
            if (this.nodeData.classNodes.length > 0) {
                // 找出当前最大的索引值加1作为新索引
                newIndex = Math.max(...this.nodeData.classNodes.map(node => node.index)) + 1;
            }
            
            //////console.log(`添加新类别，索引: ${newIndex}`);
            
            // 创建新的类别节点
            const classNodeInstance = new this.ClassNode({
                index: newIndex,
                position: {x: 0, y: 0},
                parentContext: this
            });
            
            const newClassNode = classNodeInstance.node;
            newClassNode.style.position = 'relative';
            newClassNode.style.marginBottom = '16px';
            newClassNode.style.width = '100%';
            // 将新类别插入到添加按钮之前，确保按钮始终在最下面
            classesContainer.insertBefore(newClassNode, addButton);
            
            // 添加到节点数据中
            this.nodeData.classNodes.push({
                node: newClassNode,
                index: newIndex,
                position: {x: 0, y: 0},
                instance: classNodeInstance
            });
            
            // 添加连接线
            if (this.nodeData.trainNode) {
                this.connections.push({
                    from: newClassNode,
                    to: this.nodeData.trainNode
                });
            }
            
            // 使用新方法更新连接线
            this.updateConnectionsAfterDOMChange();
            
            // 初始化新类别的训练数据存储
            if (!this.images) {
                this.images = {};
            }
            this.images[newIndex] = [];
            
            // 更新训练按钮状态
            this.checkSamplesForTraining();
            
            // 滚动到新添加的类别
            setTimeout(() => {
                if (newClassNode) {
                    newClassNode.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }, 100);
        };

        // 初始化两个默认类别
        const classNodes = [];
        for (let i = 0; i < 2; i++) {
            // 使用ClassNode组件创建类别节点
            const classNodeInstance = new this.ClassNode({
                index: i,
                position: {x: 0, y: i * 20},
                parentContext: this
            });
            
            const classNode = classNodeInstance.node;
            classNode.style.position = 'relative';
            classNode.style.marginBottom = '16px';
            classNode.style.width = '100%';
            classNodes.push(classNode);
            classesContainer.appendChild(classNode);
            
            // 添加到节点数据中
            this.nodeData.classNodes.push({
                node: classNode,
                index: i,
                position: {x: 0, y: i * 20},
                instance: classNodeInstance // 保存实例引用
            });
            
            // 确保初始化图像数据结构
            if (!this.images) {
                this.images = {};
            }
            this.images[i] = [];
        }

        // 组装容器
        classNodesContainer.appendChild(classesContainer);
        classesContainer.appendChild(addButton); // 将添加按钮直接放入类别列表容器
        classesArea.appendChild(classNodesContainer);

        // 创建训练和预览节点
        // 使用TrainNode组件创建训练节点
        const trainNodeInstance = new this.TrainNode({
            position: {x: 0, y: 0},
            parentContext: this
        });
        
        const trainNode = trainNodeInstance.node;

        // 使用PreviewNode组件创建预览节点
        const previewNodeInstance = new this.PreviewNode({
            position: {x: 0, y: 0},
            parentContext: this
        });
        
        const previewNode = previewNodeInstance.node;

        // 调整节点样式以适应新布局
        trainNode.style.position = 'relative';
        trainNode.style.left = '0';
        trainNode.style.top = '50%';
        trainNode.style.transform = 'translateY(-50%)';
        trainNode.style.marginTop = '0';

        previewNode.style.position = 'relative';
        previewNode.style.left = '0';
        previewNode.style.top = '50%';
        previewNode.style.transform = 'translateY(-50%)';
        previewNode.style.width = '100%';
        previewNode.style.marginTop = '0';

        // 存储训练和预览节点
        this.nodeData.trainNode = trainNode;
        this.nodeData.previewNode = previewNode;
        this.nodeData.trainNodeInstance = trainNodeInstance; // 保存实例引用
        this.nodeData.previewNodeInstance = previewNodeInstance; // 保存实例引用
        
        // 将预览节点实例添加到this对象上，使其可被TrainNode访问
        this.previewNode = previewNode;
        this.previewNodeInstance = previewNodeInstance;
        
        // 添加连接线
        this.connections = [
            ...classNodes.map(classNode => ({from: classNode, to: trainNode})),
            {from: trainNode, to: previewNode}
        ];

        // 将节点添加到各自的区域
        trainArea.appendChild(trainNode);
        previewArea.appendChild(previewNode);

        // 添加到容器
        container.appendChild(this.canvas);

        // 使用setTimeout确保DOM已经渲染后再更新连接线
        setTimeout(() => {
            this.updateConnectionsAfterDOMChange();
        }, 100);

        // 初始化 Teachable Machine
        this.initTeachableMachine().then(() => {
            this.startCapture();
            this.updateConnectionsAfterDOMChange();
        });
    }

    // 初始化 TensorFlow.js 和模型
    async initTeachableMachine () {
        const { createSquareLoadingAnimation } = require('../../utils/loadingAnimation');
        const loader = createSquareLoadingAnimation({
            message: '正在初始化模型...',
            backgroundColor: '#4766C2',
            boxColor: '#ffffff',
            textColor: '#ffffff'
        });

        try {
            loader.updateProgress('加载TensorFlow.js...', 20);
            const tf = require('@tensorflow/tfjs');
            await tf.setBackend('webgl').catch(() => tf.setBackend('cpu'));

            loader.updateProgress('加载MobileNet模型...', 40);
            const mobilenetModule = require('@tensorflow-models/mobilenet');
            const knnClassifier = require('@tensorflow-models/knn-classifier');

            loader.updateProgress('初始化分类器...', 60);
            this.knn = knnClassifier.create();

            loader.updateProgress('加载预训练模型...', 80);
            const modelUrl = `${window.location.origin}/static/utils/image_train/model.json`;
            this.mobilenet = await mobilenetModule.load({version: 1, alpha: 0.25, modelUrl: modelUrl});

            loader.updateProgress('准备就绪', 100);
            loader.updateMessage('模型初始化完成！');
            setTimeout(() => loader.remove(), 500);
        } catch (error) {
            loader.updateMessage('初始化失败');
            loader.updateProgress(error.message);
            setTimeout(() => loader.remove(), 3000);
            console.error('TensorFlow 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化 Teachable Machine 模型（无动画）
     */
    async initTeachableMachineWithoutAnimation() {
        try {
            const tf = require('@tensorflow/tfjs');
            await tf.setBackend('webgl').catch(() => tf.setBackend('cpu'));
            
            const mobilenetModule = require('@tensorflow-models/mobilenet');
            const knnClassifier = require('@tensorflow-models/knn-classifier');
            
            this.knn = knnClassifier.create();

            const modelUrl = `${window.location.origin}/static/utils/image_train/model.json`;
            this.mobilenet = await mobilenetModule.load({version: 1, alpha: 0.25, modelUrl: modelUrl});
            
        } catch (error) {
            console.error('TensorFlow (无动画) 初始化失败:', error);
            throw error;
        }
    }

    // 创建其他类型节点（训练、导出等）
    createNode (title, color, position) {
        const node = document.createElement('div');
        node.style.cssText = `
            position: absolute;
            left: ${position.x}px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            width: ${title === '预览' ? '280px' : '360px'};
            cursor: move;
            user-select: none;
        `;

        const nodeHeader = document.createElement('div');
        nodeHeader.style.cssText = `
            background: ${color};
            color: black;
            padding: 16px 24px;
            font-size: 18px;
            font-weight: 500;
            border-radius: 12px 12px 0 0;
        `;
        nodeHeader.textContent = title;

        const nodeContent = document.createElement('div');
        nodeContent.style.cssText = `
            padding: 24px;
            background: white;
            border-radius: 0 0 12px 12px;
        `;

        // 根据节点类型添加不同的内容
        if (title === '采集数据') {
            nodeContent.appendChild(this.createGatherContent());
        } else if (title === '训练') {
            // 这里不再需要，因为我们使用了TrainNode组件
            // nodeContent.appendChild(this.createTrainContent());
        } else if (title === '预览') {
            // 这里不再需要，因为我们使用了PreviewNode组件
            // nodeContent.appendChild(this.createPreviewContent());
        } else if (title === '导出') {
            nodeContent.appendChild(this.createExportContent());
        }

        node.appendChild(nodeHeader);
        node.appendChild(nodeContent);
        return node;
    }

    // 更新节点间的连接线
    updateConnections () {
        // 清除现有的连接线
        const svg = this.canvas.querySelector('svg');
        if (!svg) return;
        svg.innerHTML = '';

        // 使用存储的节点数据创建连接
        this.nodeData.classNodes.forEach(classNodeData => {
            // 连接类别节点到训练节点
            this.drawConnection(
                classNodeData.node,
                this.nodeData.trainNode,
                svg
            );
        });

        // 连接训练节点到预览节点
        this.drawConnection(
            this.nodeData.trainNode,
            this.nodeData.previewNode,
            svg
        );
    }

    // 添加绘制连接的辅助方法
    drawConnection (fromNode, toNode, svg) {
        if (!fromNode || !toNode) return;

        const fromRect = fromNode.getBoundingClientRect();
        const toRect = toNode.getBoundingClientRect();
        const svgRect = svg.getBoundingClientRect();

        // 计算连接线的起点和终点
        const start = {
            x: fromRect.right - svgRect.left,
            // eslint-disable-next-line no-mixed-operators
            y: fromRect.top + fromRect.height / 2 - svgRect.top
        };

        const end = {
            x: toRect.left - svgRect.left,
            // eslint-disable-next-line no-mixed-operators
            y: toRect.top + toRect.height / 2 - svgRect.top
        };

        // 创建连接线
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        // eslint-disable-next-line no-mixed-operators
        const controlPoint1X = start.x + (end.x - start.x) * 0.5;
        // eslint-disable-next-line no-mixed-operators
        const controlPoint2X = start.x + (end.x - start.x) * 0.5;
        
        const d = `
            M ${start.x},${start.y}
            C ${controlPoint1X},${start.y}
                ${controlPoint2X},${end.y}
                ${end.x},${end.y}
        `;

        path.setAttribute('d', d);
        path.setAttribute('stroke', '#4766C2');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('fill', 'none');

        svg.appendChild(path);
    }

    // 创建数据采集内容区域
    createGatherContent () {
        const content = document.createElement('div');
        content.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 16px;
        `;

        // 添加类别列表
        const classList = document.createElement('div');
        classList.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 12px;
        `;

        // 创建类别卡片
        for (let i = 0; i < 3; i++) {
            const classCard = document.createElement('div');
            classCard.style.cssText = `
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            `;

            const cardHeader = document.createElement('div');
            cardHeader.style.cssText = `
                padding: 12px 16px;
                background: ${i === 0 ? '#e8f0fe' : 'white'};
                border-bottom: 1px solid #e0e0e0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;

            const className = document.createElement('input');
            className.type = 'text';
            className.value = `类别 ${i + 1}`;
            className.style.cssText = `
                border: none;
                background: none;
                font-size: 14px;
                font-weight: 500;
                color: #202124;
                width: 120px;
                padding: 0;
            `;

            const cardContent = document.createElement('div');
            cardContent.style.cssText = `
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 8px;
            `;

            const holdButton = document.createElement('button');
            holdButton.innerHTML = '按住录制';
            holdButton.style.cssText = `
                background: #f1f3f4;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                color: #202124;
                font-size: 14px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            `;

            const webcamIcon = document.createElement('span');
            webcamIcon.innerHTML = '📷';
            holdButton.prepend(webcamIcon);

            const sampleCount = document.createElement('div');
            sampleCount.style.cssText = `
                color: #5f6368;
                font-size: 12px;
                display: flex;
                justify-content: space-between;
            `;
            sampleCount.innerHTML = '<span>样本数量</span><span>0</span>';
            this.infoTexts[i] = sampleCount.querySelector('span:last-child');

            cardContent.appendChild(holdButton);
            cardContent.appendChild(sampleCount);

            cardHeader.appendChild(className);
            classCard.appendChild(cardHeader);
            classCard.appendChild(cardContent);

            // 添加训练事件
            holdButton.addEventListener('mousedown', () => {
                this.currentRecordIndex = i;
                holdButton.style.background = '#e8f0fe';
            });
            holdButton.addEventListener('mouseup', () => {
                this.currentRecordIndex = -1;
                holdButton.style.background = '#f1f3f4';
            });

            classList.appendChild(classCard);
        }

        // 添加新类别按钮
        const addClassButton = document.createElement('button');
        addClassButton.innerHTML = '+ 添加新类别';
        addClassButton.style.cssText = `
            background: none;
            border: 1px dashed #dadce0;
            border-radius: 8px;
            padding: 12px;
            color: #1a73e8;
            font-size: 14px;
            cursor: pointer;
            margin-top: 8px;
        `;

        content.appendChild(classList);
        content.appendChild(addClassButton);
        return content;
    }

    // 修改预览节点的创建方法
    createPreviewContent() {
        const container = document.createElement('div');
        
        // 创建预览区域
        this.previewContainer = document.createElement('div');
        this.previewContainer.style.cssText = `
            width: 100%;
            aspect-ratio: 4/3;
            background: #f5f5f5;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            margin-bottom: 16px;
        `;
        
        // 创建预览视频元素
        const video = document.createElement('video');
        video.style.cssText = `
            width: 100%;
            height: 100%;
            object-fit: cover;
        `;
        video.autoplay = true;
        video.muted = true;
        video.playsInline = true;
        
        // 创建预览状态显示
        this.previewStatus = document.createElement('div');
        this.previewStatus.style.cssText = `
            position: absolute;
            bottom: 16px;
            left: 16px;
            right: 16px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        `;
        this.previewStatus.innerHTML = `
            <span>未训练</span>
            <span class="prediction-confidence"></span>
        `;
        
        // 添加到预览容器
        this.previewContainer.appendChild(video);
        this.previewContainer.appendChild(this.previewStatus);
        
        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.cssText = `
            display: flex;
            gap: 10px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        `;
        
        // 创建开始预览按钮
        const startPreviewButton = document.createElement('button');
        startPreviewButton.style.cssText = `
            flex: 1;
            min-width: 120px;
            background: #4766C2;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s;
        `;
        startPreviewButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polygon points="10 8 16 12 10 16 10 8"/>
            </svg>
            开始预览
        `;
        
        // 创建使用模型按钮
        const useModelButton = document.createElement('button');
        useModelButton.style.cssText = `
            flex: 1;
            min-width: 120px;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s;
        `;
        useModelButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
            </svg>
            使用模型
        `;
        
        // 创建导出模型按钮
        const exportModelButton = document.createElement('button');
        exportModelButton.style.cssText = `
            flex: 1;
            min-width: 120px;
            background: #ea4335;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s;
        `;
        exportModelButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7 10 12 15 17 10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            导出模型
        `;
        
        // 创建保存到云端按钮
        const saveToCloudButton = document.createElement('button');
        saveToCloudButton.style.cssText = `
            flex: 1;
            min-width: 120px;
            background: #9C27B0;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s;
        `;
        saveToCloudButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9"></path>
                <polyline points="13 11 9 17 15 17 11 23"></polyline>
            </svg>
            保存到云端
        `;
        
        // 添加导出模型按钮点击事件
        exportModelButton.addEventListener('click', () => {
            // 检查模型是否已训练
            if (!this.modelTrained) {
                Notification.error('请先训练模型');
                return;
            }
            
            // 导出模型
            this.exportModel();
        });
        
        // 添加使用模型按钮点击事件
        useModelButton.addEventListener('click', () => {
            // 检查模型是否已训练
            if (!this.modelTrained) {
                Notification.error('请先训练模型');
                return;
            }
            
            // 使用模型并关闭窗口
            this.useModelAndClose();
        });
        
        // 添加保存到云端按钮点击事件
        saveToCloudButton.addEventListener('click', () => {
            // 检查模型是否已训练
            if (!this.modelTrained) {
                Notification.error('请先训练模型');
                return;
            }
            
            // 保存模型到云端
            this.saveModelToCloud();
        });
        
        // 添加按钮点击事件
        startPreviewButton.addEventListener('click', async () => {
            // 检查模型是否已训练
            if (!this.modelTrained) {
                Notification.error('请先训练模型');
                return;
            }
            
            // 开始预览
            try {
                // 获取摄像头权限
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                this.videoStream = stream;
                
                // 更新按钮状态
                startPreviewButton.textContent = '停止预览';
                startPreviewButton.onclick = () => this.stopPreview(startPreviewButton);
                
                // 更新预览状态
                this.previewStatus.querySelector('span').textContent = '预览中...';
                
                // 开始预测
                this.startPrediction(video);
                
            } catch (error) {
                console.error('无法访问摄像头:', error);
                // 显示错误提示
                Notification.error('无法访问摄像头');
            }
        });
        
        // 添加按钮到容器
        buttonContainer.appendChild(startPreviewButton);
        buttonContainer.appendChild(useModelButton);
        buttonContainer.appendChild(exportModelButton);
        buttonContainer.appendChild(saveToCloudButton);
        
        // 添加到容器
        container.appendChild(this.previewContainer);
        container.appendChild(buttonContainer);
        
        return container;
    }
    
    // 使用模型并关闭窗口
    useModelAndClose() {
        // 检查模型是否已训练
        if (!this.modelTrained) {
            Notification.error('请先训练模型');
            return;
        }
        
        // 导出模型数据
        const modelData = this.exportModelData();
        
        // 获取父实例，并同步模型数据
        const parentInstance = window._logicleapImageTrainInstance;
        if (parentInstance) {
            parentInstance.setModelData(modelData);
        }
        
        // 关闭窗口
        this.close();
    }
    
    // 导出模型数据
    exportModelData() {
        // 检查是否已训练模型
        if (!this.modelTrained || !this.knn) {
            Notification.error('请先训练模型');
            return null;
        }
        
        // 获取所有类别的标签
        const labels = this.nodeData.classNodes.map(node => {
            return node.node.querySelector('input')?.value || `类别${node.index}`;
        });
        
        // 获取分类器数据集
        const dataset = this.knn.getClassifierDataset();
        
        // 将张量转换为可序列化的对象 - 添加量化处理以减小文件大小
        const tf = require('@tensorflow/tfjs');
        const serializedDataset = {};
        
        Object.keys(dataset).forEach((key) => {
            const tensor = dataset[key];
            // 找出数据范围以便进行更好的量化
            const values = tensor.dataSync();
            
            // 找出最大和最小值，用于量化
            let minVal = Number.POSITIVE_INFINITY;
            let maxVal = Number.NEGATIVE_INFINITY;
            for (let i = 0; i < values.length; i++) {
                if (values[i] < minVal) minVal = values[i];
                if (values[i] > maxVal) maxVal = values[i];
            }
            
            // 将32位浮点数量化为16位整数
            const range = maxVal - minVal;
            const quantizedValues = new Int16Array(values.length);
            for (let i = 0; i < values.length; i++) {
                // 将值映射到0-65535范围
                const normalizedVal = (values[i] - minVal) / range;
                // 量化为16位整数
                quantizedValues[i] = Math.round(normalizedVal * 32767);
            }
            
            // 保存量化后的数据和还原所需的信息
            serializedDataset[key] = {
                quantizedValues: Array.from(quantizedValues),
                shape: tensor.shape,
                minVal: minVal,
                maxVal: maxVal,
                quantized: true
            };
        });
        
        // 构建完整的模型数据
        const modelData = {
            modelType: 'knn-classifier',
            dataset: serializedDataset,
            labels: labels,
            modelVersion: '1.2',
            exportDate: new Date().toISOString(),
            compression: 'int16',
        };
        
        return modelData;
    }
    
    // 保存模型到云端
    async saveModelToCloud() {
        // 检查模型是否已训练
        if (!this.modelTrained) {
            Notification.error('请先训练模型');
            return;
        }

        try {
            // 导出模型数据 (exportModelData 逻辑不变)
            const modelDataToSave = this.exportModelData();
            if (!modelDataToSave) {
                Notification.error('无法导出模型数据');
                return;
            }

            // 创建弹窗 (弹窗逻辑不变)
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;
            
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 24px;
                width: 400px;
                max-width: 90%;
            `;
            
            const modalTitle = document.createElement('h3');
            modalTitle.textContent = '保存模型到云端';
            modalTitle.style.cssText = `
                margin-top: 0;
                margin-bottom: 16px;
                font-size: 18px;
                font-weight: 500;
            `;
            
            const nameLabel = document.createElement('label');
            nameLabel.textContent = '模型名称';
            nameLabel.style.cssText = `
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #333;
            `;
            
            const nameInput = document.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '请输入模型名称';
            nameInput.style.cssText = `
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                margin-bottom: 16px;
                box-sizing: border-box;
            `;
            
            const descLabel = document.createElement('label');
            descLabel.textContent = '模型描述';
            descLabel.style.cssText = `
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                color: #333;
            `;
            
            const descInput = document.createElement('textarea');
            descInput.placeholder = '请输入模型描述（可选）';
            descInput.style.cssText = `
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                margin-bottom: 16px;
                box-sizing: border-box;
                min-height: 80px;
                resize: vertical;
            `;
            
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                justify-content: flex-end;
                gap: 12px;
            `;
            
            const cancelButton = document.createElement('button');
            cancelButton.textContent = '取消';
            cancelButton.style.cssText = `
                padding: 8px 16px;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                background: white;
                cursor: pointer;
            `;
            
            const saveButton = document.createElement('button');
            saveButton.textContent = '保存';
            saveButton.style.cssText = `
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                background: #4766C2;
                color: white;
                cursor: pointer;
            `;
            
            // 添加取消按钮点击事件
            cancelButton.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
            
            // 添加保存按钮点击事件
            saveButton.addEventListener('click', async () => {
                const modelName = nameInput.value.trim();
                const modelDesc = descInput.value.trim();

                if (!modelName) {
                    alert('请输入模型名称');
                    return;
                }

                saveButton.disabled = true;
                saveButton.textContent = '保存中...';

                try {
                    // 准备模型 Blob
                    const modelBlob = new Blob([JSON.stringify(modelDataToSave)], { type: 'application/json' });

                    // 生成唯一的文件名
                    const timestamp = Date.now();
                    const randomStr = Math.random().toString(36).substring(2, 8);
                    const fileName = `image-models/${modelName}_${timestamp}_${randomStr}.json`;

                    // ---- 使用 ossApi 上传文件 ----
                    const formData = new FormData();
                    formData.append('file', modelBlob, fileName);
                    const fileUrl = await ossApi.uploadFile(formData);
                    // ---------------------------

                    // 准备传递给 workApi 的元数据
                    const modelMetaData = {
                        name: modelName,
                        description: modelDesc,
                        fileUrl: fileUrl,
                        labels: modelDataToSave.labels,
                        isPublic: true
                    };

                    // ---- 使用 workApi 保存元数据 ----
                    const result = await workApi.saveImageModel(modelMetaData);
                    // -----------------------------------

                    if (result.success) {
                        document.body.removeChild(modal);
                        Notification.success('模型已成功保存到云端');
                    } else {
                        throw new Error(result.message || '保存模型元数据失败');
                    }

                } catch (error) {
                    console.error('保存模型失败:', error);
                    Notification.error('保存模型失败: ' + error.message);
                    saveButton.disabled = false;
                    saveButton.textContent = '保存';
                }
            });
            
            // 组装DOM
            buttonContainer.appendChild(cancelButton);
            buttonContainer.appendChild(saveButton);
            
            modalContent.appendChild(modalTitle);
            modalContent.appendChild(nameLabel);
            modalContent.appendChild(nameInput);
            modalContent.appendChild(descLabel);
            modalContent.appendChild(descInput);
            modalContent.appendChild(buttonContainer);
            
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // 自动聚焦到名称输入框
            nameInput.focus();
            
            
        } catch (error) {
            console.error('保存模型到云端准备失败:', error);
            Notification.error('保存模型到云端失败: ' + error.message);
        }
    }

    // 添加开始预测方法
    startPrediction(video) {
        // 创建预测循环
        this.predictionInterval = setInterval(async () => {
            if (!video.videoWidth) return;
            
            try {
                // 获取视频帧
                const tf = require('@tensorflow/tfjs');
                    const image = tf.browser.fromPixels(video);
                
                // 使用 MobileNet 提取特征
                    const logits = this.mobilenet.infer(image, 'conv_pw_13_relu');
                
                // 使用 KNN 进行预测
                const res = await this.knn.predictClass(logits, this.TOPK);
                
                // 获取预测结果
                const classIndex = res.label;
                const confidence = res.confidences[classIndex] * 100;
                
                // 更新预览状态
                const classNode = this.nodeData.classNodes.find(n => n.index === parseInt(classIndex));
                if (classNode) {
                    const className = classNode.node.querySelector('input').value;
                    this.previewStatus.querySelector('span').textContent = `预测: ${className}`;
                    this.previewStatus.querySelector('.prediction-confidence').textContent = `${confidence.toFixed(1)}%`;
                }
                
                // 释放内存
                    image.dispose();
                    logits.dispose();

                } catch (error) {
                    console.error('预测错误:', error);
            }
        }, 100); // 每100毫秒预测一次
    }

    // 停止预览
    stopPreview() {
        // 停止预测循环
        if (this.predictionInterval) {
            clearInterval(this.predictionInterval);
            this.predictionInterval = null;
        }
        
        // 停止视频流
        if (this.videoStream) {
            const tracks = this.videoStream.getTracks();
            tracks.forEach(track => track.stop());
            this.videoStream = null;
        }
        
        // 清空视频源
        if (this.previewContainer) {
            const video = this.previewContainer.querySelector('video');
            if (video) {
                video.srcObject = null;
            }
        }
        
        // 更新预览状态
        if (this.previewStatus) {
            this.previewStatus.querySelector('span').textContent = '已停止';
            this.previewStatus.querySelector('.prediction-confidence').textContent = '';
        }
    }
    
    /**
     * 导出模型
     */
    exportModel() {
        // 检查模型是否已训练
        if (!this.modelTrained) {
            Notification.error('请先训练模型');
            return;
        }

        try {
            //////console.log('开始导出模型...');
            
            // 准备模型数据
            const modelData = this.exportModelData();
            
            //////console.log('准备导出的模型数据:', modelData);
            
            // 直接创建下载链接并下载
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(modelData));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "model.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            //////console.log('导出模型完成');
            Notification.success('模型已成功导出');
        } catch (error) {
            console.error('导出模型失败:', error);
            Notification.error('导出模型失败: ' + error.message);
        }
    }

    // 添加开始捕获摄像头画面方法
    async startCapture() {
        try {
            // 获取视频元素
        this.video = document.createElement('video');
        this.video.setAttribute('autoplay', '');
        this.video.setAttribute('playsinline', '');
            
            // 获取摄像头权限
            const constraints = {
                video: true,
                audio: false
            };
            
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.video.srcObject = stream;
            
            // 等待视频元素加载
            await new Promise(resolve => {
                this.video.onloadedmetadata = () => {
                    resolve();
                };
            });
            
            // 开始播放视频
            await this.video.play();
                        this.videoPlaying = true;
            
            // 开始动画循环
            this.timer = requestAnimationFrame(this.animate.bind(this));
            
        } catch (error) {
            console.error('启动摄像头失败:', error);
            
            // 显示错误消息
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4d4f;
                color: white;
                padding: 12px 24px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 10000;
            `;
            message.textContent = '无法访问摄像头';
            document.body.appendChild(message);
            setTimeout(() => message.remove(), 3000);
        }
    }

    // 添加停止捕获摄像头画面方法
    stopCapture () {
        if (this.video && this.video.readyState >= 2) {
            this.video.pause();
        }
        if (this.timer) {
            cancelAnimationFrame(this.timer);
            this.timer = null;
        }
    }

    // 动画循环函数，用于实时处理摄像头画面
    async animate () {
        if (this.videoPlaying) {
            try {
                const tf = require('@tensorflow/tfjs');
                
                // 确保视频元素准备就绪
                if (!this.video.videoWidth) {
                    this.timer = requestAnimationFrame(this.animate.bind(this));
                    return;
                }

                // 获取视频帧
                const image = tf.browser.fromPixels(this.video);
                
                let logits;
                // 使用 MobileNet 提取特征 - 使用更小的特征层
                const infer = () => this.mobilenet.infer(image, 'conv_pw_13_relu');

                // 如果正在训练，添加样本
                if (this.training !== -1) {
                    logits = infer();
                    this.knn.addExample(logits, this.training);
                    
                    // 保存当前帧作为预览图片
                    const canvas = document.createElement('canvas');
                    canvas.width = this.video.videoWidth;
                    canvas.height = this.video.videoHeight;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(this.video, 0, 0);
                    const imageUrl = canvas.toDataURL('image/jpeg');

                    // 获取当前类别的预览区域
                    const classNode = this.canvas.querySelectorAll('.class-node')[this.training];
                    if (classNode) {
                        // 保存当前的类别索引，因为this.training可能会在后续操作中改变
                        const currentClassIndex = this.training;
                        ////console.log(`[样本添加] 添加样本到类别索引: ${currentClassIndex}`);
                        
                        // 创建预览元素（用于扩展视图）
                        const previewElement = document.createElement('div');
                        previewElement.style.cssText = `
                            width: 100%;
                                aspect-ratio: 1;
                            border-radius: 4px;
                                background-image: url(${imageUrl});
                                background-size: cover;
                                background-position: center;
                                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                            position: relative;
                        `;
                        previewElement.setAttribute('data-url', imageUrl);
                        previewElement.setAttribute('data-class-index', currentClassIndex);
                        
                        // 添加删除按钮（隐藏，鼠标悬停时显示）
                        const deleteButton = document.createElement('button');
                        deleteButton.className = 'debug-delete-button';
                        deleteButton.setAttribute('data-source', 'imageMode-expanded');
                        deleteButton.setAttribute('data-class-index', currentClassIndex);
                        deleteButton.style.cssText = `
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 16px;
                            height: 16px;
                            background: rgba(0, 0, 0, 0.5);
                            color: white;
                            border: none;
                            border-radius: 0 4px 0 4px;
                            font-size: 10px;
                            line-height: 1;
                            padding: 0;
                            display: none;
                            cursor: pointer;
                        `;
                        deleteButton.innerHTML = '×';
                        
                        // 添加删除按钮点击事件
                        deleteButton.onclick = (e) => {
                            ////console.log(`======== 预览区域删除按钮被点击 (imageMode.js) ========`);
                            ////console.log(`删除按钮点击 - 存储的类别索引: ${currentClassIndex}`);
                            e.stopPropagation();
                            
                            // 使用新的删除函数
                            this.deleteSampleAndUpdateCount(previewElement, currentClassIndex);
                        };
                        
                        // 添加鼠标悬停效果
                        previewElement.onmouseover = () => {
                            deleteButton.style.display = 'block';
                        };
                        previewElement.onmouseout = () => {
                            deleteButton.style.display = 'none';
                        };
                        
                        previewElement.appendChild(deleteButton);

                        // 添加到展开状态的预览区域
                        const expandedPreviewArea = classNode.querySelector('.preview-area');
                        if (expandedPreviewArea) {
                            const expandedPreviewElement = previewElement.cloneNode(true);
                            expandedPreviewElement.querySelector('button').onclick = (e) => {
                                e.stopPropagation();
                                expandedPreviewElement.remove();
                                
                                // 更新样本数量
                                const classNodeData = this.nodeData.classNodes.find(n => n.index === this.training);
                                if (classNodeData && classNodeData.instance && typeof classNodeData.instance.updateSampleCount === 'function') {
                                    classNodeData.instance.updateSampleCount();
                                }
                            };
                            expandedPreviewArea.appendChild(expandedPreviewElement);
                            
                            // 确保预览区域可见
                            expandedPreviewArea.style.display = 'grid';
                        }

                        // 添加到初始状态的预览容器
                        const initialPreviewContainer = classNode.querySelector('.preview-container');
                        if (initialPreviewContainer) {
                            const previewClone = document.createElement('div');
                            previewClone.style.cssText = `
                                width: 48px;
                                height: 48px;
                                min-width: 48px;
                                flex-shrink: 0;
                                border-radius: 4px;
                                background-image: url(${imageUrl});
                                background-size: cover;
                                background-position: center;
                                box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                position: relative;
                                display: inline-block;
                                margin: 2px;
                            `;
                            previewClone.setAttribute('data-url', imageUrl);
                            previewClone.setAttribute('data-class-index', currentClassIndex);
                            
                            // 添加删除按钮（隐藏，鼠标悬停时显示）
                            const deleteButton = document.createElement('button');
                            deleteButton.className = 'debug-delete-button';
                            deleteButton.setAttribute('data-source', 'imageMode-initial');
                            deleteButton.setAttribute('data-class-index', currentClassIndex);
                            deleteButton.style.cssText = `
                                position: absolute;
                                top: 0;
                                right: 0;
                                width: 16px;
                                height: 16px;
                                background: rgba(0, 0, 0, 0.5);
                                color: white;
                                border: none;
                                border-radius: 0 4px 0 4px;
                                font-size: 10px;
                                line-height: 1;
                                padding: 0;
                                display: none;
                                cursor: pointer;
                            `;
                            deleteButton.innerHTML = '×';
                            
                            // 添加删除按钮点击事件
                            deleteButton.onclick = (e) => {
                                ////console.log(`======== 初始预览区域删除按钮被点击 (imageMode.js) ========`);
                                ////console.log(`删除按钮点击 - 存储的类别索引: ${currentClassIndex}`);
                                e.stopPropagation();
                                
                                // 使用新的删除函数
                                this.deleteSampleAndUpdateCount(previewClone, currentClassIndex);
                            };
                            
                            // 添加鼠标悬停效果
                            previewClone.onmouseover = () => {
                                deleteButton.style.display = 'block';
                            };
                            previewClone.onmouseout = () => {
                                deleteButton.style.display = 'none';
                            };
                            
                            previewClone.appendChild(deleteButton);
                            initialPreviewContainer.appendChild(previewClone);
                            
                            // 确保预览容器可见
                            initialPreviewContainer.style.display = 'flex';
                        }
                        
                        // 更新样本数量
                        const classNodeData = this.nodeData.classNodes.find(n => n.index === this.training);
                        if (classNodeData && classNodeData.instance && typeof classNodeData.instance.updateSampleCount === 'function') {
                            classNodeData.instance.updateSampleCount();
                        }
                    }
                }

                // 如果有训练的类别，进行预测
                const numClasses = this.knn.getNumClasses();
                if (numClasses > 0) {
                    logits = infer();
                    const res = await this.knn.predictClass(logits, this.TOPK);

                    // 更新每个类别的信息
                    const classNodes = this.canvas.querySelectorAll('.class-node');
                    for (let i = 0; i < classNodes.length; i++) {
                        const node = classNodes[i];
                        const exampleCount = this.knn.getClassExampleCount();
                        
                        // 更新样本数量显示
                        const count = exampleCount[i] || 0;
                        
                        // 更新初始状态的样本数量
                        const initialSampleCount = node.querySelector('[data-content="initial"] div:first-child');
                        if (initialSampleCount) {
                            // initialSampleCount.textContent = `${count} 个图片样本`;
                        }

                        // 更新展开状态的样本数量
                        const expandedSampleCount = node.querySelector('[data-content="expanded"] div:first-child > div:nth-child(2)');
                        if (expandedSampleCount) {
                            expandedSampleCount.textContent = `${count} 个图片样本`;
                        }

                        // 更新infoTexts数组中的样本数量
                        if (this.infoTexts[i]) {
                            this.infoTexts[i].textContent = count;
                        }

                        // 更新置信度显示
                        if (node.confidenceProgress && node.confidenceText) {
                            const confidence = res.confidences[i] * 100;
                            node.confidenceProgress.style.width = `${confidence}%`;
                            node.confidenceText.textContent = `${confidence.toFixed(1)}%`;
                        }
                    }
                }

                // 释放内存
                image.dispose();
                if (logits) {
                    logits.dispose();
                }
            } catch (error) {
                console.error('Error during animation:', error);
            }
            
            // 下一帧动画
        this.timer = requestAnimationFrame(this.animate.bind(this));
        }
    }

    // 你想要每秒执行的操作
    performActionEverySecond () {
        
        // 在这里添加你想要执行的代码
    }

    // 修改关闭方法，确保清理资源
    close () {
        if (!this.visible) return;
        
        // 停止所有活动
        this.stopCapture();
        
        // 清理摄像头流
        this.cleanupWebcamStreams();
        
        // 移除窗口缩放事件监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
            this.resizeHandler = null;
        }
        
        // 清理资源
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
        if (this.overlay && this.overlay.parentNode) {
            this.overlay.parentNode.removeChild(this.overlay);
        }
        

        // 停止视频播放
        if (this.previewContainer) {
            const video = this.previewContainer.querySelector('video');
            if (video && video.srcObject) {
                const stream = video.srcObject;
                const tracks = stream.getTracks();
                tracks.forEach(track => track.stop());
            }
        }

        this.videoPlaying = false;
    }

    getCenterPosition (elementHeight) {
        const windowHeight = window.innerHeight;
        return (windowHeight - elementHeight) / 2;
    }

    // 添加相关方法
    deleteClass = (classIndex) => {
        //////console.log('尝试删除类别:', classIndex);
        
        if (!this.nodeData || !this.nodeData.classNodes) {
            console.error('节点数据未初始化');
            return;
        }
        
        // 在真正删除前检查类别在KNN模型中是否存在
        if (this.knn) {
            try {
                const exampleCount = this.knn.getClassExampleCount();
                if (!exampleCount[classIndex] || exampleCount[classIndex] === 0) {
                    //////console.log(`类别 ${classIndex} 在KNN模型中不存在或没有样本，跳过KNN清除操作`);
                } else {
                    // 只有当类别存在且有样本时才尝试清除
                    //////console.log(`清除KNN模型中的类别 ${classIndex}`);
                    this.knn.clearClass(classIndex);
                }
            } catch (error) {
                console.error(`清除KNN类别 ${classIndex} 时出错:`, error);
                // 继续执行后续操作，不让KNN错误阻止UI更新
            }
        }
        
        // 找到要删除的节点
        const nodeToDelete = this.nodeData.classNodes.find(node => node.index === classIndex);
        if (!nodeToDelete) {
            console.error(`找不到索引为 ${classIndex} 的类别节点`);
            return;
        }
        
        // 删除DOM节点
        if (nodeToDelete.node && nodeToDelete.node.parentElement) {
            nodeToDelete.node.parentElement.removeChild(nodeToDelete.node);
        }
        
        // 从数组中删除
        this.nodeData.classNodes = this.nodeData.classNodes.filter(node => node.index !== classIndex);
        
        // 清除该类别的训练数据
        if (this.images && this.images[classIndex]) {
            delete this.images[classIndex];
        }
        
        if (this.classInfo && this.classInfo[classIndex]) {
            delete this.classInfo[classIndex];
        }
        
        // 检查是否删除了所有类别
        if (this.nodeData.classNodes.length === 0) {
            //////console.log('所有类别都已删除，重置训练状态');
            // 重置训练状态
            this.modelTrained = false;
            
            // 重新初始化KNN分类器
            if (this.knn) {
                try {
                    //////console.log('重新初始化KNN分类器');
                    this.knn.dispose();
                    const tf = require('@tensorflow/tfjs');
                    this.knn = new this.KNNClassifier();
                } catch (error) {
                    console.error('重新初始化KNN分类器失败:', error);
                }
            }
            
            // 清空训练数据
            this.images = {};
            this.classInfo = {};
        }
        
        // 重新生成类别信息
        this.generateClassInfo();
        
        // 更新连接线
        this.updateConnectionsAfterDOMChange();
        
        // 更新训练按钮状态 - 确保在所有操作后再次检查
        setTimeout(() => {
            this.checkSamplesForTraining();
        }, 100);
    }

    disableClass = (classIndex) => {
        const classNode = this.nodeData.classNodes.find(n => n.index === classIndex);
        if (classNode) {
            const node = classNode.node;
            const isDisabled = node.getAttribute('data-disabled') === 'true';
            
            if (isDisabled) {
                // 启用类别
                node.removeAttribute('data-disabled');
                node.style.opacity = '1';
                node.style.filter = 'none';
                node.style.pointerEvents = 'auto';
                
                // 更新菜单项文本
                const menuItems = node.querySelectorAll('[role="menuitem"]');
                menuItems.forEach(item => {
                    if (item.textContent === '启用类别') {
                        item.textContent = '停用类别';
                    }
                });
            } else {
                // 停用类别
                node.setAttribute('data-disabled', 'true');
                node.style.opacity = '0.5';
                node.style.filter = 'grayscale(100%)';
                node.style.pointerEvents = 'none';
                
                // 保持菜单按钮和下拉菜单可点击
                const menuButton = node.querySelector('button');
                const menuDropdown = node.querySelector('div[style*="position: absolute"]');
                
                if (menuButton) {
                    menuButton.style.pointerEvents = 'auto';
                    menuButton.style.opacity = '1';
                    menuButton.style.filter = 'none';
                }
                
                if (menuDropdown) {
                    menuDropdown.style.pointerEvents = 'auto';
                    menuDropdown.style.opacity = '1';
                    menuDropdown.style.filter = 'none';
                }
                
                // 更新菜单项文本
                const menuItems = node.querySelectorAll('[role="menuitem"]');
                menuItems.forEach(item => {
                    if (item.textContent === '停用类别') {
                        item.textContent = '启用类别';
                    }
                });
            }

            // 更新训练状态
            if (this.knn) {
                try {
                    const exampleCount = this.knn.getClassExampleCount();
                    if (exampleCount[classIndex] > 0) {
                        if (isDisabled) {
                            // 恢复训练数据
                            if (this.disabledClassData && this.disabledClassData[classIndex]) {
                                this.knn.addExample(this.disabledClassData[classIndex], classIndex);
                                delete this.disabledClassData[classIndex];
                            }
                        } else {
                            // 保存并移除训练数据
                            if (!this.disabledClassData) {
                                this.disabledClassData = {};
                            }
                            this.disabledClassData[classIndex] = this.knn.getClassExampleCount()[classIndex];
                            this.knn.clearClass(classIndex);
                        }
                    }
                } catch (error) {
                    console.error('更新训练数据状态失败:', error);
                }
            }

            // 更新连接线显示
            this.updateConnectionsAfterDOMChange();
        }
    };

    /**
     * 移除指定类别的所有样本
     * @param {number} classIndex - 类别索引
     */
    removeAllSamples = (classIndex) => {
        // 首先检查是否有样本
        let hasSamples = false;
        
        // 检查DOM中的样本
        const classNode = this.nodeData.classNodes.find(n => n.index === classIndex);
        let sampleCount = 0;
        
        if (classNode && classNode.node) {
            const previewContainer = classNode.node.querySelector('.preview-container');
            if (previewContainer) {
                // 计算实际样本预览元素的数量
                const previewItems = Array.from(previewContainer.children).filter(
                    el => el.tagName === 'DIV' && el.style.backgroundImage
                );
                sampleCount = previewItems.length;
            }
        }
        
        // 检查KNN分类器中的样本数量
        if (this.knn) {
            const exampleCount = this.knn.getClassExampleCount();
            if (exampleCount && exampleCount[classIndex] && exampleCount[classIndex] > 0) {
                hasSamples = true;
                sampleCount = Math.max(sampleCount, exampleCount[classIndex]);
            }
        }
        
        // 如果内存中有样本数据，也计入
        if (this.images && this.images[classIndex] && this.images[classIndex].length > 0) {
            hasSamples = true;
            sampleCount = Math.max(sampleCount, this.images[classIndex].length);
        }
        
        // 如果没有样本，显示提示并返回
        if (!hasSamples && sampleCount === 0) {
            // 获取类别名称
            let className = `类别 ${classIndex + 1}`;
            if (classNode && classNode.node) {
                const titleInput = classNode.node.querySelector('input[type="text"]');
                if (titleInput && titleInput.value) {
                    className = titleInput.value;
                }
            }
            
                notification.show(`${className} 没有样本可移除`, 'warning');
            
            return;
        }
        
        // 清空样本数据
        if (this.images && this.images[classIndex]) {
                this.images[classIndex] = [];
            }
            
            // 清空预览容器
            if (classNode && classNode.instance) {
                // 清空初始状态的预览容器
                const initialContent = classNode.node.querySelector('[data-content="initial"]');
                if (initialContent) {
                    const previewContainer = initialContent.querySelector('.preview-container');
                    if (previewContainer) {
                        while (previewContainer.firstChild) {
                            previewContainer.removeChild(previewContainer.firstChild);
                        }
                    }
                }
                
            // 清空展开状态的预览区域
                const expandedContent = classNode.node.querySelector('[data-content="expanded"]');
                if (expandedContent) {
                const previewArea = expandedContent.querySelector('.preview-area');
                if (previewArea) {
                    while (previewArea.firstChild) {
                        previewArea.removeChild(previewArea.firstChild);
                    }
                }
            }
            
            // 更新样本数量
                if (classNode.instance.updateSampleCount) {
                    classNode.instance.updateSampleCount();
                }
            }
            
        // 清空该类别的训练数据
        if (this.knn) {
            // 检查该类别是否有样本数据，避免尝试清除无效类别
            const exampleCount = this.knn.getClassExampleCount();
            if (exampleCount && exampleCount[classIndex] && exampleCount[classIndex] > 0) {
                try {
                    this.knn.clearClass(classIndex);
                } catch (error) {
                    console.warn(`无法清除类别 ${classIndex} 的样本: ${error.message}`);
                    // 错误已处理，不影响后续操作
                }
            }
        }
        
        // 显示成功移除样本的提示
        if (sampleCount > 0) {
            let className = `类别 ${classIndex + 1}`;
            if (classNode && classNode.node) {
                const titleInput = classNode.node.querySelector('input[type="text"]');
                if (titleInput && titleInput.value) {
                    className = titleInput.value;
                }
            }
            
            notification.show(`已移除 ${className} 的所有样本 (${sampleCount} 个)`, 'success');
        }
            
            // 检查训练按钮状态
                this.checkSamplesForTraining();

        // 更新连接线
        this.updateConnections();
    }

    downloadSamples = (classIndex) => {
        const classNode = this.nodeData.classNodes.find(n => n.index === classIndex);
        if (classNode) {
            // 获取所有预览图片的URL
            const previewArea = classNode.node.querySelector('[data-content="expanded"] .preview-area');
            if (!previewArea) return;

            const imageElements = previewArea.querySelectorAll('div[data-url]');
            const imageUrls = Array.from(imageElements).map(el => el.getAttribute('data-url'));

            if (imageUrls.length === 0) {
                Notification.error('没有可下载的样本');
                return;
            }

            // 创建ZIP文件
            const zip = new JSZip();
            const promises = imageUrls.map((url, i) => {
                return fetch(url)
                    .then(response => response.blob())
                    .then(blob => {
                        const extension = url.startsWith('data:image/png') ? 'png' : 'jpg';
                        zip.file(`sample_${i + 1}.${extension}`, blob);
                    });
            });

            // 下载ZIP文件
            Promise.all(promises).then(() => {
                zip.generateAsync({ type: 'blob' }).then(content => {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(content);
                    link.download = `class_${classIndex + 1}_samples.zip`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                });
            });
        }
    };

    // 导入训练好的模型
    importModel() {
        // 调用独立的importModel方法，并绑定this上下文
        try {
            return importModel.call(this);
        } catch (error) {
            console.error('导入模型失败:', error);
            Notification.error('导入模型失败: ' + error.message);
        }
    }

    /**
     * 直接导入模型数据（用于从积木块上传模型后调用）
     * @param {string} modelData - 模型数据字符串（JSON格式）
     * @param {boolean} [skipAnimation] - 是否跳过初始化动画，默认为false
     */
    importModelData(modelData, skipAnimation = false) {
        // 添加调试日志
        ////console.log(`imageMode.importModelData 被调用，skipAnimation=${skipAnimation}`);
        
        // 调用独立的importModelData方法，并绑定this上下文
        return importModelData.call(this, modelData, skipAnimation);
    }

    // 添加一个新方法，用于在DOM变化后更新连接线
    /**
     * 在DOM变化后更新连接线
     * 这个方法会在多个时间点更新连接线，确保在DOM完全渲染和动画效果完成后连接线位置正确
     * 使用场景：
     * 1. 添加/删除类别节点
     * 2. 展开/折叠类别节点
     * 3. 窗口大小调整
     * 4. 移除样本
     * 5. 启用/禁用类别
     */
    updateConnectionsAfterDOMChange() {
        // 立即更新一次连接线
        this.updateConnections();
        
        // 100ms后再次更新，确保DOM已完全渲染
        setTimeout(() => {
            this.updateConnections();
        }, 100);
        
        // 500ms后再次更新，确保所有动画和过渡效果完成后连接线位置正确
        setTimeout(() => {
            this.updateConnections();
        }, 500);
    }

    /**
     * 检查是否有足够的样本用于训练
     * @returns {boolean} 是否有足够的样本
     */
    checkSamplesForTraining() {
        let hasEnoughSamples = false;
        
        try {
            // 检查是否至少有两个类别
            if (!this.nodeData || !this.nodeData.classNodes) {
                console.error('节点数据未初始化');
                return false;
            }
            
            //////console.log(`当前类别节点数量: ${this.nodeData.classNodes.length}`);
            
            // 获取类别数量和每个类别的样本数
            let classesWithSamples = 0;
            let classIndicesWithSamples = [];
            let samplesPerClass = {};
            
            // 首先直接检查DOM中的样本预览容器
            //////console.log('正在检查DOM中的样本预览...');
            this.nodeData.classNodes.forEach(classNode => {
                const previewContainer = classNode.node.querySelector('.preview-container');
                if (previewContainer) {
                    // 计算样本预览元素的数量（不包括按钮等其他元素）
                    const previewItems = Array.from(previewContainer.children).filter(
                        el => el.tagName === 'DIV' && el.style.backgroundImage
                    );
                    
                    // 记录每个类别的样本数，无论是否达到10个
                    samplesPerClass[classNode.index] = previewItems.length;
                    
                    if (previewItems.length >= 10) { // 至少10个样本
                        classesWithSamples++;
                        classIndicesWithSamples.push(classNode.index);
                        //////console.log(`类别 ${classNode.index} 在DOM中有 ${previewItems.length} 个样本`);
                    }
                }
            });
            
            // 然后检查images对象
            if (this.images) {
                //////console.log('检查images对象...');
                Object.keys(this.images).forEach(classIndex => {
                    // 只考虑实际存在的类别节点的索引
                    const classNodeExists = this.nodeData.classNodes.some(node => 
                        node.index.toString() === classIndex.toString()
                    );
                    
                    if (!classNodeExists) {
                        //////console.log(`类别索引 ${classIndex} 在images中存在，但在节点中不存在，忽略`);
                        return;
                    }
                    
                    // 记录样本数量（如果尚未记录）
                    if (!samplesPerClass[classIndex] && this.images[classIndex]) {
                        samplesPerClass[classIndex] = this.images[classIndex].length;
                    }
                    
                    const hasSamples = this.images[classIndex] && this.images[classIndex].length >= 10;
                    if (hasSamples) {
                        // 如果还没计入，则增加计数
                        if (!classIndicesWithSamples.includes(parseInt(classIndex)) &&
                            !classIndicesWithSamples.includes(classIndex)) {
                            classesWithSamples++;
                            classIndicesWithSamples.push(parseInt(classIndex));
                        }
                    }
                });
            }
            
            // 最后尝试使用KNN计数
            if (this.knn) {
                //////console.log('尝试使用KNN计数...');
                const exampleCount = this.knn.getClassExampleCount();
                //////console.log('KNN样本计数:', exampleCount);
                
                for (const classIndex in exampleCount) {
                    // 只考虑实际存在的类别节点的索引
                    const classNodeExists = this.nodeData.classNodes.some(node => 
                        node.index.toString() === classIndex.toString()
                    );
                    
                    if (!classNodeExists) {
                        //////console.log(`类别索引 ${classIndex} 在KNN中存在，但在节点中不存在，忽略`);
                        continue;
                    }
                    
                    // 记录样本数量（如果尚未记录）
                    if (!samplesPerClass[classIndex]) {
                        samplesPerClass[classIndex] = exampleCount[classIndex];
                    }
                    
                    if (exampleCount[classIndex] >= 10) {
                        // 如果还没计入，则增加计数
                        if (!classIndicesWithSamples.includes(parseInt(classIndex)) &&
                            !classIndicesWithSamples.includes(classIndex)) {
                            classesWithSamples++;
                            classIndicesWithSamples.push(parseInt(classIndex));
                        }
                    }
                }
            }
            
            //////console.log(`有样本的类别数量: ${classesWithSamples}, 类别索引:`, classIndicesWithSamples);
            //////console.log('每个类别的样本数量:', samplesPerClass);
            
            // 检查是否至少有两个类别
            if (classesWithSamples < 2) {
                //////console.log('没有足够的类别（少于2个）');
                hasEnoughSamples = false;
            } else {
                // 检查是否所有类别都有足够的样本
                const allClassesHaveSamples = this.nodeData.classNodes.every(classNode => {
                    const index = classNode.index;
                    const hasSufficientSamples = (samplesPerClass[index] || 0) >= 10;
                    
                    // 如果某个类别样本不足，记录日志
                    if (!hasSufficientSamples) {
                        //////console.log(`类别 ${index} 样本不足: ${samplesPerClass[index] || 0} < 10`);
                    }
                    
                    return hasSufficientSamples;
                });
                
                // 只有当所有类别都有足够样本时，才允许训练
                hasEnoughSamples = allClassesHaveSamples;
                
                //////console.log(`所有类别都有足够样本: ${allClassesHaveSamples}`);
            }
            
            //////console.log(`训练条件满足: ${hasEnoughSamples ? '是' : '否'}`);
            
            // 强制更新训练按钮状态
            if (this.nodeData.trainNodeInstance) {
                if (typeof this.nodeData.trainNodeInstance.updateTrainButtonState === 'function') {
                    //////console.log(`更新训练按钮状态: ${hasEnoughSamples ? '启用' : '禁用'}`);
                    
                    // 添加直接DOM更新，以防止可能的状态不同步问题
                    const trainButton = this.nodeData.trainNode.querySelector('button');
                    if (trainButton) {
                        trainButton.disabled = !hasEnoughSamples;
                        trainButton.style.opacity = hasEnoughSamples ? '1' : '0.5';
                        trainButton.style.cursor = hasEnoughSamples ? 'pointer' : 'not-allowed';
                    }
                    
                    // 通过正常方法更新
                    this.nodeData.trainNodeInstance.updateTrainButtonState(hasEnoughSamples);
                } else {
                    console.warn('训练节点实例未提供updateTrainButtonState方法');
                }
            } else {
                console.warn('未找到训练节点实例');
            }
            
        } catch (error) {
            console.error('检查训练样本时出错:', error);
            hasEnoughSamples = false;
        }
        
        return hasEnoughSamples;
    }

    /**
     * 设置摄像头预览
     * @param {HTMLElement} container - 摄像头预览容器
     */
    async setupWebcam(container) {
        try {
            // 清空容器
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }
            
            // 创建视频元素
            const video = document.createElement('video');
            video.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 8px;
            `;
            video.autoplay = true;
            video.muted = true;
            video.playsInline = true;
            
            // 获取摄像头权限
            const constraints = {
                video: true,
                audio: false
            };
            
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            video.srcObject = stream;
            
            // 将视频元素添加到容器
            container.appendChild(video);
            
            // 保存视频流引用，以便后续清理
            if (!this.webcamStreams) {
                this.webcamStreams = [];
            }
            this.webcamStreams.push(stream);
            
            // 等待视频元素加载
            await new Promise(resolve => {
                video.onloadedmetadata = () => {
                    resolve();
                };
            });
            
            // 开始播放视频
            await video.play();
            
            return video;
        } catch (error) {
            console.error('启动摄像头失败:', error);
            Notification.error('无法访问摄像头');
            return null;
        }
    }

    /**
     * 清理所有摄像头流
     */
    cleanupWebcamStreams() {
        if (this.webcamStreams && this.webcamStreams.length > 0) {
            this.webcamStreams.forEach(stream => {
                if (stream) {
                    const tracks = stream.getTracks();
                    tracks.forEach(track => track.stop());
                }
            });
            this.webcamStreams = [];
        }
    }

    // 在TrainNode完成训练后调用此方法生成类别信息
    generateClassInfo() {
        // 清空现有classInfo
        this.classInfo = {};
        
        // 尝试从类别节点获取名称
        if (this.nodeData && this.nodeData.classNodes && this.nodeData.classNodes.length > 0) {
            try {
                this.nodeData.classNodes.forEach(node => {
                    // 尝试获取类别节点的标题输入框值
                    const titleInput = node.node.querySelector('input[type="text"]');
                    const className = titleInput ? titleInput.value : `类别 ${node.index + 1}`;
                    
                    // 获取样本数量
                    let sampleCount = 0;
                    if (this.knn) {
                        const exampleCount = this.knn.getClassExampleCount();
                        sampleCount = exampleCount[node.index] || 0;
                    }
                    
                    // 存储类别信息
                    this.classInfo[node.index] = {
                        name: className,
                        index: node.index,
                        sampleCount: sampleCount
                    };
                });
                
                // 同时更新modelData
                if (!this.modelData) {
                    this.modelData = {};
                }
                
                this.modelData.labels = Object.values(this.classInfo).map(info => info.name);
                
                // 通知预览节点更新类别
                if (this.nodeData.previewNode) {
                    try {
                        // 使用新的updatePreviewCategories方法
                        if (typeof this.nodeData.previewNode.updatePreviewCategories === 'function') {
                            this.nodeData.previewNode.updatePreviewCategories();
                            
                            // 如果预览已经打开，则重新启动预览以应用新类别
                            if (this.nodeData.previewNode.isPreviewOn) {
                                this.nodeData.previewNode.stopPreview();
                                setTimeout(() => {
                                    this.nodeData.previewNode.startPreview();
                                }, 500);
                            }
                        } else {
                            // 如果updatePreviewCategories方法不存在，尝试重新创建预览节点
                            const oldNode = this.nodeData.previewNode.node;
                            const parentNode = oldNode.parentNode;
                            
                            if (parentNode) {
                                const position = this.nodeData.previewNode.position;
                                const previewNode = new this.PreviewNode({
                                    position,
                                    parentContext: this
                                });
                                parentNode.replaceChild(previewNode.node, oldNode);
                                this.nodeData.previewNode = previewNode;
                            }
                        }
                    } catch (error) {
                        console.error('更新预览节点类别失败:', error);
                    }
                }
            } catch (error) {
                console.error('生成类别信息出错:', error);
            }
        }
        
        return this.classInfo;
    }

    /**
     * 删除样本预览元素并更新样本计数
     * @param {HTMLElement} previewElement - 要删除的预览元素
     * @param {number} classIndex - 类别索引
     */
    deleteSampleAndUpdateCount(previewElement, classIndex) {
        ////console.log(`======== 执行样本删除 (imageMode.js) ========`);
        ////console.log(`删除类别 ${classIndex} 的样本`);
        
        // 1. 先删除预览元素
        if (previewElement && previewElement.parentNode) {
            previewElement.remove();
            ////console.log(`样本预览元素已删除`);
        } else {
            console.warn(`样本预览元素不存在或没有父节点`);
        }
        
        // 2. 直接查找对应类别节点的DOM元素
        const classNodeElement = this.canvas.querySelectorAll('.class-node')[classIndex];
        if (!classNodeElement) {
            console.error(`找不到类别 ${classIndex} 的DOM元素`);
            return;
        }
        
        // 3. 查找正确的classNodeData
        ////console.log(`开始查找类别节点数据, 类别索引: ${classIndex}`);
        const classNodeData = this.nodeData.classNodes.find(n => n.index === classIndex);
        
        if (!classNodeData) {
            console.error(`在nodeData中找不到类别 ${classIndex} 的数据`);
            ////console.log(`现有类别索引:`, this.nodeData.classNodes.map(n => n.index));
            return;
        }
        
        // 4. 检查instance是否存在
        if (!classNodeData.instance) {
            console.error(`类别 ${classIndex} 没有instance属性`);
            ////console.log(`classNodeData:`, classNodeData);
            
            // 尝试直接更新DOM中的样本计数
            try {
                // 获取初始视图的样本数量
                const initialPreviewContainer = classNodeElement.querySelector('.preview-container');
                if (initialPreviewContainer) {
                    const previewItems = Array.from(initialPreviewContainer.children).filter(
                        el => el.tagName === 'DIV' && el.style.backgroundImage
                    );
                    const sampleCount = previewItems.length;
                    
                    // 更新初始视图的样本标题
                    const sampleCountTitle = classNodeElement.querySelector('.sample-count-title');
                    if (sampleCountTitle) {
                        sampleCountTitle.textContent = `${sampleCount} 个图片样本`;
                        ////console.log(`手动更新了初始视图的样本计数: ${sampleCount}`);
                    }
                    
                    // 更新展开视图的样本标题
                    const expandedSampleTitle = classNodeElement.querySelector('.expanded-sample-count-title');
                    if (expandedSampleTitle) {
                        expandedSampleTitle.textContent = `${sampleCount} 个图片样本`;
                        ////console.log(`手动更新了展开视图的样本计数: ${sampleCount}`);
                    }
                }
            } catch (err) {
                console.error(`手动更新样本计数失败:`, err);
            }
            return;
        }
        
        // 5. 调用updateSampleCount方法
        ////console.log(`classNodeData.instance:`, classNodeData.instance);
        const updateSampleCountMethod = classNodeData.instance.updateSampleCount;
        
        if (typeof updateSampleCountMethod === 'function') {
            ////console.log(`正在调用updateSampleCount方法...`);
            try {
                updateSampleCountMethod.call(classNodeData.instance);
                ////console.log(`样本数量更新成功`);
            } catch (err) {
                console.error(`调用updateSampleCount方法时出错:`, err);
            }
        } else {
            console.error(`updateSampleCount不是一个函数`);
            ////console.log(`classNodeData.instance上可用的方法:`, Object.keys(classNodeData.instance));
            
            // 尝试直接更新DOM中的样本计数
            try {
                // 获取初始视图的样本数量
                const initialPreviewContainer = classNodeElement.querySelector('.preview-container');
                if (initialPreviewContainer) {
                    const previewItems = Array.from(initialPreviewContainer.children).filter(
                        el => el.tagName === 'DIV' && el.style.backgroundImage
                    );
                    const sampleCount = previewItems.length;
                    
                    // 更新初始视图的样本标题
                    const sampleCountTitle = classNodeElement.querySelector('.sample-count-title');
                    if (sampleCountTitle) {
                        sampleCountTitle.textContent = `${sampleCount} 个图片样本`;
                        ////console.log(`手动更新了初始视图的样本计数: ${sampleCount}`);
                    }
                    
                    // 更新展开视图的样本标题
                    const expandedSampleTitle = classNodeElement.querySelector('.expanded-sample-count-title');
                    if (expandedSampleTitle) {
                        expandedSampleTitle.textContent = `${sampleCount} 个图片样本`;
                        ////console.log(`手动更新了展开视图的样本计数: ${sampleCount}`);
                    }
                }
            } catch (err) {
                console.error(`手动更新样本计数失败:`, err);
            }
        }
        
        ////console.log(`======== 删除操作完成 ========`);
    }
}

// eslint-disable-next-line import/no-commonjs
module.exports = ImageMode;
