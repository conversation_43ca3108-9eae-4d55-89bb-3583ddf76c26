import React, { useState, useEffect, useRef, useCallback } from 'react';
import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
const tf = require('@tensorflow/tfjs');

// 添加十个固定的颜色
const classColors = [
    '#FF6B6B', // 红色
    '#4ECDC4', // 青绿色
    '#FFD166', // 黄色
    '#6A0572', // 紫色
    '#1A936F', // 绿色
    '#3D82AB', // 蓝色
    '#F77F00', // 橙色
    '#9D70CB', // 淡紫色
    '#5D576B', // 深紫色
    '#E63946'  // 深红色
];

const ImageIdentifyWindows = ({modelData}) => {
    // 状态管理
    const [confidences, setConfidences] = useState([]);
    const [predicting, setPredicting] = useState(false);
    const videoRef = useRef(null);
    const predictionIntervalRef = useRef(null);
    const mobilenetRef = useRef(null);
    const knnRef = useRef(null);
    const [loadingMessage, setLoadingMessage] = useState('');

    // 初始化模型
    useEffect(() => {
        const initModel = async () => {
            if (knnRef.current && mobilenetRef.current) {
                return;
            }
            try {
                const tf = require('@tensorflow/tfjs');
                await tf.setBackend('webgl').catch(() => tf.setBackend('cpu'));

                // 解析模型数据
                if (modelData) {
                    const knnClassifier = require('@tensorflow-models/knn-classifier');
                    knnRef.current = knnClassifier.create();

                    // 加载 MobileNet 模型
                    if (!mobilenetRef.current) {
                        setLoadingMessage('正在加载基础模型...');
                        const mobilenetModule = require('@tensorflow-models/mobilenet');
                        const modelUrl = `${window.location.origin}/static/utils/image_train/model.json`;
                        mobilenetRef.current = await mobilenetModule.load({version: 1, alpha: 0.25, modelUrl: modelUrl});
                    }

                    // 加载 KNN 分类器
                    setLoadingMessage('加载模型数据...');

                    // 如果有模型数据，设置分类器数据集
                    if (modelData) {
                        try {
                            // 解析modelData（如果它是字符串的话）
                            const parsedModelData = typeof modelData === 'string' ? JSON.parse(modelData) : modelData;
                            console.log('解析后的模型数据:', parsedModelData);

                            if (parsedModelData && parsedModelData.dataset) {
                                console.log('发现模型数据，开始加载数据集');
                                const tf = require('@tensorflow/tfjs');
                                const dataset = {};
                                
                                // 处理数据集
                                for (const classId in parsedModelData.dataset) {
                                    if (Object.prototype.hasOwnProperty.call(parsedModelData.dataset, classId)) {
                                        console.log(`处理类别 ${classId} 的数据`);
                                        const classData = parsedModelData.dataset[classId];
                                        
                                        try {
                                            // 处理量化数据
                                            if (classData.quantized) {
                                                console.log(`类别 ${classId} 使用量化数据`);
                                                const { quantizedValues, shape, minVal, maxVal } = classData;
                                                
                                                // 解量化数据
                                                const values = new Float32Array(quantizedValues.length);
                                                const range = maxVal - minVal;
                                                
                                                for (let i = 0; i < quantizedValues.length; i++) {
                                                    values[i] = (quantizedValues[i] / 32767) * range + minVal;
                                                }
                                                
                                                // 创建张量
                                                dataset[classId] = tf.tensor(values, shape);
                                            } else {
                                                console.log(`类别 ${classId} 使用非量化数据`);
                                                // 处理非量化数据
                                                dataset[classId] = tf.tensor(classData.values, classData.shape);
                                            }
                                            console.log(`类别 ${classId} 数据处理完成`);
                                        } catch (error) {
                                            console.error(`处理类别 ${classId} 数据时出错:`, error);
                                        }
                                    }
                                }
                                
                                // 设置分类器数据集
                                console.log('设置分类器数据集:', Object.keys(dataset).length, '个类别');
                                knnRef.current.setClassifierDataset(dataset);
                                
                                // 验证数据集是否正确加载
                                const numClasses = knnRef.current.getNumClasses();
                                console.log('验证：当前加载的类别数量:', numClasses);
                            } else {
                                console.warn('模型数据中没有找到数据集');
                            }
                        } catch (error) {
                            console.error('解析模型数据失败:', error);
                        }
                    } else {
                        console.warn('没有提供模型数据');
                    }
                }

                // 开始预览
                startPreview();
            } catch (error) {
                console.error('模型初始化失败:', error);
            }
        };

        initModel();

        // 清理函数
        return () => {
            stopPreview();
        };
    }, [modelData]);

    // 开始预览
    const startPreview = async () => {
        if (predicting) return;
        
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ video: true });
            const video = document.getElementById('preview-video');
            if (video) {
                video.srcObject = stream;
                video.style.display = 'block';
                await video.play();
                setPredicting(true);
                startPrediction(video);
            }
        } catch (error) {
            console.error('无法访问摄像头:', error);
        }
    };

    // 停止预览
    const stopPreview = () => {
        if (predictionIntervalRef.current) {
            clearInterval(predictionIntervalRef.current);
            predictionIntervalRef.current = null;
        }
        
        const video = document.getElementById('preview-video');
        if (video && video.srcObject) {
            const tracks = video.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            video.srcObject = null;
            video.style.display = 'none';
        }
        
        setConfidences([]);
        setPredicting(false);
    };

    // 开始预测
    const startPrediction = async (video) => {
        if (!mobilenetRef.current || !knnRef.current) return;

        predictionIntervalRef.current = setInterval(async () => {
            if (!video || !video.videoWidth) return;

            try {
                const image = tf.browser.fromPixels(video);
                const logits = mobilenetRef.current.infer(image, 'conv_pw_13_relu');
                
                const numClasses = knnRef.current.getNumClasses();
                console.log('当前类别数量:', numClasses);
                
                if (numClasses > 0) {
                    try {
                        // 使用topK参数10确保返回所有类别的置信度
                        const res = await knnRef.current.predictClass(logits, 10);
                        console.log('预测结果:', res);
                        
                        // 准备预测结果
                        const newConfidences = [];
                        
                        // 从modelData中获取类别名称
                        let classNames = [];
                        try {
                            // 解析modelData（如果它是字符串的话）
                            const parsedModelData = typeof modelData === 'string' ? JSON.parse(modelData) : modelData;
                            
                            if (parsedModelData && parsedModelData.labels) {
                                classNames = parsedModelData.labels;
                                console.log('使用解析后的modelData中的labels:', classNames);
                            } else if (parsedModelData && parsedModelData.classNodes) {
                                classNames = parsedModelData.classNodes.map(node => node.name || `类别 ${node.id + 1}`);
                                console.log('使用解析后的classNodes中的名称:', classNames);
                            } else {
                                // 如果没有标签数据，使用默认的类别名称
                                classNames = Array(numClasses).fill().map((_, i) => `类别 ${i + 1}`);
                                console.log('使用默认类别名称:', classNames);
                            }
                        } catch (error) {
                            console.error('解析modelData失败:', error);
                            // 解析失败时使用默认类别名称
                            classNames = Array(numClasses).fill().map((_, i) => `类别 ${i + 1}`);
                            console.log('解析失败，使用默认类别名称:', classNames);
                        }
                        
                        // 构建每个类别的置信度信息
                        // 确保遍历所有类别，而不仅仅是预测结果中的类别
                        const confidences = res.confidences || {};
                        
                        // 遍历每个类别ID
                        for (let i = 0; i < numClasses; i++) {
                            const classId = String(i); // 将数字转换为字符串，因为KNN分类器的标签是字符串
                            // 获取该类别的置信度，默认为0
                            const confidence = confidences[classId] !== undefined ? confidences[classId] : 0;
                            
                            newConfidences.push({
                                id: i,
                                name: classNames[i] || `类别 ${i + 1}`,
                                confidence: confidence // 使用原始置信度值（0-1范围）
                            });
                        }
                        
                        console.log('新的置信度数组:', newConfidences);
                        // 更新预测结果状态
                        setConfidences(newConfidences);
                    } catch (predError) {
                        console.error('预测时出错:', predError);
                    }
                }
                
                // 清理资源
                image.dispose();
                logits.dispose();
            } catch (error) {
                console.error('预测错误:', error);
            }
        }, 100);
    };

    // 渲染预览容器
    const renderPreviewContainer = () => {
        return (
            <div style={{
                width: '100%',
                flexDirection: 'column',
                height: '100%',
                display : predicting ? 'block' : 'none'
            }}>
                {/* 预览区域 */}
                <div style={{
                    width: '100%',
                    aspectRatio: '4/3',
                    background: '#f8f9fa',
                    position: 'relative',
                    overflow: 'hidden',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '8px',
                    marginBottom: '16px'
                }}>
                    {/* 视频元素 */}
                    <video 
                        id="preview-video"
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            display: 'none'
                        }}
                        autoPlay
                        muted
                        playsInline
                    />
                </div>
            </div>
        );
    };

    //渲染预览面板
    const renderPreviewPanel = () => {
        return (
            <div style={trainMainStyle.panel}>
                <div style={trainMainStyle.panelContent}>
                    {renderPreviewContainer()}
                    {renderProgressContainer()}
                </div>
            </div>
        )
    }

    //渲染类别置信度展示区域
    const renderProgressContainer = () => {
        return (
            <div style={{
                ...trainMainStyle.progressContainer,
                display : confidences.length > 0 ? 'block' : 'none'
            }}>
                <h4 style={trainMainStyle.progressHeaderText}>输出</h4>
                {/* 对置信度进行排序，最高的显示在最上面，但确保所有类别都显示 */}
                {[...confidences]
                    .map((item, index) => (
                        <div key={`conf-${item.id}`} style={{
                            ...trainMainStyle.progressItem,
                        }}>
                            {/* 类别名称 */}
                            <div style={{
                                ...trainMainStyle.progressItemName,
                                color: classColors[item.id % classColors.length] // 使用item.id确保颜色一致
                            }}>
                                {item.name}
                            </div>
                            
                            {/* 进度条容器 */}
                            <div style={{
                                ...trainMainStyle.progressBarContainer,
                            }}>
                                {/* 进度条 */}
                                <div style={{
                                    ...trainMainStyle.progressBar,
                                    width: `${Math.round(item.confidence * 100)}%`,
                                    backgroundColor: classColors[item.id % classColors.length], // 使用item.id确保颜色一致
                                }} />
                                
                                {/* 百分比文字 */}
                                <div style={{
                                    ...trainMainStyle.progressPercentage,
                                    left: `${Math.min(Math.round(item.confidence * 100), 95)}%`, // 限制位置在95%以内，避免文字溢出
                                }}>
                                    {Math.round(item.confidence * 100)}%
                                </div>
                            </div>
                        </div>
                    ))}
            </div>
        )
    }
    
    return (
        <>
            {renderPreviewPanel()}
        </>
    )
}

export default ImageIdentifyWindows;