import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { CourseTeachingRecord, TeachingStatus, TemplateAppliedStatus, TeachingStatusUtils } from '../../../domain/entities/teaching/course-teaching-record.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { CourseSettings } from '../../../domain/entities/management/course-settings.entity';
import { TaskTemplate } from '../../../domain/entities/teaching/task-template.entity';
import { OneClickStartDto, OneClickStartDataDto, ExecutionDetailsDto, CreatedTaskInfo } from '../../dto/teaching/one-click-start.dto';
import { CourseSettingsDataDto, CourseContentInfoDto, CourseSettingsInfoDto, TaskTemplateInfoDto, CoursePreviewInfoDto } from '../../dto/teaching/course-settings.dto';
import { GetTeachingRecordsQueryDto, TeachingRecordsDataDto, TeachingRecordInfoDto, PaginationInfoDto } from '../../dto/teaching/teaching-records.dto';
import { CalculationUtils } from '../../../utils/management/calculation.utils';
import { LockManager } from '../../../utils/teaching/lock-manager';

import {
  ConcurrencyConflictException,
  DuplicateOperationException,
  CourseNotFoundOrNotPublishedException,
  InsufficientTeacherPermissionException,
  EmptyClassException,
  IncompleteSettingsException,
  PartialFailureException,
  InsufficientStudentPointsException
} from '../../../domain/exceptions/teaching/teaching.exceptions';
import { TaskSelfAssessmentItem } from 'src/util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity';
import { TeacherTask, TaskType, Priority, TaskStatus } from 'src/util/database/mysql/teacher_task/entities/teacher_task.entity';
import { TeacherTaskAssignment } from 'src/util/database/mysql/teacher_task_assignment/entities/teacher_task_assignment.entity';
import { UserClassService } from 'src/util/database/mysql/user_class/user_class.service';
import { UserStudentService } from 'src/util/database/mysql/user_student/user_student.service';
import { WebPointPermissionService } from 'src/web/web_point_permission/web_point_permission.service';

@Injectable()
export class TeachingService {
  private readonly logger = new Logger(TeachingService.name);

  // 1=锁获取失败 
  // 2=权限验证 
  // 3=获取信息 
  // 4=设置验证 
  // 5=创建记录
  // 6=积分分配 
  // 7=权限模板 
  // 8=任务创建 
  // 9=更新记录
  private readonly TEST_ROLLBACK_STEP: string | null = '999'; // 🔧 修改这里来测试不同步骤
  private readonly Allerror: boolean = false; // 🔧 全局测试错误开关，true=启用所有测试错误，false=禁用

  constructor(
    @InjectRepository(CourseTeachingRecord)
    private readonly teachingRecordRepository: Repository<CourseTeachingRecord>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(CourseSeries)
    private readonly courseSeriesRepository: Repository<CourseSeries>,
    @InjectRepository(CourseSettings)
    private readonly courseSettingsRepository: Repository<CourseSettings>,
    @InjectRepository(TaskTemplate)
    private readonly taskTemplateRepository: Repository<TaskTemplate>,
    @InjectRepository(TeacherTask)
    private readonly teacherTaskRepository: Repository<TeacherTask>,
    @InjectRepository(TeacherTaskAssignment)
    private readonly teacherTaskAssignmentRepository: Repository<TeacherTaskAssignment>,
    @InjectRepository(TaskSelfAssessmentItem)
    private readonly taskSelfAssessmentItemRepository: Repository<TaskSelfAssessmentItem>,
    private readonly dataSource: DataSource,
    private readonly lockManager: LockManager,
    private readonly baseUserClassService: UserClassService,
    private readonly userStudentService: UserStudentService,
    private readonly webPointPermissionService: WebPointPermissionService,
  ) { }

  /**
   * 获取测试回滚步骤配置
   */
  private getTestRollbackStep(): string | null {
    return this.TEST_ROLLBACK_STEP;
  }

  /**
   * 一键上课 - 完整的事务处理和错误处理
   */
  async oneClickStart(dto: OneClickStartDto, teacherId: number): Promise<OneClickStartDataDto> {
    // 🧪 测试事务回滚控制变量 - 硬编码版本
    // 修改这个数字来测试不同阶段的回滚：
    // 1=锁获取失败(DuplicateOperationException) 2=权限验证(Error) 3=获取信息(Error) 4=设置验证(Error) 5=创建记录(Error)
    // 6=积分分配(PartialFailureException) 7=权限模板(PartialFailureException) 8=任务创建(PartialFailureException) 9=更新记录(PartialFailureException)
    // 设置为 null 或 0 表示不测试
    const testRollbackStep = this.getTestRollbackStep(); // 获取测试配置

    // 用户调用一键上课接口，传递课程和班级id，根据课程id找到其配置信息，然后对其班级下的所有学生分配数据
    // ===================1.获取分布式锁，防止重复调用接口===================
    // =========2.开启事物=========
    // 3.验证当前老师有无权限开课
    // 4.重复执行开课校验，一天只能开一次
    // 5.获取课程和班级信息，promise并发查询数据库
    // 6.验证课程设置完整性
    // 7.创建或复用教学记录
    // 8.开始进行分配资源
    // 9.更新教学记录状态
    // 10.构造成功响应给前端
    // =========11.提交事物=========
    // ===================12.释放分布式锁===================

    // 使用分布式锁，防止重复调用接口
    const lockKey = `course-teaching:${dto.courseId}:${dto.classId}`;
    const startTime = Date.now();
    let lockAcquireTime = 0; // 移到外层作用域
    console.log("1.获取分布式锁，防止重复调用接口");

    // 🧪 测试回滚点1：模拟锁获取失败
    if (testRollbackStep === '1') {
      this.logger.warn('🧪 测试回滚：模拟锁获取失败');
      throw new DuplicateOperationException(
        dto.courseId,
        dto.classId,
        teacherId,
        -1, // 测试用，没有记录ID
        new Date().toISOString()
      );
    }

    try {
      return await this.lockManager.withDistributedLock(
        lockKey,
        async () => {
          // 记录获取锁的耗时时间
          // 性能监控：记录耗时用于评估锁竞争程度和系统性能，及时发现异常情况。
          // 动态调优：基于历史耗时调整锁超时时间，避免无效等待或过早超时。
          // 故障排查：提供关键调试信息，帮助定位死锁、网络延迟或锁服务问题。
          lockAcquireTime = Date.now() - startTime;
          console.log(`获取锁成功，耗时: ${lockAcquireTime}ms, 开始执行业务逻辑`);

          return this.dataSource.transaction(async (manager) => {
            console.log("2.开启事物....");
            let teachingRecord: CourseTeachingRecord | null = null;

            try {
              console.log("3.验证当前老师有无权限开课....");
              // 🧪 测试回滚点2：权限验证阶段
              if (testRollbackStep === '2') {
                this.logger.warn('🧪 测试回滚：权限验证阶段异常');
                throw new Error('🧪 测试事务回滚：权限验证阶段异常');
              }
              // 3. 验证当前老师有无权限开课
              await this.validateTeachingPermissions(dto, teacherId, manager);
              console.log("教师有权限开课，验证通过");

              console.log("4.并发开课校验，防止同时开课操作....");


              console.log("5.获取课程和班级信息，promise并发查询数据库....");
              // 🧪 测试回滚点3：获取课程信息阶段
              if (testRollbackStep === '3') {
                this.logger.warn('🧪 测试回滚：获取课程信息阶段异常');
                throw new Error('🧪 测试事务回滚：获取课程信息阶段异常');
              }
              // 5. 获取课程和班级信息，promise并发查询数据库
              const [courseInfo, classInfo, students, courseSettings, taskTemplates] = await Promise.all([
                this.getCourseInfoWithValidation(dto.courseId, manager),
                this.getClassInfoWithValidation(dto.classId),
                this.getClassStudentsWithValidation(dto.classId),
                this.getCourseSettingsWithValidation(dto.courseId, manager),
                this.getTaskTemplatesForCourse(dto.courseId, manager)
              ]);
              // console.log("获取课程信息:", courseInfo);
              // console.log("获取班级信息:", classInfo);
              // console.log("获取学生信息:", students);
              // console.log("获取课程设置信息:", courseSettings);
              // console.log("获取任务模板信息:", taskTemplates);

              console.log("6.验证课程设置完整性....");
              // 🧪 测试回滚点4：课程设置验证阶段
              if (testRollbackStep === '4') {
                this.logger.warn('🧪 测试回滚：课程设置验证阶段异常');
                throw new Error('🧪 测试事务回滚：课程设置验证阶段异常');
              }
              // 6. 验证课程设置完整性
              this.validateCourseSettings(dto.courseId, courseSettings, taskTemplates);
              console.log("课程设置完整，验证通过");


              console.log("7.创建或复用教学记录....");
              // 🧪 测试回滚点5：创建教学记录阶段
              if (testRollbackStep === '5') {
                this.logger.warn('🧪 测试回滚：创建教学记录阶段异常');
                throw new Error('🧪 测试事务回滚：创建教学记录阶段异常');
              }
              // 7. 检查是否存在失败记录，如果有则复用，否则创建新记录
              teachingRecord = await this.createOrReuseTeachingRecord(dto, teacherId, lockAcquireTime, manager);


              console.log("8.开始进行分配资源流程....");
              // 8. 开始进行分配资源流程（在事务中执行）
              const executionResult = await this.executeTeachingFlow({
                dto,
                teacherId,
                courseInfo,
                classInfo,
                students,
                courseSettings,
                taskTemplates,
                manager, // 传递事务管理器
                testRollbackStep: testRollbackStep || undefined // 传递测试参数
              });


              console.log("9.更新教学记录状态....");
              // 🧪 测试回滚点9：更新记录阶段
              if (testRollbackStep === '9') {
                this.logger.warn('🧪 测试部分失败：更新记录阶段异常');
                throw new PartialFailureException({
                  templateApplied: true,
                  pointsAllocated: true,
                  tasksCreated: true,
                  recordUpdated: false,
                  details: {
                    successfulOperations: [
                      { operation: '积分分配', affectedStudents: students.length },
                      { operation: '权限模板应用', affectedStudents: students.length },
                      { operation: '任务创建', affectedStudents: students.length }
                    ],
                    failedOperations: [
                      { operation: '记录更新', affectedStudents: 0, error: '数据库更新失败' }
                    ]
                  }
                });
              }
              // 9. 更新教学记录状态（在事务中执行）
              const totalExecutionTime = Date.now() - startTime;
              await this.updateTeachingRecordSuccess(
                teachingRecord,
                executionResult,
                totalExecutionTime,
                manager // 传递事务管理器
              );
              console.log("10.构建成功响应....");
              // 10. 构建成功响应
              console.log("11.提交事物....");
              console.log("12.释放分布式锁....");
              return this.buildSuccessResponse(teachingRecord, executionResult, lockAcquireTime, totalExecutionTime);
            } catch (error) {
              console.log("提交事物失败，一键上课失败，事务进行回滚，更新教学记录为失败状态....");
              console.log("失败对象error", error);

              this.logger.error(`一键上课失败: ${error.message}`, error.stack);

              // 11. 不在事务中处理失败状态，因为记录会被回滚
              // 失败记录将在事务外重新处理
              console.log("事务即将回滚，失败记录将在事务外重新创建或更新");

              // 如果是已知的业务异常，直接抛出
              if (error instanceof ConcurrencyConflictException ||
                error instanceof DuplicateOperationException ||
                error instanceof CourseNotFoundOrNotPublishedException ||
                error instanceof InsufficientTeacherPermissionException ||
                error instanceof EmptyClassException ||
                error instanceof IncompleteSettingsException ||
                error instanceof PartialFailureException ||
                error instanceof InsufficientStudentPointsException) {
                throw error;
              }
              // 其他未知错误，包装为内部服务器错误
              throw new InternalServerErrorException('一键上课执行失败，请稍后重试');
            }
          });
        },
        60000 // 60秒锁超时
      );

    } catch (error) {
      console.log("一键上课执行失败，开始处理异常");
      this.logger.error(`一键上课执行失败: ${error.message}`, error.stack);

      // 👇 检查是否是并发冲突异常（ConcurrencyConflictException）
      if (error instanceof ConcurrencyConflictException) {
        console.log("检测到并发冲突异常，转换为重复开课异常，不创建失败记录");
        // 抛出用户友好的重复操作异常
        throw new DuplicateOperationException(
          dto.courseId,
          dto.classId,
          teacherId,
          -1, // 没有记录ID，使用-1表示
          new Date().toISOString()
        );
      }

      // 特殊处理部分失败异常
      if (error instanceof PartialFailureException) {
        console.log("============= 事务回滚，但保存部分失败的详细信息 =============");
        console.log("失败操作：", (error.getResponse() as any).data?.details?.failedOperations);

        try {
          const totalExecutionTime = Date.now() - startTime;
          const responseData = error.getResponse() as any;
          const partialData = responseData.data;

          // 补充时间信息
          partialData.lockAcquireTime = lockAcquireTime;
          partialData.totalExecutionTime = totalExecutionTime;

          // 创建部分成功的失败记录，包含详细的失败信息
          const partialFailureRecord = await this.createPartialFailureRecord(
            dto,
            teacherId,
            lockAcquireTime,
            partialData,
            totalExecutionTime
          );

          // 补充教学记录ID到响应数据中
          partialData.teachingRecordId = partialFailureRecord.id;

          console.log("部分失败记录创建成功");
        } catch (recordError) {
          console.error(`处理部分失败记录时出错:`, recordError);
          this.logger.error(`处理部分失败记录时出错: ${recordError.message}`);
        }

        // 重新抛出异常，让控制器返回206状态码
        throw error;
      }

      // 事务失败后，原教学记录已被回滚删除，需要重新处理失败记录
      console.log("事务执行失败，重新处理失败记录");

      try {
        const totalExecutionTime = Date.now() - startTime;
        // 检查是否已存在今天的记录（可能是之前复用的失败记录）
        const today = new Date().toISOString().split('T')[0];
        const existingRecord = await this.teachingRecordRepository
          .createQueryBuilder('record')
          .where('record.courseId = :courseId', { courseId: dto.courseId })
          .andWhere('record.classId = :classId', { classId: dto.classId })
          .andWhere('record.teacherId = :teacherId', { teacherId })
          .andWhere('DATE(record.createdAt) = :today', { today })
          .getOne();

        if (existingRecord) {
          // 如果存在记录（可能是复用的记录被回滚后重新创建），直接更新为失败状态
          this.logger.log(`复用失败记录: 记录ID=${existingRecord.id}, 重置为失败状态`);
          await this.teachingRecordRepository.update(existingRecord.id, {
            status: TeachingStatus.FAILED,
            errorMessage: error.message || '一键上课执行失败',
            totalExecutionTime,
          });
          console.log("失败记录更新成功");
        } else {
          // 如果不存在记录，创建新的失败记录
          this.logger.log('没有找到失败记录，创建新的失败记录');
          const failedRecord = await this.createFailedTeachingRecord(
            dto,
            teacherId,
            0, // lockAcquireTime设为0
            error.message || '一键上课执行失败',
            totalExecutionTime
          );
          console.log("失败记录创建成功");
        }
      } catch (recordError) {
        this.logger.error(`处理失败记录时出错: ${recordError.message}`);
      }

      // 重新抛出原始异常
      throw error;
    }
  }

  /**
   * 验证教学权限
   */
  private async validateTeachingPermissions(dto: OneClickStartDto, teacherId: number, manager: any): Promise<void> {
    // 1.获取课程信息以进行权限检查
    const course = await manager.findOne(Course, {
      where: { id: dto.courseId },
      select: ['id', 'status', 'creatorId', 'title']
    });

    // 2.如果找不到课程，直接抛出课程不存在异常
    if (!course) {
      throw new CourseNotFoundOrNotPublishedException(dto.courseId);
    }

    // 3.检查教师对课程的权限
    const hasPermission = this.checkCoursePermission(course, teacherId);
    // 4.校验失败，抛出异常
    if (!hasPermission) {
      throw new InsufficientTeacherPermissionException(
        teacherId,
        dto.classId,
        dto.courseId,
        course.status,
        course.creatorId
      );
    }
  }

  /**
   * 检查课程权限的纯函数
   */
  private checkCoursePermission(course: any, teacherId: number): boolean {
    // 1.如果课程已发布，所有教师都可以操作
    if (course.status === 1) {
      this.logger.debug(`课程已发布，允许所有教师操作: courseId=${course.id}, teacherId=${teacherId}`);
      return true;
    }

    // 2.如果课程未发布，只有创建者可以操作
    if (course.creatorId === teacherId) {
      this.logger.debug(`课程未发布，但当前用户是创建者，允许操作: courseId=${course.id}, teacherId=${teacherId}, creatorId=${course.creatorId}`);
      return true;
    }

    // 3.课程未发布且当前用户不是创建者，拒绝操作 （拿不到宝贵的true）
    this.logger.warn(`课程未发布且当前用户不是创建者，拒绝操作: courseId=${course.id}, teacherId=${teacherId}, creatorId=${course.creatorId}, status=${course.status}`);
    return false;
  }







  /**
   * 获取课程信息并验证
   */
  private async getCourseInfoWithValidation(courseId: number, manager: any) {
    // 1.获取课程信息，包含了系列的相关信息
    console.log("进入获取课程信息接口调用");
    const course = await manager
      .createQueryBuilder(Course, 'course')
      .leftJoinAndSelect('course.series', 'series')
      .where('course.id = :courseId', { courseId })
      // .andWhere('course.status = :status', { status: 1 }) // 只允许已发布的课程 （也可以没发布的课程，私人定制）
      .getOne();
    // console.log("获取的课程信息:", course);
    // 2.如果不存在抛出课程不存在异常
    if (!course) {
      console.log("课程不存在");
      throw new CourseNotFoundOrNotPublishedException(courseId);
    }
    // 3.返回课程信息
    console.log("课程信息获取成功");
    return {
      id: course.id,
      title: course.title,
      seriesName: course.series?.title || '未知系列',
      status: course.status
    };
  }

  /**
   * 获取班级信息并验证
   */
  private async getClassInfoWithValidation(classId: number) {
    try {
      const classInfo = await this.getClassInfo(classId);
      console.log("班级信息获取成功");
      return classInfo;
    } catch (error) {
      this.logger.error(`获取班级信息失败: ${error.message}`);
      throw new BadRequestException('获取班级信息失败，请检查班级是否存在');
    }
  }

  /**
   * 获取班级学生并验证
   */
  private async getClassStudentsWithValidation(classId: number) {
    const students = await this.getClassStudents(classId);

    if (!students || students.length === 0) {
      throw new EmptyClassException(classId, 0);
    }
    console.log("班级学生获取成功");
    return students;
  }

  /**
   * 获取课程设置并验证
   */
  private async getCourseSettingsWithValidation(courseId: number, manager: any) {
    const courseSettings = await manager.findOne(CourseSettings, {
      where: { courseId },
    });
    console.log("课程设置获取成功");
    // 课程设置可以为空，但如果存在则需要验证完整性
    return courseSettings;
  }

  /**
   * 获取任务模板
   */
  private async getTaskTemplatesForCourse(courseId: number, manager: any) {
    console.log("任务模板获取成功");
    return await manager.find(TaskTemplate, {
      where: { courseId },
      order: { id: 'ASC' },
    });
  }

  /**
   * 验证课程设置完整性
   */
  private validateCourseSettings(courseId: number, courseSettings: any, taskTemplates: any[]): void {
    const missingSettings: string[] = [];

    // 1.如果开启了自动创建任务但没有任务模板
    if (courseSettings?.autoCreateTasks && (!taskTemplates || taskTemplates.length === 0)) {
      missingSettings.push('taskTemplates');
    }

    // 2.如果配置了需要应用模板但模板ID为空或无效
    if (courseSettings?.templateId && courseSettings.templateId <= 0) {
      missingSettings.push('templateId');
    }

    if (missingSettings.length > 0) {
      throw new IncompleteSettingsException(courseId, missingSettings);
    }
  }



  /**
   * 直接创建失败状态的教学记录（一条龙操作，避免残留）
   */
  private async createFailedTeachingRecord(
    dto: OneClickStartDto,
    teacherId: number,
    lockAcquireTime: number,
    errorMessage: string,
    totalExecutionTime: number
  ): Promise<CourseTeachingRecord> {
    const record = this.teachingRecordRepository.create({
      courseId: dto.courseId,
      classId: dto.classId,
      teacherId,
      status: TeachingStatus.FAILED,
      lockAcquireTime,
      errorMessage,
      totalExecutionTime,
    });

    return await this.teachingRecordRepository.save(record);
  }

  /**
   * 创建部分失败状态的教学记录，包含详细的失败信息
   */
  private async createPartialFailureRecord(
    dto: OneClickStartDto,
    teacherId: number,
    lockAcquireTime: number,
    partialData: any,
    totalExecutionTime: number
  ): Promise<CourseTeachingRecord> {
    console.log("======开始创建部分失败记录=====");
    try {
      // 先检查今天是否已有相同组合的记录
      const today = new Date().toISOString().split('T')[0];
      console.log(`检查今天(${today})是否已有相同组合的记录：课程=${dto.courseId}, 班级=${dto.classId}, 教师=${teacherId}`);

      const existingRecord = await this.teachingRecordRepository
        .createQueryBuilder('record')
        .where('record.courseId = :courseId', { courseId: dto.courseId })
        .andWhere('record.classId = :classId', { classId: dto.classId })
        .andWhere('record.teacherId = :teacherId', { teacherId })
        .andWhere('DATE(record.createdAt) = :today', { today })
        .getOne();

      if (existingRecord) {
        console.log(`找到已存在的记录，ID=${existingRecord.id}，将进行更新而不是创建新记录`);

        // 更新现有记录
        existingRecord.status = TeachingStatus.FAILED;
        existingRecord.lockAcquireTime = lockAcquireTime;
        existingRecord.pointsAllocated = partialData.pointsAllocated || 0;
        existingRecord.tasksCreated = partialData.tasksCreated || 0;
        existingRecord.templateApplied = partialData.templateApplied ? TemplateAppliedStatus.YES : TemplateAppliedStatus.NO;
        existingRecord.executionDetails = partialData;
        existingRecord.totalExecutionTime = totalExecutionTime;
        existingRecord.errorMessage = `部分操作失败: ${partialData.details?.failedOperations?.map(op => op.operation).join(', ')} 操作失败，共影响${partialData.details?.failedOperations?.reduce((total, op) => total + op.affectedStudents, 0) || 0}名学生`;

        const updatedRecord = await this.teachingRecordRepository.save(existingRecord);
        console.log(`现有记录更新成功，ID=${updatedRecord.id}`);
        return updatedRecord;
      } else {
        console.log("未找到现有记录，创建新记录");
        const record = this.teachingRecordRepository.create({
          courseId: dto.courseId,
          classId: dto.classId,
          teacherId,
          status: TeachingStatus.FAILED,
          lockAcquireTime,
          pointsAllocated: partialData.pointsAllocated || 0,
          tasksCreated: partialData.tasksCreated || 0,
          templateApplied: partialData.templateApplied ? TemplateAppliedStatus.YES : TemplateAppliedStatus.NO,
          executionDetails: partialData,
          totalExecutionTime,
          errorMessage: `部分操作失败: ${partialData.details?.failedOperations?.map(op => op.operation).join(', ')} 操作失败，共影响${partialData.details?.failedOperations?.reduce((total, op) => total + op.affectedStudents, 0) || 0}名学生`,
        });

        console.log("新记录创建完成，准备保存到数据库");
        console.log("记录信息:", {
          courseId: record.courseId,
          classId: record.classId,
          teacherId: record.teacherId,
          status: record.status,
          errorMessage: record.errorMessage
        });

        const savedRecord = await this.teachingRecordRepository.save(record);
        console.log("新记录保存成功，ID:", savedRecord.id);
        return savedRecord;
      }
    } catch (error) {
      console.error("保存部分失败记录时出错:", error);
      throw error;
    }
  }

  /**
   * 创建或复用教学记录（优先复用失败记录）
   */
  private async createOrReuseTeachingRecord(
    dto: OneClickStartDto,
    teacherId: number,
    lockAcquireTime: number,
    manager: any
  ): Promise<CourseTeachingRecord> {
    // 1. 先查找今天是否有失败的记录
    const today = new Date().toISOString().split('T')[0];
    const existingFailedRecord = await manager
      .createQueryBuilder(CourseTeachingRecord, 'record')
      .where('record.courseId = :courseId', { courseId: dto.courseId })
      .andWhere('record.classId = :classId', { classId: dto.classId })
      .andWhere('record.teacherId = :teacherId', { teacherId })
      .andWhere('record.status = :status', { status: TeachingStatus.FAILED })
      .andWhere('DATE(record.createdAt) = :today', { today })
      .getOne();

    if (existingFailedRecord) {
      // 2. 如果有失败记录，复用它并重置为进行中状态
      this.logger.log(`复用失败记录: 记录ID=${existingFailedRecord.id}, 重置为进行中状态`);
      console.log("更新失败记录为进行中状态");
      await manager.update(CourseTeachingRecord, existingFailedRecord.id, {
        status: TeachingStatus.IN_PROGRESS,
        lockAcquireTime,
        errorMessage: null, // 清空之前的错误信息
        totalExecutionTime: 0, // 重置为0而不是null
        pointsAllocated: 0, // 重置为0而不是null
        tasksCreated: 0, // 重置为0而不是null
        templateApplied: TemplateAppliedStatus.NO, // 重置为NO而不是null
        executionDetails: null, // 清空之前的执行详情
      });
      console.log("失败记录更新成功");
      // 重新查询更新后的记录
      return await manager.findOne(CourseTeachingRecord, { where: { id: existingFailedRecord.id } });
    } else {
      // 3. 如果没有失败记录，创建新记录
      this.logger.log('没有找到失败记录，创建新的教学记录');
      console.log("创建新的教学记录");
      const record = manager.create(CourseTeachingRecord, {
        courseId: dto.courseId,
        classId: dto.classId,
        teacherId,
        status: TeachingStatus.IN_PROGRESS,
        lockAcquireTime,
      });

      return await manager.save(record);
    }
  }

  /**
   * 在事务中创建教学记录（保留用于兼容性）
   */
  private async createTeachingRecordInTransaction(
    dto: OneClickStartDto,
    teacherId: number,
    lockAcquireTime: number,
    manager: any
  ): Promise<CourseTeachingRecord> {
    const record = manager.create(CourseTeachingRecord, {
      courseId: dto.courseId,
      classId: dto.classId,
      teacherId,
      status: TeachingStatus.IN_PROGRESS,
      lockAcquireTime,
    });

    return await manager.save(record);
  }

  // /**
  //  * 获取课程信息
  //  */
  // private async getCourseInfo(courseId: number) {
  //   const course = await this.courseRepository
  //     .createQueryBuilder('course')
  //     .leftJoinAndSelect('course.series', 'series')
  //     .where('course.id = :courseId', { courseId })
  //     .getOne();

  //   if (!course) {
  //     throw new BadRequestException('课程不存在');
  //   }

  //   return {
  //     id: course.id,
  //     title: course.title,
  //     seriesName: course.series?.title || '未知系列',
  //   };
  // }

  /**
   * 获取班级信息（调用实际的班级信息API）
   */
  private async getClassInfo(classId: number) {
    try {
      // 1.获取班级信息
      const classInfo = await this.baseUserClassService.findOne(+classId);
      this.logger.log(`获取班级信息成功: ${JSON.stringify(classInfo)}`);
      if (!classInfo) {
        throw new BadRequestException(`班级ID为${classId}的记录不存在`);
      }

      return {
        id: classInfo.id,
        name: classInfo.className,
        schoolId: classInfo.schoolId,
        grade: classInfo.grade,
        teacherId: classInfo.teacherId,
        assistantTeacherId: classInfo.assistantTeacherId,
        inviteCode: classInfo.inviteCode,
      };
    } catch (error) {
      this.logger.error(`获取班级信息失败: ${error.message}`);
      throw new BadRequestException('获取班级信息失败，请检查班级是否存在');
    }
  }

  /**
   * 获取班级学生列表（调用userStudent服务）
   */
  private async getClassStudents(classId: number) {
    try {
      // 调用userStudent服务获取学生列表
      const students = await this.userStudentService.findByClass(+classId);

      // 转换为需要的格式
      return students.map(student => ({
        id: student.userId,
        name: `学生${student.userId}`, // 使用用户ID作为默认名称
        studentId: student.id,
        classId: student.classId,
        schoolId: student.schoolId,
        studentNumber: student.studentNumber,
      }));
    } catch (error) {
      this.logger.error(`获取班级学生列表失败: ${error.message}`);
      throw new BadRequestException('获取班级学生列表失败');
    }
  }





  /**
   * 执行教学流程
   */
  private async executeTeachingFlow(params: {
    dto: OneClickStartDto;
    teacherId: number;
    courseInfo: any;
    classInfo: any;
    students: any[];
    courseSettings: any;
    taskTemplates: any[];
    manager: any; // 事务管理器，确保在事务中执行
    testRollbackStep?: string; // 测试回滚步骤
  }) {
    // 1.提取params
    const { dto, teacherId, courseInfo, classInfo, students, courseSettings, taskTemplates, manager, testRollbackStep } = params;
    // 2.构造初始化result
    const result = {
      pointsAllocated: 0,
      tasksCreated: 0,
      templateApplied: false,
      createdTasks: [] as CreatedTaskInfo[],
      failedOperations: [] as Array<{
        operation: string;
        error: string;
        affectedStudents: number;
      }>,
      warningMessages: [] as string[],
      details: {
        courseName: courseInfo.title,
        seriesName: courseInfo.seriesName,
        className: classInfo.name,
        studentCount: students.length,
        pointsPerStudent: 0,
        templateName: '',
      }
    };

    // 3.开始构造result - 支持部分失败模式
    // 4. 分配积分权限，必须要大于0（在事务中执行）
    if (courseSettings?.requiredPoints > 0) {
      try {
        // 调用内部方法，批量分配积分
        const pointsResult = await this.allocatePoints(dto.classId, students, courseSettings.requiredPoints, teacherId, manager);
        // 总分配成功的分数
        result.pointsAllocated = pointsResult.totalPoints;
        // 单个学生分配的分数作为细节记录
        result.details.pointsPerStudent = courseSettings.requiredPoints;
        console.log("分配积分权限成功，pointsResult：", pointsResult);

        // 处理积分不足的学生
        if (pointsResult.insufficientStudents && pointsResult.insufficientStudents.length > 0) {
          result.failedOperations.push({
            operation: 'allocatePoints',
            error: '部分学生积分不足，无法完成积分分配',
            affectedStudents: pointsResult.insufficientStudents.length
          });

          // 为每个积分不足的学生添加详细的警告信息
          pointsResult.insufficientStudents.forEach(student => {
            result.warningMessages.push(
              `学生：${student.studentName}积分不足，当前${student.currentPoints}分，需要${student.requiredPoints}分，已跳过积分分配`
            );
          });
          result.warningMessages.push(`共${pointsResult.insufficientStudents.length}名学生的积分分配失败，请稍后手动处理`);
        }
      } catch (error) {
        this.logger.error(`积分分配失败: ${error.message}`);
        result.failedOperations.push({
          operation: 'allocatePoints',
          error: error.message || '积分分配过程中发生未知错误',
          affectedStudents: students.length
        });
        result.warningMessages.push(`积分分配失败，请稍后手动处理`);
      }
    }

    // 5. 应用权限模板，必须存在才分配（在事务中执行）
    if (courseSettings?.templateId) {
      try {
        // 🧪 测试回滚点7：权限模板应用阶段
        if (testRollbackStep === '7') {
          this.logger.warn('🧪 测试部分失败：权限模板应用阶段异常');
          throw new PartialFailureException({
            templateApplied: false,
            pointsAllocated: true,
            tasksCreated: false,
            details: {
              successfulOperations: [
                { operation: '积分分配', affectedStudents: students.length }
              ],
              failedOperations: [
                { operation: '权限模板应用', affectedStudents: students.length, error: '模板服务暂时不可用' }
              ]
            }
          });
        }
        await this.applyPermissionTemplate(students, courseSettings.templateId, manager);
        result.templateApplied = true;
        result.details.templateName = `模板${courseSettings.templateId}`;
        console.log("应用权限模板成功");
      } catch (error) {
        this.logger.error(`权限模板应用失败: ${error.message}`);

        // 获取失败的学生信息
        const failedStudents = (error as any).failedStudents || students.slice(0, 3); // 如果没有详细信息，默认前3个

        result.failedOperations.push({
          operation: 'applyTemplate',
          error: error.message || '模板应用过程中发生未知错误',
          affectedStudents: failedStudents.length
        });

        // 为每个失败的学生添加详细的警告信息
        failedStudents.forEach(student => {
          result.warningMessages.push(
            `学生：${student.name || student.id}的权限模板应用失败：${error.message}`
          );
        });

        result.warningMessages.push(`共${failedStudents.length}名学生的模板应用失败，请稍后手动处理`);
      }
    }

    // 6. 创建任务（在事务中执行）
    if (taskTemplates.length > 0 && courseSettings?.autoCreateTasks) {
      try {
        // 🧪 测试回滚点8：任务创建阶段
        if (testRollbackStep === '8') {
          this.logger.warn('🧪 测试部分失败：任务创建阶段异常');
          throw new PartialFailureException({
            templateApplied: true,
            pointsAllocated: true,
            tasksCreated: false,
            details: {
              successfulOperations: [
                { operation: '积分分配', affectedStudents: students.length },
                { operation: '权限模板应用', affectedStudents: students.length }
              ],
              failedOperations: [
                { operation: '任务创建', affectedStudents: students.length, error: '系统繁忙' }
              ]
            }
          });
        }
        const tasksResult = await this.createTasks(dto.classId, students, taskTemplates, teacherId, manager);
        result.tasksCreated = tasksResult.length;
        result.createdTasks = tasksResult;
        console.log("创建任务成功");
      } catch (error) {
        this.logger.error(`任务创建失败: ${error.message}`);

        // 获取失败的学生信息
        const failedStudents = (error as any).failedStudents || students.slice(-2); // 如果没有详细信息，默认后2个（用户测试）

        result.failedOperations.push({
          operation: 'createTasks',
          error: error.message || '任务创建过程中发生未知错误',
          affectedStudents: failedStudents.length
        });

        // 为每个失败的学生添加详细的警告信息
        failedStudents.forEach((student: any) => {
          result.warningMessages.push(
            `学生：${student.name || student.id}的任务创建失败：${error.message}`
          );
        });

        result.warningMessages.push(`共${failedStudents.length}名学生的任务创建失败，请稍后手动处理`);
      }
    }

    // 检查是否有部分失败，如果有则抛出部分失败异常
    if (result.failedOperations.length > 0) {
      this.logger.warn(`检测到部分失败，失败操作数: ${result.failedOperations.length}`);

      // 构建部分失败响应数据
      const partialFailureData = {
        success: true, // 虽然部分失败，但整体流程仍然成功
        teachingRecordId: null, // 记录ID在外部补充
        pointsAllocated: result.pointsAllocated,
        tasksCreated: result.tasksCreated,
        templateApplied: result.templateApplied,
        executionTime: new Date().toISOString(),
        lockAcquireTime: 0, // 在外部补充
        totalExecutionTime: 0, // 在外部补充
        details: {
          ...result.details,
          createdTasks: result.createdTasks,
          failedOperations: result.failedOperations,
          warningMessages: result.warningMessages
        }
      };

      throw new PartialFailureException(partialFailureData);
    }

    return result;
  }



  /**
   * 分配积分权限（在事务中执行，批量优化，提升性能）
   */
  private async allocatePoints(classId: number, students: any[], pointsPerStudent: number, teacherId: number, manager: any) {
    const startTime = Date.now();
    this.logger.log(`步骤1: 开始积分分配: 班级ID=${classId}, 教师ID=${teacherId}, 学生数=${students.length}, 每人积分=${pointsPerStudent}`);
    console.log("开始分配积分");

    const results: Array<{ studentId: number; status: string; error?: string }> = [];
    let successCount = 0;
    let failedCount = 0;

    const studentIds = students.map(s => s.id);
    this.logger.debug(`提取学生ID列表: [${studentIds.join(', ')}]`);

    // 1. 批量检查学生是否存在user_info表
    this.logger.debug('步骤1: 开始批量检查学生是否存在');
    const existingStudents = await manager.query(
      `SELECT id FROM user_info WHERE id IN (${studentIds.map(() => '?').join(',')})`,
      // 将[1,2,3]转为'?,?,?'占位符，然后数组studentIds填坑
      studentIds
    );
    // 转为Set，提升性能
    // 性能提升的核心原因在于 Set 的查找时间复杂度是 O(1)，而数组的查找是 O(n)。
    // 每次查找需要遍历数组（时间复杂度 O(n)）
    // const isExist = existingStudentIds.includes(202); // 最坏情况下要查 3 次
    // Set 的 has() 方法是哈希查找（时间复杂度 O(1)）
    // const isExist = existingStudentIds.has(202); // 只需查 1 次
    const existingStudentIds = new Set(existingStudents.map(s => s.id));
    this.logger.debug(`学生存在性检查完成: 查询到${existingStudents.length}个有效学生`);

    // 检查是否有不存在的学生
    for (const studentId of studentIds) {
      if (!existingStudentIds.has(studentId)) {
        this.logger.error(`学生验证失败: 学生ID ${studentId} 不存在于user_info表`);
        throw new Error(`学生ID ${studentId} 不存在`);
      }
    }
    this.logger.debug('步骤1完成: 所有学生都存在于系统中');










    // 2. 获取学生的特殊套餐总额度   逻辑为从userPackage获取用户的所有的有效的特殊套餐总和，
    // 过滤出有效的特殊套餐（status !== 0 且 assignType === 2）并计算积分总和
    this.logger.debug('步骤2: 开始批量获取学生总的特殊套餐积分信息');
    const studentPointsResult = await manager.query(
      `SELECT 
        userId,
        COALESCE(SUM(points), 0) AS total
       FROM user_package
       WHERE status != 0 
         AND assignType = 2 
         AND userId IN (${studentIds.map(() => '?').join(',')})
         AND userId IS NOT NULL
       GROUP BY userId`,
      studentIds
    );
    const studentPointsMap = new Map();
    this.logger.debug(`学生积分查询结果: 查询到${studentPointsResult.length}个学生的积分记录`);
    studentPointsResult.forEach(row => {
      studentPointsMap.set(row.userId, Number(row.total));
      this.logger.debug(`学生ID ${row.userId} 当前总的特殊套餐积分: ${row.total}`);
    });
    this.logger.debug('步骤2完成: 学生总的特殊套餐积分信息获取完毕');





    // 3. 找该学生所有已经分配出去的有效且未过期的权限记录，用于计算总分配积分并与特殊套餐总额度进行比较
    this.logger.debug('步骤3: 找该学生所有已分配的权限记录');
    const existingPermissions = await manager.query(
      `SELECT
        id,
        studentUserId,
        teacherUserId,
        availablePoints,
        expireTime,
        status,
        createTime,
        remark
       FROM user_points_permission
       WHERE studentUserId IN (${studentIds.map(() => '?').join(',')})
         AND status = 1
         AND (expireTime IS NULL OR expireTime > NOW())
       ORDER BY studentUserId, createTime DESC`,
      [...studentIds]
    );
    // 建立当前开课教师的权限记录映射表Map
    const teacherPermissionsMap = new Map();
    this.logger.debug(`权限记录查询结果: 查询到${existingPermissions.length}个现有权限记录`);

    // 按学生ID分组所有权限记录
    const studentPermissionsMap = new Map();
    existingPermissions.forEach(perm => {
      const studentId = perm.studentUserId;
      // 不存在则初始化
      if (!studentPermissionsMap.has(studentId)) {
        studentPermissionsMap.set(studentId, []);
      }
      // 将权限记录添加到对应学生的数组中
      studentPermissionsMap.get(studentId).push(perm);
    });

    // 对每个学生进行积分检查，收集错误信息而不是直接抛异常
    const validStudents: any[] = [];
    const insufficientStudents: Array<{
      studentId: any;
      studentName: string;
      currentPoints: number;
      requiredPoints: number;
      errorType: string;
      errorMessage: string;
    }> = [];

    // 遍历所有学生进行检查
    for (const student of students) {
      const studentId = student.id;
      const studentTotalPoints = studentPointsMap.get(studentId) || 0;

      // 🧪 测试模式：模拟前3个学生积分不足
      const openTest = false;
      if ((this.getTestRollbackStep() === '6' || openTest || this.Allerror) && students.indexOf(student) < 3) {
        console.log(`正在模拟学生ID ${studentId} 积分不足`);
        insufficientStudents.push({
          studentId,
          studentName: student.name || `学生${studentId}`,
          currentPoints: 0, // 模拟积分为0
          requiredPoints: pointsPerStudent,
          errorType: 'test_simulation',
          errorMessage: '测试模拟积分不足'
        });
        continue; // 跳过这个学生
      }

      // 检查该学生是否有权限记录
      const permissions = studentPermissionsMap.get(studentId) || [];

      // 计算学生已经分配出去的总积分（只统计有效的权限）
      const totalAllocatedPoints = permissions.reduce((sum, permission) => {
        // 只统计有效的权限：status === 1 且未过期
        if (permission.status === 1 && (!permission.expireTime || new Date(permission.expireTime) > new Date())) {
          const newSum = Number(sum) + Number(permission.availablePoints || 0);
          this.logger.debug(`学生ID ${studentId} 累加积分: ${sum} + ${permission.availablePoints} = ${newSum}`);
          return newSum;
        }
        return Number(sum);
      }, 0);

      this.logger.debug(`学生ID ${studentId} 总已分配积分: ${totalAllocatedPoints}, 本次分配积分: ${pointsPerStudent}，能够分配的总特殊套餐积分为${studentTotalPoints}`);

      // 计算本次分配后的总分配量
      const totalAfterAllocation = Number(totalAllocatedPoints) + Number(pointsPerStudent);
      this.logger.debug(`学生ID ${studentId} 分配后总积分: ${totalAfterAllocation}, 特殊套餐总额度: ${studentTotalPoints}, 剩余可分配: ${studentTotalPoints - totalAfterAllocation}`);

      // 检查分配后的总积分是否超过特殊套餐总积分
      if (totalAfterAllocation > studentTotalPoints) {
        this.logger.warn(
          `学生ID ${studentId} 分配积分超过限制，学生特殊套餐总积分${Number(studentTotalPoints)}，` +
          `已分配${Number(totalAllocatedPoints)}，本次分配${Number(pointsPerStudent)}，` +
          `分配后总积分将达到${Number(totalAfterAllocation)}`
        );
        insufficientStudents.push({
          studentId,
          studentName: student.name || `学生${studentId}`,
          currentPoints: studentTotalPoints,
          requiredPoints: pointsPerStudent,
          errorType: 'allocation_limit_exceeded',
          errorMessage: `分配积分超过限制，已分配${totalAllocatedPoints}，本次分配${pointsPerStudent}，总额度${studentTotalPoints}`
        });
        continue; // 跳过这个学生
      }

      // 学生通过检查，添加到有效学生列表
      validStudents.push(student);

      // 为了保持兼容性，将数据装入原有的Map结构
      // 筛选出当前教师分配给该学生的权限记录
      const teacherPermissions = permissions.filter(p => p.teacherUserId === teacherId);
      const existingTeacherPermission = teacherPermissions.length > 0 ? teacherPermissions[0] : null;

      teacherPermissionsMap.set(studentId, {
        id: existingTeacherPermission ? existingTeacherPermission.id : null,
        availablePoints: totalAllocatedPoints, // 这里是所有教师分配的总积分，用于检查
        teacherAvailablePoints: existingTeacherPermission ? Number(existingTeacherPermission.availablePoints || 0) : 0, // 当前教师分配的积分
        records: permissions, // 保存所有记录供后续使用
        teacherRecords: teacherPermissions // 保存当前教师的记录
      });
    }

    this.logger.debug(`步骤3完成: 积分检查完毕，有效学生${validStudents.length}个，积分不足学生${insufficientStudents.length}个`);

    // 如果没有积分足够的学生，直接返回失败信息
    if (validStudents.length === 0) {
      this.logger.warn('所有学生积分都不足，无法进行积分分配');
      return {
        totalPoints: 0,
        successCount: 0,
        failCount: students.length,
        results: students.map(student => ({
          studentId: student.id,
          status: 'failed',
          error: '积分不足'
        })),
        insufficientStudents
      };
    }















    // 4. 批量创建总积分的分配记录，并更新学生积分总数（只处理积分足够的学生）
    this.logger.debug('步骤4: 开始构建积分流水记录');
    const pointsInsertValues = validStudents.map(student => {
      const studentId = student.id;
      const studentTotalPoints = studentPointsMap.get(studentId) || 0;
      const newTotalPoints = studentTotalPoints - pointsPerStudent;
      this.logger.debug(`构建学生ID ${studentId} 积分流水: 扣除${pointsPerStudent}分, 余额${newTotalPoints}分`);
      return `(${studentId}, ${-pointsPerStudent}, ${newTotalPoints}, 2, 2, '老师${teacherId}一键上课自动分配积分 - 班级ID: ${classId}', '${teacherId}', NOW(), NOW())`;
    }).join(', ');

    // 5. 批量插入积分流水记录表
    this.logger.debug('步骤5: 开始批量插入积分流水记录');
    await manager.query(
      `INSERT INTO user_points (userId, pointsValue, totalPoints, type, source, remark, operator, createTime, updateTime)
       VALUES ${pointsInsertValues}`
    );
    this.logger.debug(`步骤5完成: 成功插入${validStudents.length}条积分流水记录`);











    // 6. 批量处理权限记录（更新已有的，插入新的）- 只处理积分足够的学生
    this.logger.debug('步骤6: 开始处理权限记录');
    // updatePromises请求数组，如果存在权限记录，则将update语句push进来，批量更新
    const updatePromises: Promise<any>[] = [];
    // newPermissions请求数组，如果不存在权限记录，则将insert语句push进来，批量插入
    const newPermissions: string[] = [];

    for (const student of validStudents) {
      const studentId = student.id;
      const existingPerm = teacherPermissionsMap.get(studentId);

      if (existingPerm && existingPerm.id) {
        // 当前教师已经给该学生分配过权限，更新现有权限（累加积分）
        const newAvailablePoints = existingPerm.teacherAvailablePoints + pointsPerStudent;
        this.logger.debug(`学生ID ${studentId} 更新教师权限: ${existingPerm.teacherAvailablePoints} + ${pointsPerStudent} = ${newAvailablePoints}`);
        updatePromises.push(
          manager.query(
            'UPDATE user_points_permission SET availablePoints = ?, updateTime = NOW() WHERE id = ?',
            [newAvailablePoints, existingPerm.id]
          )
        );
      } else {
        // 当前教师没有给该学生分配过权限，创建新权限记录
        this.logger.debug(`学生ID ${studentId} 创建新权限记录: ${pointsPerStudent}分`);
        newPermissions.push(`(${studentId}, ${teacherId}, ${pointsPerStudent}, 1, '一键上课自动分配积分 - 班级ID: ${classId}', NOW(), NOW())`);
      }

      results.push({ studentId, status: 'success' });
      successCount++;
    }

    // 记录积分不足的学生为失败状态
    for (const insufficientStudent of insufficientStudents) {
      results.push({
        studentId: insufficientStudent.studentId,
        status: 'failed',
        error: `积分不足，当前${insufficientStudent.currentPoints}分，需要${insufficientStudent.requiredPoints}分`
      });
      failedCount++;
    }

    // 执行批量更新
    if (updatePromises.length > 0) {
      this.logger.debug(`执行批量更新: ${updatePromises.length}个权限记录`);
      await Promise.all(updatePromises);
      this.logger.debug('批量更新权限记录完成');
    }

    // 批量插入新权限记录
    if (newPermissions.length > 0) {
      this.logger.debug(`执行批量插入: ${newPermissions.length}个新权限记录`);
      // 多条记录用,分隔
      await manager.query(
        `INSERT INTO user_points_permission (studentUserId, teacherUserId, availablePoints, status, remark, createTime, updateTime)
         VALUES ${newPermissions.join(', ')}`
      );
      this.logger.debug('批量插入新权限记录完成');
    }
    this.logger.debug('步骤6完成: 权限记录处理完毕');

    // 7. 记录总分配积分数量和性能统计
    const totalPoints = validStudents.length * pointsPerStudent;
    const executionTime = Date.now() - startTime;

    this.logger.log(`步骤7: 积分分配操作完成统计`);
    this.logger.log(`✅ 积分分配成功完成:`);
    this.logger.log(`   - 班级ID: ${classId}`);
    this.logger.log(`   - 教师ID: ${teacherId}`);
    this.logger.log(`   - 总分配积分: ${totalPoints}分`);
    this.logger.log(`   - 每人积分: ${pointsPerStudent}分`);
    this.logger.log(`   - 成功人数: ${successCount}人`);
    this.logger.log(`   - 失败人数: ${failedCount}人`);
    this.logger.log(`   - 执行耗时: ${executionTime}ms`);
    this.logger.log(`   - 平均每人耗时: ${Math.round(executionTime / students.length)}ms`);

    return {
      executionTime,
      totalPoints,
      successCount,
      failedCount,
      results,
      insufficientStudents // 返回积分不足的学生详细信息
    };
  }



  /**
   * 应用权限模板（在事务中执行）
   */
  private async applyPermissionTemplate(students: any[], templateId: number, manager: any) {
    try {
      // 1.初始日志记录值
      let successCount = 0;
      let failCount = 0;
      const results: Array<{ userId: number; roleId: number; success: boolean; message: string }> = [];

      // 2. 验证角色是否存在且启用（学生角色ID为1）
      const roleId = 1;
      const role = await manager.query(
        'SELECT id, status FROM user_role WHERE id = ? AND status = 1',
        [roleId]
      );

      this.logger.debug(`验证角色是否存在user_role表且启用: 角色ID=${roleId}, 结果=${role.length > 0 ? '存在' : '不存在'}`);
      if (!role.length) {
        this.logger.error(`学生角色不存在或已被禁用: 角色ID=${roleId}`);
        throw new Error('学生角色不存在或已被禁用');
      }

      // 3. 逐个处理每个学生的权限模板分配
      const failedStudents: any[] = [];

      for (let i = 0; i < students.length; i++) {
        const student = students[i];
        try {
          const userId = student.id;
          this.logger.debug(`开始处理学生ID ${userId} 的权限模板应用: 待分配的模板ID=${templateId}`);

          // 🧪 测试模式：模拟前3个学生权限模板应用失败
          const openTest = false
          if ((this.getTestRollbackStep() === '7' || openTest || this.Allerror) && i < 3) {
            console.log("正在模拟前3个学生权限模板应用失败");

            failedStudents.push(student);
            throw new Error(`模板服务暂时不可用`);
          }

          // 4.检查是否已存在关联记录，有的话就更新，没有就新增
          const existingRelation = await manager.query(
            'SELECT id, templateId FROM user_join_role WHERE userId = ? AND roleId = ?',
            [userId, roleId]
          );

          if (existingRelation.length > 0) {
            this.logger.debug(`学生ID ${userId} 已存在user_join_role记录: ID=${existingRelation[0].id}, 当前模板ID=${existingRelation[0].templateId}`);
            // 更新现有关联的模板ID
            await manager.query(
              'UPDATE user_join_role SET templateId = ?, originalTemplateId = ?, updateTime = NOW() WHERE userId = ? AND roleId = ?',
              [templateId, templateId, userId, roleId]
            );
          } else {
            this.logger.debug(`学生ID ${userId} 不存在user_join_role记录`);
            // 创建新的用户角色关联
            await manager.query(
              `INSERT INTO user_join_role (userId, roleId, templateId, originalTemplateId, createTime, updateTime)
               VALUES (?, ?, ?, ?, NOW(), NOW())`,
              [userId, roleId, templateId, templateId]
            );
          }

          results.push({
            userId,
            roleId,
            success: true,
            message: '权限模板应用成功'
          });
          successCount++;

        } catch (error) {
          this.logger.error(`学生ID ${student.id} 权限模板应用失败: ${error.message}`);
          results.push({
            userId: student.id,
            roleId,
            success: false,
            message: error.message
          });
          failCount++;
          // 不立即抛出异常，继续处理其他学生
        }
      }

      this.logger.log(`事务中权限模板应用完成: 模板ID=${templateId}, 学生数=${students.length}, 成功${successCount}人, 失败${failCount}人`);

      // 如果有失败的学生，抛出 PartialFailureException
      if (failedStudents.length > 0) {
        this.logger.warn(`权限模板应用部分失败，${failedStudents.length}名学生失败`);
        throw new PartialFailureException({
          templateApplied: false, // 部分失败
          pointsAllocated: true,  // 前面的积分分配成功了
          tasksCreated: false,    // 还没到任务创建阶段
          details: {
            successfulOperations: [
              { operation: '积分分配', affectedStudents: students.length }
            ],
            failedOperations: [
              {
                operation: '权限模板应用',
                affectedStudents: failedStudents.length,
                error: `${failedStudents.length}名学生权限模板应用失败`,
                failedStudents: failedStudents.map(s => ({ id: s.id, name: s.name || `学生${s.id}` }))
              }
            ]
          }
        });
      }

      return {
        successCount,
        failCount,
        results
      };
    } catch (error) {
      this.logger.error(`权限模板应用失败: ${error.message}`);
      throw error; // 直接抛出原始异常，保留 failedStudents 信息
    }
  }

  /**
   * 创建任务（在事务中执行）
   */
  private async createTasks(classId: number, students: any[], taskTemplates: any[], teacherId: number, manager: any): Promise<CreatedTaskInfo[]> {
    // 1. 准备返回值
    const createdTasks: CreatedTaskInfo[] = [];

    for (const template of taskTemplates) {
      try {
        // 2. 准备任务创建数据
        const startDate = new Date();
        const endDate = new Date(Date.now() + template.durationDays * 24 * 60 * 60 * 1000);
        const validStudentIds = students.map(s => s.id).filter(id => id);

        // 3. 在事务中创建教师任务
        const taskResult = await manager.query(
          `INSERT INTO teacher_task (
            taskName, taskDescription, teacherId, classId, taskType, priority,
            startDate, endDate, taskContent, allowLateSubmission, attachments,
            workIdsStr, status, isPublic, createTime, updateTime
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          [
            template.taskName,
            template.taskDescription,
            teacherId,
            classId,
            TaskType.GRAPHIC, // 默认为图形化任务
            Priority.IMPORTANT, // 默认优先级
            startDate,
            endDate,
            template.taskDescription || '',
            0, // 默认不允许迟交 (0-否 1-是)
            JSON.stringify(template.attachments || []),
            template.workIdsStr || null,
            TaskStatus.NOT_STARTED, // 默认未开始
            0 // 默认不公开
          ]
        );

        // 4. 获取新创建的任务ID
        this.logger.debug(`任务创建成功: 任务名=${template.taskName}, 任务ID=${taskResult.insertId}`);
        const taskId = taskResult.insertId;

        // 5. 在事务中创建任务分配记录
        if (validStudentIds.length > 0) {
          // 🧪 测试模式：模拟后2个学生任务分配失败
          let successfulStudentIds = validStudentIds;
          const failedStudentIds: number[] = [];
          // 测试模式开关
          const openTest = false; // 启用测试模式
          if (this.getTestRollbackStep() === '8' || openTest || this.Allerror) {
            // 模拟后2个学生任务分配失败
            successfulStudentIds = validStudentIds.slice(0, -2);
            failedStudentIds.push(...validStudentIds.slice(-2));

            if (failedStudentIds.length > 0) {
              // 抛出 PartialFailureException
              const failedStudents = students.filter(s => failedStudentIds.includes(s.id));
              this.logger.warn(`任务创建部分失败，${failedStudents.length}名学生失败`);
              throw new PartialFailureException({
                templateApplied: true,  // 权限模板应用成功了
                pointsAllocated: true,  // 积分分配成功了
                tasksCreated: false,    // 任务创建部分失败
                details: {
                  successfulOperations: [
                    { operation: '积分分配', affectedStudents: students.length },
                    { operation: '权限模板应用', affectedStudents: students.length }
                  ],
                  failedOperations: [
                    {
                      operation: '任务创建',
                      affectedStudents: failedStudents.length,
                      error: '系统繁忙，任务创建失败',
                      failedStudents: failedStudents.map(s => ({ id: s.id, name: s.name || `学生${s.id}` }))
                    }
                  ]
                }
              });
            }
          }

          if (successfulStudentIds.length > 0) {
            const assignmentValues = successfulStudentIds.map(studentId =>
              `(${taskId}, ${studentId}, 0, NOW(), NOW())`
            ).join(', ');

            await manager.query(
              `INSERT INTO teacher_task_assignment (taskId, studentId, taskStatus, createTime, updateTime)
               VALUES ${assignmentValues}`
            );
          }
        }

        // 6. 在事务中创建自评项（如果有），写表
        if (template.selfAssessmentItems && Array.isArray(template.selfAssessmentItems)) {
          // 使用参数化查询避免SQL注入
          for (let index = 0; index < template.selfAssessmentItems.length; index++) {
            const item = template.selfAssessmentItems[index];
            const content = item.content || item;
            const sequence = item.sequence || index + 1;

            await manager.query(
              `INSERT INTO task_self_assessment_item (task_id, content, sequence, score_sum, rated_count)
               VALUES (?, ?, ?, ?, ?)`,
              [taskId, content, sequence, 0, 0]
            );
          }
        }

        // 7. 记录创建的任务信息
        createdTasks.push({
          taskId,
          taskName: template.taskName,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          assignedStudents: validStudentIds.length,
        });

        this.logger.log(`事务中成功创建任务: ${template.taskName}, ID=${taskId}`);
        this.logger.log(`   - 任务开始时间: ${startDate.toISOString()}`);
        this.logger.log(`   - 任务结束时间: ${endDate.toISOString()}`);
        this.logger.log(`   - 任务分配学生数: ${validStudentIds.length}`);
      } catch (error) {
        this.logger.error(`任务创建失败: ${template.taskName}, ${error.message}`);
        throw new Error(`任务创建失败: ${template.taskName}`);
      }
    }

    return createdTasks;
  }

  /**
   * 更新教学记录为成功状态（必须在事务中执行）
   */
  private async updateTeachingRecordSuccess(
    record: CourseTeachingRecord,
    executionResult: any,
    totalExecutionTime: number,
    manager: any
  ) {
    const updateData = {
      status: TeachingStatus.SUCCESS,
      pointsAllocated: executionResult.pointsAllocated,
      tasksCreated: executionResult.tasksCreated,
      templateApplied: executionResult.templateApplied ? TemplateAppliedStatus.YES : TemplateAppliedStatus.NO,
      executionDetails: executionResult,
      totalExecutionTime,
    };

    // 必须在事务中更新
    await manager.update(CourseTeachingRecord, record.id, updateData);
  }

  // /**
  //  * 更新教学记录为部分成功状态（必须在事务中执行）
  //  */
  // private async updateTeachingRecordPartialSuccess(
  //   record: CourseTeachingRecord,
  //   partialData: any,
  //   totalExecutionTime: number,
  //   manager: any
  // ) {
  //   const updateData = {
  //     status: TeachingStatus.PARTIAL_SUCCESS,
  //     pointsAllocated: partialData.pointsAllocated || 0,
  //     tasksCreated: partialData.tasksCreated || 0,
  //     templateApplied: partialData.templateApplied ? TemplateAppliedStatus.YES : TemplateAppliedStatus.NO,
  //     executionDetails: partialData,
  //     totalExecutionTime,
  //   };

  //   // 必须在事务中更新
  //   await manager.update(CourseTeachingRecord, record.id, updateData);
  // }

  /**
   * 更新教学记录为失败状态（必须在事务中执行）
   */
  private async updateTeachingRecordFailed(
    record: CourseTeachingRecord,
    errorMessage: string,
    totalExecutionTime: number,
    manager: any
  ) {
    const updateData = {
      status: TeachingStatus.FAILED,
      errorMessage,
      totalExecutionTime,
    };

    // 必须在事务中更新
    await manager.update(CourseTeachingRecord, record.id, updateData);
  }

  /**
   * 构建成功响应
   */
  private buildSuccessResponse(
    record: CourseTeachingRecord,
    executionResult: any,
    lockAcquireTime: number,
    totalExecutionTime: number
  ): OneClickStartDataDto {
    return {
      // 1.成功状态值
      success: true,
      // 2.教师记录id
      teachingRecordId: record.id,
      // 3.分配的总积分（用户需要根据学生数量和每个分配的大小进行计算）
      pointsAllocated: executionResult.pointsAllocated,
      // 4.成功创建的任务数量，同学生数量一致！
      tasksCreated: executionResult.tasksCreated,
      // 5.是否应用了模板
      templateApplied: executionResult.templateApplied,
      // 6.执行时间记录
      executionTime: new Date().toISOString(),
      // 7.锁获取和总执行时间
      lockAcquireTime,
      // 8.总执行时间
      totalExecutionTime,
      // 9.详细信息
      details: {
        // 10.细节
        ...executionResult.details,
        // 11.创建的任务列表
        createdTasks: executionResult.createdTasks,
        // 12.失败的操作列表（用于错误记录给前端看）
        failedOperations: executionResult.failedOperations,
        // 13.警告信息列表
        warningMessages: executionResult.warningMessages,
      },
    };
  }

  /**
   * 获取课程设置信息
   */
  async getCourseSettings(courseId: number): Promise<CourseSettingsDataDto> {
    // 1. 获取课程基本信息
    console.log("进入获取课程设置信息接口调用");
    const course = await this.courseRepository
      .createQueryBuilder('course')
      .leftJoinAndSelect('course.series', 'series')
      .where('course.id = :courseId', { courseId })
      .getOne();
    console.log("获取课程基本信息:", course);
    if (!course) {
      throw new BadRequestException('课程不存在');
    }

    // 2. 获取课程设置
    const courseSettings = await this.courseSettingsRepository.findOne({
      where: { courseId },
    });

    // 3. 获取任务模板
    const taskTemplates = await this.taskTemplateRepository.find({
      where: { courseId },
      order: { id: 'ASC' },
    });

    // 4. 构建响应数据
    return this.buildCourseSettingsResponse(course, courseSettings, taskTemplates);
  }

  /**
   * 构建课程设置响应数据
   */
  private buildCourseSettingsResponse(
    course: any,
    courseSettings: any,
    taskTemplates: any[]
  ): CourseSettingsDataDto {
    // 构建内容信息
    const contentInfo: CourseContentInfoDto = {
      hasVideo: course.hasVideo,
      hasDocument: course.hasDocument,
      hasAudio: course.hasAudio,
      videoDuration: course.videoDuration,
      videoDurationLabel: this.formatVideoDuration(course.videoDuration),
      videoName: this.extractVideoName(course.contentConfig),
      resourcesCount: this.calculateResourcesCount(course),
    };

    // 构建设置信息
    const settings: CourseSettingsInfoDto = {
      templateId: courseSettings?.templateId || 0,
      templateName: courseSettings?.templateId ? `模板${courseSettings.templateId}` : '无模板',
      requiredPoints: courseSettings?.requiredPoints || 0,
      autoCreateTasks: courseSettings?.autoCreateTasks || 0,
      autoCreateTasksLabel: courseSettings?.autoCreateTasks ? '是' : '否',
    };

    // 构建任务模板信息
    const taskTemplateInfos: TaskTemplateInfoDto[] = taskTemplates.map(template => ({
      id: template.id,
      taskName: template.taskName,
      taskDescription: template.taskDescription,
      durationDays: template.durationDays,
      status: 1, // 假设都是启用状态
      statusLabel: '启用',
      attachmentsCount: this.getAttachmentsCount(template.attachments),
      assessmentItemsCount: this.getAssessmentItemsCount(template.selfAssessmentItems),
      firstAttachmentType: this.getFirstAttachmentType(template.attachments),
    }));

    // 构建预览信息
    const preview: CoursePreviewInfoDto = {
      willAllocatePoints: (courseSettings?.requiredPoints || 0) > 0,
      pointsPerStudent: courseSettings?.requiredPoints || 0,
      willApplyTemplate: !!courseSettings?.templateId,
      willCreateTasks: !!(courseSettings?.autoCreateTasks && taskTemplates.length > 0),
      tasksCount: courseSettings?.autoCreateTasks ? taskTemplates.length : 0,
    };

    return {
      courseId: course.id,
      courseName: course.title,
      seriesName: course.series?.title || '未知系列',
      contentInfo,
      settings,
      taskTemplates: taskTemplateInfos,
      preview,
    };
  }

  /**
   * 格式化视频时长
   */
  private formatVideoDuration(seconds: number): string {
    return CalculationUtils.formatVideoDuration(seconds);
  }

  /**
   * 提取视频文件名
   */
  private extractVideoName(contentConfig: any): string {
    if (!contentConfig || !contentConfig.video) return '';
    return contentConfig.video.name || contentConfig.video.url?.split('/').pop() || '';
  }

  /**
   * 计算资源数量
   */
  private calculateResourcesCount(course: any): number {
    let count = 0;
    if (course.hasVideo) count++;
    if (course.hasDocument) count++;
    if (course.hasAudio) count++;

    // 如果有额外资源配置，也计算在内
    if (course.additionalResources && Array.isArray(course.additionalResources)) {
      count += course.additionalResources.length;
    }

    return count;
  }

  /**
   * 获取附件数量
   */
  private getAttachmentsCount(attachments: any): number {
    if (!attachments) return 0;
    if (Array.isArray(attachments)) return attachments.length;
    return 0;
  }

  /**
   * 获取自评项数量
   */
  private getAssessmentItemsCount(assessmentItems: any): number {
    if (!assessmentItems) return 0;
    if (Array.isArray(assessmentItems)) return assessmentItems.length;
    return 0;
  }

  /**
   * 获取第一个附件类型
   */
  private getFirstAttachmentType(attachments: any): string {
    if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
      return '';
    }

    const firstAttachment = attachments[0];
    return firstAttachment.type || 'file';
  }

  /**
   * 查询教学记录
   */
  async getTeachingRecords(query: GetTeachingRecordsQueryDto): Promise<TeachingRecordsDataDto> {
    const { page = 1, pageSize = 10 } = query;

    // 构建查询条件
    const queryBuilder = this.teachingRecordRepository
      .createQueryBuilder('record')
      .leftJoinAndSelect('record.course', 'course')
      .leftJoinAndSelect('course.series', 'series');

    // 添加筛选条件
    this.applyFilters(queryBuilder, query);

    // 添加排序
    queryBuilder.orderBy('record.createdAt', 'DESC');

    // 计算分页信息
    const total = await queryBuilder.getCount();
    const paginationInfo = CalculationUtils.calculatePaginationInfo(page, pageSize, total);

    // 应用分页
    queryBuilder
      .skip(paginationInfo.skip)
      .take(pageSize);

    // 执行查询
    const records = await queryBuilder.getMany();

    // 构建响应数据
    const list = await this.buildTeachingRecordList(records);

    return {
      list,
      pagination: {
        page: paginationInfo.page,
        pageSize: paginationInfo.pageSize,
        total: paginationInfo.total,
        totalPages: paginationInfo.totalPages,
        hasNext: paginationInfo.hasNext,
        hasPrev: paginationInfo.hasPrev,
      },
    };
  }

  /**
   * 应用查询筛选条件
   */
  private applyFilters(queryBuilder: any, query: GetTeachingRecordsQueryDto): void {
    // 教师ID筛选
    if (query.teacherId) {
      queryBuilder.andWhere('record.teacherId = :teacherId', { teacherId: query.teacherId });
    }

    // 课程ID筛选
    if (query.courseId) {
      queryBuilder.andWhere('record.courseId = :courseId', { courseId: query.courseId });
    }

    // 班级ID筛选
    if (query.classId) {
      queryBuilder.andWhere('record.classId = :classId', { classId: query.classId });
    }

    // 状态筛选
    if (query.status !== undefined && query.status !== null) {
      queryBuilder.andWhere('record.status = :status', { status: query.status });
    }

    // 日期范围筛选
    if (query.startDate) {
      queryBuilder.andWhere('DATE(record.createdAt) >= :startDate', { startDate: query.startDate });
    }

    if (query.endDate) {
      queryBuilder.andWhere('DATE(record.createdAt) <= :endDate', { endDate: query.endDate });
    }
  }

  /**
   * 构建教学记录列表
   */
  private async buildTeachingRecordList(records: CourseTeachingRecord[]): Promise<TeachingRecordInfoDto[]> {
    const list: TeachingRecordInfoDto[] = [];

    for (const record of records) {
      // 获取班级信息（模拟调用外部API）
      const classInfo = await this.getClassInfoForRecord(record.classId);

      // 获取教师信息（模拟调用外部API）
      const teacherInfo = await this.getTeacherInfoForRecord(record.teacherId);

      const recordInfo: TeachingRecordInfoDto = {
        id: record.id,
        courseId: record.courseId,
        courseName: record.course?.title || '未知课程',
        seriesName: record.course?.series?.title || '未知系列',
        classId: record.classId,
        className: classInfo.name,
        teacherId: record.teacherId,
        teacherName: teacherInfo.name,
        status: record.status,
        statusLabel: TeachingStatusUtils.getStatusLabel(record.status),
        pointsAllocated: record.pointsAllocated,
        tasksCreated: record.tasksCreated,
        templateApplied: record.templateApplied,
        lockAcquireTime: record.lockAcquireTime,
        totalExecutionTime: record.totalExecutionTime,
        errorMessage: record.errorMessage,
        createdAt: record.createdAt.toISOString(),
        updatedAt: record.updatedAt.toISOString(),
      };

      list.push(recordInfo);
    }

    return list;
  }

  /**
   * 获取班级信息（用于记录显示）
   */
  private async getClassInfoForRecord(classId: number) {
    try {
      // 获取班级信息
      const classInfo = await this.baseUserClassService.findOne(+classId);

      if (!classInfo) {
        this.logger.warn(`班级信息不存在: classId=${classId}`);
        return {
          id: classId,
          name: `班级${classId}`,
        };
      }

      return {
        id: classInfo.id,
        name: classInfo.className,
      };
    } catch (error) {
      this.logger.warn(`获取班级信息失败: classId=${classId}, ${error.message}`);
      return {
        id: classId,
        name: `班级${classId}`,
      };
    }
  }

  /**
   * 获取教师信息（用于记录显示）
   */
  private async getTeacherInfoForRecord(teacherId: number) {
    try {
      // 这里应该调用实际的用户信息API
      // const response = await firstValueFrom(this.httpService.get(`/api/user/info/${teacherId}`));

      // 模拟返回教师信息
      return {
        id: teacherId,
        name: `教师${teacherId}`,
      };
    } catch (error) {
      this.logger.warn(`获取教师信息失败: teacherId=${teacherId}, ${error.message}`);
      return {
        id: teacherId,
        name: `教师${teacherId}`,
      };
    }
  }
}
