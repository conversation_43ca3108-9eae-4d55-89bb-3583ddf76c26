const path = require("path");
const webpack = require("webpack");
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
// Plugins
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
// 添加压缩相关插件
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
// 添加Brotli压缩插件
const zlib = require('zlib');

const ScratchWebpackConfigBuilder = require("scratch-webpack-configuration");

// const STATIC_PATH = process.env.STATIC_PATH || '/static';

const baseConfig = new ScratchWebpackConfigBuilder({
    rootPath: path.resolve(__dirname),
    enableReact: true,
    shouldSplitChunks: true, // 启用代码分割
})
    .setTarget("browserslist")
    .merge({
        output: {
            assetModuleFilename: "static/assets/[name].[hash][ext][query]",
            library: {
                name: "GUI",
                type: "umd2",
            },
            // 为分割后的代码添加输出路径
            publicPath: '/logicleap/',
            filename: 'static/js/[name].[contenthash].js',
            chunkFilename: 'static/js/[name].[contenthash].js',
        },
        performance: {
            hints: false, // 关闭性能提示
        },
        // 添加错误处理配置
        ignoreWarnings: [
            {
                module: /node_modules/,
                message: /ResizeObserver/,
            },
            /ResizeObserver loop/,
            (warning) => {
                try {
                    if (
                        warning.message &&
                        warning.message.includes("ResizeObserver")
                    ) {
                        return true;
                    }
                } catch (error) {
                    console.error("ResizeObserver warning:", error);
                }
                return false;
            },
        ],
        // 添加优化配置
        optimization: {
            minimize: true,
            minimizer: [
                new TerserPlugin({
                    terserOptions: {
                        compress: {
                            drop_console: process.env.NODE_ENV === 'production', // 仅生产环境移除console
                            drop_debugger: process.env.NODE_ENV === 'production', // 仅生产环境移除debugger
                        },
                        output: {
                            comments: false // 移除注释
                        }
                    },
                    extractComments: false
                }),
                new CssMinimizerPlugin()
            ],
            // 配置代码分割
            splitChunks: {
                chunks: 'all',
                maxInitialRequests: 10,
                maxAsyncRequests: 10,
                minSize: 20000,
                cacheGroups: {
                    // 基础库
                    vendors: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        priority: -10,
                        chunks: 'initial',
                        reuseExistingChunk: true
                    },
                    // React相关
                    react: {
                        test: /[\\/]node_modules[\\/](react|react-dom|prop-types)[\\/]/,
                        name: 'react',
                        priority: 0,
                        reuseExistingChunk: true
                    },
                    // TensorFlow相关 - 改为异步加载
                    tensorflow: {
                        test: /[\\/]node_modules[\\/]@tensorflow/,
                        name: 'tensorflow',
                        priority: 10,
                        // chunks: 'async', // 改为async实现按需加载
                        reuseExistingChunk: true
                    },
                    // 共用模块
                    commons: {
                        name: 'commons',
                        minChunks: 2,
                        priority: -20,
                        reuseExistingChunk: true
                    }
                }
            },
            // 启用模块连接优化
            concatenateModules: true,
            // 启用副作用分析
            sideEffects: true,
            // 保持chunk名称
            chunkIds: 'deterministic',
            // 保持模块名称
            moduleIds: 'deterministic'
        },
        resolve: {
            extensions: [".js", ".jsx", ".ts", ".tsx"],
            fallback: {
                buffer: require.resolve("buffer/"),
                stream: require.resolve("stream-browserify"),
            },
            alias: {
                "@scratch-vm": path.resolve(
                    __dirname,
                    "node_modules/scratch-vm/src"
                ),
                "@extensions": path.resolve(
                    __dirname,
                    "src/lib/libraries/extensions"
                ),
                "@utils": path.resolve(__dirname, "src/utils"),
                "@blocks": path.resolve(__dirname, "src/lib/libraries/blocks"),
                "@api": path.resolve(__dirname, "src/api"),
            },
        },
    })
    // 在处理资源文件的规则之后，添加针对 TS/TSX 的 Babel Loader 规则
    .addModuleRule({
        test: /\.(ts|tsx)$/,
        loader: "babel-loader",
        include: [
            path.resolve(__dirname, "src"),
            path.resolve(__dirname, "api"),
        ], // 只处理 src 和 api 目录
        options: {
            // Babel 配置会从 .babelrc 文件读取
        },
    })
    .addModuleRule({
        test: /\.(svg|png|wav|mp3|gif|jpg)$/,
        resourceQuery: /^$/, // reject any query string
        type: "asset", // let webpack decide on the best type of asset
    })
    .addPlugin(
        new webpack.ProvidePlugin({
            Buffer: ["buffer", "Buffer"],
        })
    )
    .addPlugin(
        new webpack.DefinePlugin({
            "process.env.DEBUG": Boolean(process.env.DEBUG),
            "process.env.GA_ID": `"${process.env.GA_ID || "UA-000000-01"}"`,
            "process.env.GTM_ENV_AUTH": `"${process.env.GTM_ENV_AUTH || ""}"`,
            "process.env.GTM_ID": process.env.GTM_ID
                ? `"${process.env.GTM_ID}"`
                : null,
        })
    )
    // 添加自定义错误处理插件
    .addPlugin(
        new webpack.IgnorePlugin({
            resourceRegExp: /ResizeObserver/,
        })
    )
    // 添加错误处理插件
    .addPlugin(
        new webpack.DefinePlugin({
            __SUPPRESS_RESIZE_OBSERVER_ERROR__: JSON.stringify(true),
        })
    )
    .addPlugin(
        new webpack.BannerPlugin({
            banner: `
            window.addEventListener('error', function(e) {
                try{
                    if (e.message.includes('ResizeObserver')) {
                        e.stopImmediatePropagation();
                        return false;
                    }
                }catch(error){
                    console.log(error);
                }
            }, true);
        `,
            raw: true,
            entryOnly: true,
        })
    )
    // 添加Gzip压缩
    .addPlugin(
        new CompressionPlugin({
            filename: '[path][base].gz',
            algorithm: 'gzip',
            test: /\.(js|css|html|svg)$/,
            threshold: 10240, // 大于10KB的文件才压缩
            minRatio: 0.8
        })
    )
    // 添加Brotli压缩 - 效果更好
    .addPlugin(
        new CompressionPlugin({
            filename: '[path][base].br',
            algorithm: 'brotliCompress',
            test: /\.(js|css|html|svg)$/,
            compressionOptions: {
                params: {
                    [zlib.constants.BROTLI_PARAM_QUALITY]: 11,
                    [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,
                    [zlib.constants.BROTLI_PARAM_SIZE_HINT]: 0
                }
            },
            threshold: 10240,
            minRatio: 0.8,
            // 强制开发环境也使用Brotli
            deleteOriginalAssets: false
        })
    )
    // 添加缓存控制相关插件
    .addPlugin(
        new webpack.LoaderOptionsPlugin({
            options: {
                output: {
                    publicPath: '/logicleap/'
                }
            }
        })
    )
    .addPlugin(
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: "node_modules/scratch-blocks/media",
                    to: "static/blocks-media/default",
                },
                {
                    from: "node_modules/scratch-blocks/media",
                    to: "static/blocks-media/high-contrast",
                },
                {
                    // overwrite some of the default block media with high-contrast versions
                    // this entry must come after copying scratch-blocks/media into the high-contrast directory
                    from: "src/lib/themes/high-contrast/blocks-media",
                    to: "static/blocks-media/high-contrast",
                    force: true,
                },
                {
                    context: "node_modules/scratch-vm/dist/web",
                    from: "extension-worker.{js,js.map}",
                    noErrorOnMissing: true,
                },
            ],
        })
    );

if (!process.env.CI) {
    baseConfig.addPlugin(new webpack.ProgressPlugin());
}

// 添加分析插件
if (process.env.ANALYZE) {
    baseConfig.addPlugin(
        new BundleAnalyzerPlugin({
            analyzerMode: 'server',
            analyzerPort: 8888,
        })
    );
}

// build the shipping library in `dist/`
const distConfig = baseConfig
    .clone()
    .merge({
        entry: {
            "scratch-gui": path.join(__dirname, "src/index.js"),
        },
        output: {
            path: path.resolve(__dirname, "dist"),
        },
    })
    .addPlugin(
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: "src/lib/libraries/*.json",
                    to: "libraries",
                    flatten: true,
                },
            ],
        })
    );

// build the examples and debugging tools in `build/`
const buildConfig = baseConfig
    .clone()
    .merge({
        devServer: {
            port: 8601,
            host: "0.0.0.0",
            allowedHosts: "all",
            static: {
                directory: path.join(__dirname, "build"),
            },
            hot: true, // Enable Hot Module Replacement (HMR)
            liveReload: true, // Enable live reloading when files change
            devMiddleware: {
                writeToDisk: true, // Write files to disk in dev mode for better HMR support
            },
            // 添加这个配置来指定 WebSocket 连接地址
            client: {
                webSocketURL:
                    process.env.NODE_ENV === "production"
                        ? "wss://logicleapai.cn/ws"
                        : "ws://127.0.0.1:8001/ws",
                progress: true, // Show progress while compiling
                overlay: true, // Show error overlay
            },
            proxy: [
                {
                    context: [
                        "/api",
                        "/ai-model.js",
                        "/uploads",
                        "/recordings",
                        "/cache",
                    ],
                    target:
                        process.env.NODE_ENV === "production"
                            ? "https://www.logicleapai.cn/server" // 生产环境
                            : "http://127.0.0.1:8602", // 开发环境
                    secure: false,
                    changeOrigin: true,
                },
            ],
            // 添加HTTP头配置
            headers: {
                'Cache-Control': 'public, max-age=31536000',
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': 'DENY',
                'X-XSS-Protection': '1; mode=block',
            },
            // 添加对Brotli的支持
            compress: true,
            setupMiddlewares: function(middlewares, devServer) {
                if (!devServer) {
                    throw new Error('webpack-dev-server is not defined');
                }

                const express = require('express');
                const fs = require('fs');
                const path = require('path');

                // 中间件：优先提供.br文件
                devServer.app.use((req, res, next) => {
                    if (req.path.match(/\.(js|css|svg|json)$/)) {
                        const acceptEncoding = req.headers['accept-encoding'] || '';
                        
                        if (acceptEncoding.includes('br')) {
                            const brPath = path.join(
                                path.resolve(__dirname, 'build'), 
                                req.path + '.br'
                            );
                            
                            // 检查.br文件是否存在
                            try {
                                if (fs.existsSync(brPath)) {
                                    // 设置正确的Content-Type
                                    const ext = path.extname(req.path);
                                    const contentTypes = {
                                        '.js': 'application/javascript',
                                        '.css': 'text/css',
                                        '.svg': 'image/svg+xml',
                                        '.json': 'application/json'
                                    };
                                    
                                    res.set({
                                        'Content-Encoding': 'br',
                                        'Content-Type': contentTypes[ext] || 'text/plain',
                                        'Vary': 'Accept-Encoding',
                                        'Cache-Control': 'public, max-age=2592000, immutable'
                                    });
                                    
                                    // 发送.br文件
                                    fs.createReadStream(brPath).pipe(res);
                                    return;
                                }
                            } catch (err) {
                                console.error('Error checking for Brotli file:', err);
                            }
                        }
                    }
                    next();
                });
                
                return middlewares;
            },
        },
        entry: {
            gui: "./src/playground/index.jsx",
            blocksonly: "./src/playground/blocks-only.jsx",
            compatibilitytesting: "./src/playground/compatibility-testing.jsx",
            player: "./src/playground/player.jsx",
        },
        output: {
            path: path.resolve(__dirname, "build"),
        },
    })
    .addPlugin(
        new HtmlWebpackPlugin({
            chunks: ["gui"],
            template: "src/playground/index.ejs",
            title: "LogicLeapingAI",
            minify: {
                //删除 HTML 标签之间的空白字符和换行符，减少文件体积，不影响页面渲染
                collapseWhitespace: true,
                //删除 HTML 中的所有注释，减少文件体积，移除开发辅助信息
                removeComments: true,
                //删除 HTML 标签中的冗余属性（如与浏览器默认值相同的属性）
                removeRedundantAttributes: true,
                //使用短版本的文档类型声明：<!DOCTYPE html> 替换冗长的 HTML4/XHTML doctype
                useShortDoctype: true,
                //删除值为空的属性
                removeEmptyAttributes: true,
                //压缩内联 JavaScript 代码，移除空格、注释和不必要的代码
                minifyJS: true,
                //压缩内联 CSS 样式，移除空格、注释和优化样式声明
                minifyCSS: true
            }
        })
    )
    .addPlugin(
        new HtmlWebpackPlugin({
            chunks: ["blocksonly"],
            filename: "blocks-only.html",
            template: "src/playground/index.ejs",
            title: "LogicLeapingAI: Blocks Only Example",
            minify: {
                collapseWhitespace: true,
                removeComments: true,
                removeRedundantAttributes: true,
                useShortDoctype: true,
                removeEmptyAttributes: true,
                minifyJS: true,
                minifyCSS: true
            }
        })
    )
    .addPlugin(
        new HtmlWebpackPlugin({
            chunks: ["compatibilitytesting"],
            filename: "compatibility-testing.html",
            template: "src/playground/index.ejs",
            title: "LogicLeapingAI: Compatibility Testing",
        })
    )
    .addPlugin(
        new HtmlWebpackPlugin({
            chunks: ["player"],
            filename: "player.html",
            template: "src/playground/index.ejs",
            title: "LogicLeapingAI: Player Example",
        })
    )
    .addPlugin(
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: "static",
                    to: "static",
                },
                {
                    from: "extensions/**",
                    to: "static",
                    context: "src/examples",
                },
                {
                    from: "utils",
                    to: "static/utils/image_train",
                    context:
                        "src/lib/libraries/extensions/logicleap_image_train",
                },
                {
                    from: "utils",
                    to: "static/utils/sound_train",
                    context:
                        "src/lib/libraries/extensions/logicleap_sound_train",
                },
                {
                    from: "utils",
                    to: "static/utils/pose_train",
                    context:
                        "src/lib/libraries/extensions/logicleap_pose_train",
                },
                {
                    from: "utils",
                    to: "static/utils",
                    context:
                        "src/lib/libraries/extensions"
                }
            ],
        })
    )
    // 添加HTML缓存控制插件
    .addPlugin(
        new webpack.DefinePlugin({
            'process.env.BUILD_TIMESTAMP': JSON.stringify(Date.now())
        })
    );

// Skip building `dist/` unless explicitly requested
// It roughly doubles build time and isn't needed for `scratch-gui` development
// If you need non-production `dist/` for local dev, such as for `scratch-www` work, you can run something like:
// `BUILD_MODE=dist npm run build`
const buildDist =
    process.env.NODE_ENV === "production" || process.env.BUILD_MODE === "dist";

module.exports = buildDist
    ? [buildConfig.get(), distConfig.get()]
    : buildConfig.get();
