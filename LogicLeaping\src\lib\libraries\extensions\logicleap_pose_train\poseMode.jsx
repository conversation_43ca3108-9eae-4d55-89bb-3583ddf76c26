import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import PropTypes from 'prop-types';
import * as tf from '@tensorflow/tfjs';
// 删除Chart.js引入
import { createSquareLoadingAnimation } from '../utils/loadingAnimation';
import TrainNotification from '../utils/trainNotification';
import InitNotification from '../utils/initNotification';
import trainMainStyle from '../utils/trainUtil/trainMain/trainMainStyle';
import trainMainCss from '../utils/trainUtil/trainMain/trainMainCss.css';
import TrainMain from '../utils/trainUtil/trainMain/trainMain.jsx';
import ClassCardCss from '../utils/trainUtil/ClassCard/ClassCardCss.css';
import ClassCardStyle from '../utils/trainUtil/ClassCard/ClassCardStyle';
import CameraCardStyle from '../utils/trainUtil/ClassCard/CameraCardStyle';

// 导入模型存储工具
import {
  saveModelToCloud as cloudSaveModel,
  downloadModelAsFile,
  saveModelToLocalStorage as saveToLocalStorage,
  loadSavedModels as loadModelsFromStorage,
  saveModelToCloudWithUI,
  importModelFromFile,
  importModelWithUI
} from './utils/modelCloudStorage';

// 导入样式
import styles, { addGlobalStyle } from './poseStyles';

// 添加绘制样本姿态线框的函数
const drawSamplePose = (canvas, keypoints) => {
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;
  
  // 清除画布
  ctx.clearRect(0, 0, width, height);
  
  // 设置背景色
  ctx.fillStyle = '#f0f2f5';
  ctx.fillRect(0, 0, width, height);
  
  // 计算缩放因子，使姿态适应画布
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
  
  keypoints.forEach(kp => {
    if (kp.score > 0.3) {
      minX = Math.min(minX, kp.x);
      minY = Math.min(minY, kp.y);
      maxX = Math.max(maxX, kp.x);
      maxY = Math.max(maxY, kp.y);
    }
  });
  
  const poseWidth = maxX - minX;
  const poseHeight = maxY - minY;
  const scaleX = (width - 16) / poseWidth;  // 增加边距
  const scaleY = (height - 16) / poseHeight; // 增加边距
  const scale = Math.min(scaleX, scaleY);
  
  // 计算居中偏移
  const offsetX = (width - poseWidth * scale) / 2;
  const offsetY = (height - poseHeight * scale) / 2;
  
  // 转换坐标函数（添加水平翻转）
  const transformX = x => (x - minX) * scale + offsetX;

  const transformY = y => (y - minY) * scale + offsetY;
  
  // 绘制骨架连接线
  const connections = [
    ['nose', 'left_eye'], ['nose', 'right_eye'],
    ['left_eye', 'left_ear'], ['right_eye', 'right_ear'],
    ['nose', 'left_shoulder'], ['nose', 'right_shoulder'],
    ['left_shoulder', 'right_shoulder'],
    ['left_shoulder', 'left_elbow'], ['right_shoulder', 'right_elbow'],
    ['left_elbow', 'left_wrist'], ['right_elbow', 'right_wrist'],
    ['left_shoulder', 'left_hip'], ['right_shoulder', 'right_hip'],
    ['left_hip', 'right_hip'],
    ['left_hip', 'left_knee'], ['right_hip', 'right_knee'],
    ['left_knee', 'left_ankle'], ['right_knee', 'right_ankle']
  ];
  
  // 创建关键点映射
  const keypointMap = {};
  keypoints.forEach(keypoint => {
    keypointMap[keypoint.name] = keypoint;
  });
  
  // 绘制连接线
  ctx.strokeStyle = '#4766C2';  // 蓝色线条
  ctx.lineWidth = 4;            // 增加线宽
  
  connections.forEach(([p1Name, p2Name]) => {
    const p1 = keypointMap[p1Name];
    const p2 = keypointMap[p2Name];
    
    if (p1 && p2 && p1.score > 0.3 && p2.score > 0.3) {
      ctx.beginPath();
      ctx.moveTo(transformX(p1.x), transformY(p1.y));
      ctx.lineTo(transformX(p2.x), transformY(p2.y));
      ctx.stroke();
    }
  });
  
  // 绘制关键点
  ctx.fillStyle = '#FF4D4F';  // 红色关键点
  keypoints.forEach(kp => {
    if (kp.score > 0.3) {
      ctx.beginPath();
      ctx.arc(transformX(kp.x), transformY(kp.y), 4, 0, 2 * Math.PI);  // 增大关键点大小
      ctx.fill();
    }
  });
};

// 根据传入的标签初始化类别节点
const getInitialClassNodes = (labels) => {
  if (labels && Array.isArray(labels) && labels.length > 0) {
      return labels.map((label, index) => ({
          id: index,
          name: label || `类别${index + 1}`, // 如果标签为空，则使用默认名称
          samples: [],
          isImported: false,
          originalIndex: index
      }));
  }
  // 默认类别
  return [
      { id: 0, name: '类别1', samples: [], isImported: false, originalIndex: 0 },
      { id: 1, name: '类别2', samples: [], isImported: false, originalIndex: 1 }
  ];
};

const PoseMode = ({modeRef, modeString, initialClassLabels}) => {
  // 状态管理
  const [classNodes, setClassNodes] = useState(() => getInitialClassNodes(initialClassLabels));
  const [isCollecting, setIsCollecting] = useState(false);
  const [currentClass, setCurrentClass] = useState(null);

  //训练参数
  const [isTraining, setIsTraining] = useState(false);
  const [training, setTraining] = useState(0);
  // 添加训练参数展开/缩放状态
  const [advancedExpanded, setAdvancedExpanded] = useState(false);
  const [DefaultTrainParams] = useState({
    epochs: 5,
    batchSize: 8,
    learningRate: 0.005
  });
  const [trainParams, setTrainParams] = useState(DefaultTrainParams);

  const canTrainRef = useRef(false);
  //训练参数

  //预览参数
  // 在组件顶部添加 state
  const [confidences, setConfidences] = useState([]);
  //预览参数

  // 添加设置参数状态
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [showCameraSettings, setShowCameraSettings] = useState(false);
  const [videoDevices, setVideoDevices] = useState([]);
  const [recordSettings, setRecordSettings] = useState({
    fps: 10,
    holdToRecord: true,
    canSpaceRecord: true, // 是否可以按空格键录制
    delayTime: 0,
    duration: 3,
    selectedDeviceId: ''
  });
  // 添加ref来同步记录settings的值，用于实时获取
  const recordSettingsRef = useRef({
    fps: 10,
    holdToRecord: true,
    canSpaceRecord: true, // 是否可以按空格键录制
    delayTime: 0,
    duration: 3,
    selectedDeviceId: ''
  });

  //trainMain里面的ref,用于调用里面的方法
  const trainMainFunctionRef = useRef(null);

  const [modelTrained, setModelTrained] = useState(false);
  const [predicting, setPredicting] = useState(false);
  const [currentPrediction, setCurrentPrediction] = useState(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [savedModels, setSavedModels] = useState([]);
  const [originalModelClasses, setOriginalModelClasses] = useState([]);
  const [classIdMapping, setClassIdMapping] = useState(() => {
    const initialNodes = getInitialClassNodes(initialClassLabels);
    const mapping = {};
    initialNodes.forEach(node => {
        mapping[node.id] = node.id;
    });
    return mapping;
  });
  // 添加新状态来跟踪每个类别节点的展开状态
  const [expandedNodes, setExpandedNodes] = useState(() => {
    const initialNodes = getInitialClassNodes(initialClassLabels);
    const initialExpanded = {};
    initialNodes.forEach(node => {
        initialExpanded[node.id] = false;
    });
    return initialExpanded;
  });
  // 添加状态来跟踪菜单显示状态
  const [showMenus, setShowMenus] = useState({});
  // 添加状态来跟踪摄像头是否已初始化
  const [isCameraInitialized, setIsCameraInitialized] = useState(false);
  const isCameraInitializedRef = useRef(false);
  // 添加一个引用来存储当前摄像头方向
  const facingModeRef = useRef('user');
  const previewFacingModeRef = useRef('user');


  // 添加历史帧数据存储
  const poseHistoryRef = useRef([]);
  const MAX_HISTORY = 3; // 保存最近3帧

  // Refs
  const containerRef = useRef(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const modelRef = useRef(null);
  const trainedModelRef = useRef(null);
  const videoStreamRef = useRef(null);
  const previewVideoRef = useRef(null);
  const previewCanvasRef = useRef(null);
  const isCollectingRef = useRef(false);
  const currentClassRef = useRef(null);
  const isPreviewActiveRef = useRef(false);
  const predictionBufferRef = useRef([]);
  const modelTrainedRef = useRef(false);
  const poseDataRef = useRef([]);
  const loadingAnimationRef = useRef(null);
  // 添加面板引用
  const classesPanelContentRef = useRef(null);
  
  //添加训练面板控制函数
  const [showDataChart, setShowDataChart] = useState(false);

  // 添加一个引用来存储上次采集的时间戳
  const lastCaptureTimeRef = useRef(0);
  // 添加一个引用来存储录制开始的时间戳
  const recordStartTimeRef = useRef(0);
  // 添加一个引用来存储录制定时器
  const recordingTimerRef = useRef(null);
  // 添加一个引用来存储当前是否通过空格键采集
  const spaceCapturingRef = useRef(false);

  // 添加训练历史状态
  const [trainingHistory, setTrainingHistory] = useState({
    epochs: [],
    trainAccuracy: [],
    trainLoss: [],
    valAccuracy: [],
    valLoss: [],
  });

  // 添加图表所需引用
  const accChartRef = useRef(null);
  const lossChartRef = useRef(null);

  // 暴露方法给父组件
  useImperativeHandle(modeRef, () => ({
    handleImportModel: () => {
      handleImportModel();
    },
    // 暴露下载模型方法
    handleExportToLocal: () => {
      handleExportToLocal();
    },
    // 暴露保存到云端方法
    handleExportToCloud: () => {
      handleExportToCloud();
    }
  }));

  // 初始化
  useEffect(() => {
    // 添加全局样式
    const removeGlobalStyle = addGlobalStyle();

    initializeModels();

    // 将焦点设置到根容器
    if (containerRef.current) {
      containerRef.current.focus();
    }

    // 保存实例引用到全局变量，便于外部访问
    window._logicleapPoseTrainInstance = {
      handleImportModel,
      handleExportToLocal,
      handleExportToCloud,
      exportModelToBlocks
    };

    // 检查是否有待导入的模型数据（来自编辑模型功能）
    console.log('检查待导入的姿态模型数据:', window._pendingPoseModelDataForEdit);
    if (window._pendingPoseModelDataForEdit) {
      console.log('发现待导入的姿态模型数据，开始处理...');
      try {
        // 保存数据到局部变量，避免被过早清理
        const pendingModelData = window._pendingPoseModelDataForEdit;

        // 立即清理全局变量
        window._pendingPoseModelDataForEdit = null;

        // 延迟执行导入，确保组件完全初始化
        setTimeout(() => {
          handlePendingModelImport(pendingModelData);
        }, 1000); // 等待1秒确保组件完全初始化

      } catch (error) {
        console.error('处理待导入姿态模型数据失败:', error);
        TrainNotification.error('处理模型数据失败');
        window._pendingPoseModelDataForEdit = null;
      }
    }

    return () => {
      // 清理资源
      stopVideoStream();

      // 移除全局样式
      removeGlobalStyle();

      // 释放模型资源
      if (trainedModelRef.current) {
        try {
          trainedModelRef.current.dispose();
          trainedModelRef.current = null;
        } catch (error) {
          console.warn('释放模型资源失败:', error);
        }
      }

      if (modelRef.current) {
        try {
          // 某些模型可能没有dispose方法，所以用try-catch包裹
          if (typeof modelRef.current.dispose === 'function') {
            modelRef.current.dispose();
          }
          modelRef.current = null;
        } catch (error) {
          console.warn('释放检测模型资源失败:', error);
        }
      }

      // 清理TensorFlow内存
      try {
        if (window.tf) {
          // window.tf.disposeVariables();
        }
      } catch (error) {
        console.warn('清理TensorFlow内存失败:', error);
      }

      // 清理全局实例
      window._logicleapPoseTrainInstance = null;
    };
  }, []);

  useEffect(() => {
    isCameraInitializedRef.current = isCameraInitialized;
  }, [isCameraInitialized]);
  
  const getVideoDevices = async () => {
    try {
        if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
            console.log("enumerateDevices() not supported.");
            return;
        }
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoInputDevices = devices.filter(device => device.kind === 'videoinput');
        setVideoDevices(videoInputDevices);
        if (videoInputDevices.length > 0 && !recordSettingsRef.current.selectedDeviceId) {
            // Use ref to check current value and set state
            setRecordSettings(prev => ({...prev, selectedDeviceId: videoInputDevices[0].deviceId}));
        }
    } catch (error) {
        console.error('获取摄像头列表失败:', error);
        showError('获取摄像头列表失败', error.message);
    }
  };

  useEffect(() => {
      if (showSettingsPanel || showCameraSettings) {
        getVideoDevices();
      }
  }, [showSettingsPanel, showCameraSettings]);

  // 在组件加载时获取摄像头设备列表
  useEffect(() => {
    getVideoDevices();
  }, []);

  useEffect(() => {
    const updateCanTrain = () => {
      // 检查是否有至少两个类别节点
      const hasEnoughClasses = classNodes.length >= 2;
      
      // 检查每个类别是否都有足够的样本（至少8个）
      const classSamples = {};
      classNodes.forEach(node => {
        classSamples[node.id] = node.samples ? node.samples.length : 0;
      });
      
      const minSamplesRequired = 10;
      const allClassesHaveSamples = Object.entries(classSamples).every(
        ([classId, count]) => count >= minSamplesRequired
      );
      
      // 检查是否有导入的模型和类别
      const importedClasses = classNodes.filter(node => node.isImported);
      const newClasses = classNodes.filter(node => !node.isImported);
      const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
      
      // 更新canTrainRef的值（根据导入模型情况使用不同判断逻辑）
      if (hasImportedModel) {
        // 如果有导入的模型，只需要至少一个新添加的类别有足够样本
        let hasEnoughSamplesForNewClass = false;
        for (const newClass of newClasses) {
          if (classSamples[newClass.id] >= minSamplesRequired) {
            hasEnoughSamplesForNewClass = true;
            break;
          }
        }
        canTrainRef.current = newClasses.length > 0 && hasEnoughSamplesForNewClass;
      } else {
        // 如果没有导入模型，需要至少两个类别，且每个类别都有足够样本
        canTrainRef.current = hasEnoughClasses && allClassesHaveSamples;
      }
      
      // 输出当前训练条件状态到控制台，帮助调试
      console.log('训练条件检查:', {
        hasEnoughClasses,
        allClassesHaveSamples,
        hasImportedModel,
        importedClassesCount: importedClasses.length,
        newClassesCount: newClasses.length,
        canTrain: canTrainRef.current,
        classCount: classNodes.length,
        samplesState: Object.entries(classSamples).map(([id, count]) => ({
          classId: id,
          sampleCount: count,
          isImported: classNodes.find(node => node.id === parseInt(id))?.isImported || false
        }))
      });
    };
    
    // 执行更新
    updateCanTrain();
    
  }, [classNodes, trainedModelRef.current]);

      // 停止视频流
  const stopVideoStream = () => {
    if (videoStreamRef.current) {
      try {
        const tracks = videoStreamRef.current.getTracks();
        tracks.forEach(track => track.stop());
        videoStreamRef.current = null;
        
        // 清除视频元素的源
        if (videoRef.current) {
          videoRef.current.srcObject = null;
          videoRef.current.pause();
        }
      } catch (error) {
        console.error('停止视频流失败:', error);
      }
    }
    
    if (previewVideoRef.current && previewVideoRef.current.srcObject) {
      try {
        const tracks = previewVideoRef.current.srcObject.getTracks();
        tracks.forEach(track => track.stop());
        previewVideoRef.current.srcObject = null;
        previewVideoRef.current.pause();
      } catch (error) {
        console.error('停止预览视频流失败:', error);
      }
    }
    
    // 重置采集状态
    if (isCollectingRef.current) {
      setIsCollecting(false);
      isCollectingRef.current = false;
      currentClassRef.current = null;
    }
  };
  
  
  // 初始化模型
  const initializeModels = async () => {
    setIsInitializing(true);
    loadingAnimationRef.current = createSquareLoadingAnimation({
      message: '正在初始化模型...',
      backgroundColor: '#4766C2',
      boxColor: '#fff',
      textColor: '#fff'
    });
    
    try {
      // 加载 TensorFlow.js
      if (!window.tf) {
        loadingAnimationRef.current.updateProgress('正在加载 TensorFlow.js...',25);
        ////console.log('加载 TensorFlow.js...');
        
        await new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = '/static/utils/pose_train/tf.min.js';
          script.crossOrigin = 'anonymous';
          
          script.onload = () => {
            ////console.log('TensorFlow.js 已加载');
            resolve();
          };
          
          script.onerror = (error) => {
            console.error('TensorFlow.js 加载失败:', error);
            console.error('加载失败的文件路径:', script.src);
            reject(error);
          };
          
          document.head.appendChild(script);
        });
      }
      
      // 加载 PoseNet
      if (!window.poseDetection) {
        loadingAnimationRef.current.updateProgress('正在加载 PoseNet 模型...',50);
        ////console.log('加载 PoseNet...');
        
        await new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = '/static/utils/pose_train/pose-detection.min.js';
          script.crossOrigin = 'anonymous';
          
          script.onload = () => {
            ////console.log('PoseNet 已加载');
            resolve();
          };
          
          script.onerror = (error) => {
            console.error('PoseNet 加载失败:', error);
            console.error('加载失败的文件路径:', script.src);
            reject(error);
          };
          
          document.head.appendChild(script);
        });
      }


      

      // 初始化 PoseNet 模型
      loadingAnimationRef.current.updateProgress('正在初始化 PoseNet 模型...',75);
      ////console.log('初始化 PoseNet 模型...');
      const model = await window.poseDetection.createDetector(
        window.poseDetection.SupportedModels.MoveNet,
        { 
          modelType: window.poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
          modelUrl: '/static/utils/pose_train/model.json'  // 指向本地模型文件
        }
      );

      modelRef.current = model;
      ////console.log('PoseNet 模型初始化完成');

      setIsInitializing(false);
      if (loadingAnimationRef.current) {
        loadingAnimationRef.current.updateProgress('模型初始化完成',100);
        loadingAnimationRef.current.updateMessage('模型初始化完成！');
        loadingAnimationRef.current.remove();
      }
    } catch (error) {
      console.error('模型初始化失败:', error);
      if (loadingAnimationRef.current) {
        loadingAnimationRef.current.remove();
      }
      showError('初始化失败', '无法加载必要的模型，请检查网络连接并刷新页面重试。');
      setIsInitializing(false);
    }
  };

  // 显示错误
  const showError = (title, message) => {
    TrainNotification.error(`${title}: ${message}`);
  };

  // 添加类别节点
  const addClassNode = () => {
    setClassNodes(prev => {
      // 检查是否已达到最大类别数量限制
      if (prev.length >= 10) {
        showMessage('最多只能创建10个类别');
        return prev;
      }

      // 计算新的ID，确保不与现有类别冲突
      const newId = prev.length > 0 ? Math.max(...prev.map(node => 
        typeof node.id === 'string' ? parseInt(node.id.replace('class-', ''), 10) : node.id
      )) + 1 : 0;
      
      // 更新类别ID映射
      setClassIdMapping(prevMapping => {
        const newMapping = {...prevMapping};
        // 新类别的UI ID映射到模型的新类别索引
        newMapping[newId] = newId;
        ////console.log(`添加新类别，ID ${newId} 映射到模型类别索引 ${newId}`);
        return newMapping;
      });
      
      // 创建新节点
      const newNode = {
        id: newId,
        name: `类别${newId + 1}`,
        samples: [],
        isImported: false, // 标记为新添加的类别
        originalIndex: newId // 保存原始索引，对于新类别，与ID相同
      };
      
      // 确保新节点默认为未展开状态
      setExpandedNodes(prev => ({
        ...prev,
        [newId]: false
      }));
      
      // 如果已经有训练好的模型，提示用户需要重新训练
      if (modelTrained) {
        showMessage('已添加新类别，需要重新训练模型');
        
        // 如果有已导入的模型，提醒用户添加新类别后需要重新训练
        if (trainedModelRef.current) {
          ////console.log('添加新类别到已导入模型，将需要重新训练全部模型');
        }
      }
      
      // 添加后自动滚动到容器底部
      setTimeout(() => {
        if (classesPanelContentRef.current) {
          classesPanelContentRef.current.scrollTop = classesPanelContentRef.current.scrollHeight;
        }
      }, 100);
      
      return [...prev, newNode];
    });
  };

  // 删除类别节点
  const deleteClassNode = (id) => {
    // 关闭菜单
    closeAllMenus();
    
    if (window.confirm(`确定要删除类别 ${id + 1} 吗？`)) {
      setClassNodes(prev => prev.filter(node => node.id !== id));
      
      // 删除相关样本数据
      poseDataRef.current = poseDataRef.current.filter(sample => sample.classId !== id);
      
      showMessage(`已删除类别${id + 1}`, 'success');
    }
  };

  // 初始化摄像头
  const initCamera = async (classId) => {
    try {
      const initHandle = InitNotification.show('正在初始化摄像头...');

      // 停止现有视频流
      stopVideoStream();
      
      // 重置摄像头初始化状态
      setIsCameraInitialized(false);
      
      // 清空视频和画布引用
      videoRef.current = null;
      canvasRef.current = null;
      
      // 使用ref中存储的当前朝向
      const currentFacingMode = facingModeRef.current;
      
      // 创建新的视频流
      const videoConstraints = {
        width: { ideal: 320 },
        height: { ideal: 240 }
      };
      if (recordSettingsRef.current.selectedDeviceId) {
        videoConstraints.deviceId = { exact: recordSettingsRef.current.selectedDeviceId };
      } else {
        videoConstraints.facingMode = currentFacingMode;
      }
      const stream = await navigator.mediaDevices.getUserMedia({
        video: videoConstraints,
        audio: false
      });
      
      // 创建视频元素
      videoRef.current = document.createElement('video');
      videoRef.current.setAttribute('playsinline', '');
      videoRef.current.style.transform = currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)';
      
      // 创建画布
      canvasRef.current = document.createElement('canvas');
      canvasRef.current.width = 320;
      canvasRef.current.height = 240;
      canvasRef.current.style.transform = currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)';
      
      // 设置视频源
      videoRef.current.srcObject = stream;
      videoStreamRef.current = stream;
      
      // 播放视频
      await videoRef.current.play();

      
      // 将所有类别节点设置为未展开状态，然后只展开当前类别
      setExpandedNodes(prev => {
        const newState = {};
        // 先将所有类别设为未展开
        Object.keys(prev).forEach(key => {
          newState[key] = false;
        });
        // 再将当前类别设为展开    
        newState[classId] = true;
        return newState;
      });
      
      // 设置摄像头容器
      // 获取摄像头容器
        const cameraContainer = document.getElementById(`camera-container-${classId}`);
        if (cameraContainer) {
          // 清空容器
          cameraContainer.innerHTML = '';
          
          // 设置样式
          videoRef.current.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: ${currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)'};
          `;
          
          canvasRef.current.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: ${currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)'};
          `;
          
          // 添加视频和画布到容器
          cameraContainer.appendChild(videoRef.current);
          cameraContainer.appendChild(canvasRef.current);
        
          
          if (!recordSettingsRef.current.selectedDeviceId) {
            // 动态创建翻转摄像头按钮
            const flipButton = document.createElement('div');
            flipButton.style.position = 'absolute';
            flipButton.style.top = '8px';
            flipButton.style.right = '8px';
            flipButton.style.background = 'transparent';
            flipButton.style.color = 'white';
            flipButton.style.border = 'none';
            flipButton.style.width = '32px'; // 固定宽度
            flipButton.style.height = '32px'; // 固定高度
            flipButton.style.padding = '0';
            flipButton.style.cursor = 'pointer';
            flipButton.style.display = 'flex';
            flipButton.style.alignItems = 'center';
            flipButton.style.justifyContent = 'center';
            flipButton.innerHTML = `
              <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M24 6V42" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M4 34L16 12V34H4Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
                  <path d="M44 34H32V12L44 34Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
              </svg>`;
            flipButton.addEventListener('click', () => toggleCameraFacing(classId));
            cameraContainer.appendChild(flipButton);
          }
          
          // 设置摄像头已初始化状态
          setIsCameraInitialized(true);
          
          initHandle.close();

          // 开始检测姿态
          detectPose();
        }

      // loadingAnimation.remove();
    } catch (error) {
      console.error('摄像头初始化失败:', error);
      showError('摄像头初始化失败', '无法访问摄像头，请确保已授予摄像头权限并刷新页面重试。');
      setIsCameraInitialized(false);
    }
  };
  
  // 添加切换摄像头方向的函数
  const toggleCameraFacing = (classId) => {
    // 切换摄像头方向并立即更新ref
    const newFacingMode = facingModeRef.current === 'user' ? 'environment' : 'user';
    facingModeRef.current = newFacingMode;
    
    // 更新状态
    
    // 重新初始化摄像头
    initCamera(classId);
  };
  
  // 开始采集
  const startCollecting = async (classId) => {
    // 如果已经在采集，并且是当前类别，不做任何操作
    if (isCollectingRef.current && currentClassRef.current === classId) {
      return true;
    }
    
    // 如果正在采集，但不是当前类别，先停止采集
    if (isCollectingRef.current && currentClassRef.current !== classId) {
      stopCollecting();
    }
    
    // 清除之前的定时器（如果有）
    if (recordingTimerRef.current) {
      clearTimeout(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    // 确保当前类别处于展开状态
    setExpandedNodes(prev => {
      const newState = {};
      // 先将所有类别设为未展开
      Object.keys(prev).forEach(key => {
        newState[key] = false;
      });
      // 再将当前类别设为展开
      newState[classId] = true;
      return newState;
    });
    
    // 检查摄像头是否已初始化
    if (!isCameraInitializedRef.current || !videoStreamRef.current) {
      // 初始化摄像头
      await initCamera(classId);
      
      // 如果初始化失败，直接返回
      if (!isCameraInitializedRef.current || !videoStreamRef.current) {
        return false;
      }
    }
    
    // 根据设置决定是否延迟开始采集
    if (!recordSettingsRef.current.holdToRecord && recordSettingsRef.current.delayTime > 0) {
      // 显示延迟倒计时
      const startMessage = document.createElement('div');
      startMessage.style.position = 'absolute';
      startMessage.style.top = '50%';
      startMessage.style.left = '50%';
      startMessage.style.transform = 'translate(-50%, -50%)';
      startMessage.style.background = 'rgba(0, 0, 0, 0.7)';
      startMessage.style.color = 'white';
      startMessage.style.padding = '15px 20px';
      startMessage.style.borderRadius = '5px';
      startMessage.style.fontSize = '18px';
      startMessage.style.fontWeight = 'bold';
      startMessage.style.zIndex = '1000';
      
      // 获取摄像头容器元素
      const cameraContainer = document.getElementById(`camera-container-${classId}`);
      if (cameraContainer) {
        cameraContainer.style.position = 'relative';
        cameraContainer.appendChild(startMessage);
        
        // 倒计时
        let countdown = recordSettingsRef.current.delayTime;
        startMessage.textContent = `倒计时: ${countdown.toFixed(1)}秒`;
        
        return new Promise(resolve => {
          const countdownInterval = setInterval(() => {
            countdown -= 0.1;
            startMessage.textContent = `倒计时: ${countdown.toFixed(1)}秒`;
            
            if (countdown <= 0) {
              clearInterval(countdownInterval);
              cameraContainer.removeChild(startMessage);
              
              // 实际开始采集
              setIsCollecting(true);
              setCurrentClass(classId);
              isCollectingRef.current = true;
              currentClassRef.current = classId;
              // 设置录制开始时间
              recordStartTimeRef.current = Date.now();
              
              // 如果不是按住录制模式，并且设置了持续时间，设置自动停止
              if (!recordSettingsRef.current.holdToRecord && recordSettingsRef.current.duration > 0) {
                recordingTimerRef.current = setTimeout(() => {
                  stopCollecting();
                }, recordSettingsRef.current.duration * 1000);
              }
              
              resolve(true);
            }
          }, 100);
        });
      }
    } else {
      // 立即开始采集
      setIsCollecting(true);
      setCurrentClass(classId);
      isCollectingRef.current = true;
      currentClassRef.current = classId;
      // 设置录制开始时间
      recordStartTimeRef.current = Date.now();
      
      // 如果不是按住录制模式，并且设置了持续时间，设置自动停止
      if (!recordSettingsRef.current.holdToRecord && recordSettingsRef.current.duration > 0) {
        recordingTimerRef.current = setTimeout(() => {
          stopCollecting();
        }, recordSettingsRef.current.duration * 1000);
      }
    }
    
    return true;
  };

  // 停止采集
  const stopCollecting = () => {
    // 清除录制定时器（如果有）
    if (recordingTimerRef.current) {
      clearTimeout(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
    
    // 在按住录制模式下，立即停止
    if (recordSettingsRef.current.holdToRecord) {
      // 更新状态
      setIsCollecting(false);
      setCurrentClass(null);
      isCollectingRef.current = false;
      currentClassRef.current = null;
    } else {
      // 非按住录制模式，检查是否达到设置的持续时间
      const currentTime = Date.now();
      // 计算从开始录制到现在的总时间（秒）
      const totalRecordingTime = (currentTime - recordStartTimeRef.current) / 1000;
      
      // 如果未到设置的持续时间，不停止采集
      if (totalRecordingTime < recordSettingsRef.current.duration) {
        return;
      }
      
      // 已达到设置的持续时间，停止采集
      setIsCollecting(false);
      setCurrentClass(null);
      isCollectingRef.current = false;
      currentClassRef.current = null;
    }
    
    // 不再停止视频流，只是停止采集
    // 这样用户可以继续看到摄像头画面，但不会采集样本
  };

  // 检测姿态
  const detectPose = async () => {
    if (!modelRef.current || !videoRef.current || !canvasRef.current) return;
    
    try {
      // 检测姿态
      const poses = await modelRef.current.estimatePoses(videoRef.current);
      
      if (poses.length > 0) {
        const currentPose = poses[0];
        
        // 添加到历史数据
        poseHistoryRef.current.push(currentPose);
        if (poseHistoryRef.current.length > MAX_HISTORY) {
          poseHistoryRef.current.shift();
        }
        
        // 平滑处理关键点
        const smoothedPose = smoothPose(currentPose, poseHistoryRef.current);
        
        // 使用平滑后的姿态进行绘制
        drawPose(smoothedPose);
        
        // 控制采集频率
        const currentTime = Date.now();
        if (isCollectingRef.current && currentClassRef.current !== null) {
          // 使用设置中的FPS参数计算采集间隔，从ref获取最新值
          const captureInterval = 1000 / (recordSettingsRef.current.fps || 10);
          // 检查是否达到采集间隔
          if (currentTime - lastCaptureTimeRef.current >= captureInterval) {
            // 执行采集
            capturePoseData(smoothedPose);
            // 更新上次采集时间
            lastCaptureTimeRef.current = currentTime;
          }
        }
      }
    } catch (error) {
      console.error('姿态检测错误:', error);
    }
    
    // 只要视频流存在，就继续检测，不仅限于采集状态
    if (videoStreamRef.current) {
      requestAnimationFrame(detectPose);
    }
  };
  
  // 平滑处理函数
  const smoothPose = (currentPose, history) => {
    if (history.length <= 1) return currentPose;
    
    const smoothedKeypoints = currentPose.keypoints.map((keypoint, i) => {
      let sumX = keypoint.x;
      let sumY = keypoint.y;
      let sumScore = keypoint.score;
      let count = 1;
      
      // 对历史帧中置信度较高的点进行加权平均
      history.slice(0, -1).forEach(pose => {
        const historicKeypoint = pose.keypoints[i];
        if (historicKeypoint && historicKeypoint.score > 0.3) {
          sumX += historicKeypoint.x;
          sumY += historicKeypoint.y;
          sumScore += historicKeypoint.score;
          count++;
        }
      });
      
      return {
        ...keypoint,
        x: sumX / count,
        y: sumY / count,
        score: sumScore / count
      };
    });
    
    return { ...currentPose, keypoints: smoothedKeypoints };
  };
  
  // 绘制骨架
  const drawPose = (pose) => {
    if (!canvasRef.current) return;
    
    const ctx = canvasRef.current.getContext('2d');
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    
    // 绘制关键点
    ctx.fillStyle = 'red';
    pose.keypoints.forEach(keypoint => {
      if (keypoint.score > 0.3) {
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, 5, 0, 2 * Math.PI);
        ctx.fill();
      }
    });
    
    // 绘制骨架连接线
    const connections = [
      ['nose', 'left_eye'], ['nose', 'right_eye'],
      ['left_eye', 'left_ear'], ['right_eye', 'right_ear'],
      ['nose', 'left_shoulder'], ['nose', 'right_shoulder'],
      ['left_shoulder', 'right_shoulder'],
      ['left_shoulder', 'left_elbow'], ['right_shoulder', 'right_elbow'],
      ['left_elbow', 'left_wrist'], ['right_elbow', 'right_wrist'],
      ['left_shoulder', 'left_hip'], ['right_shoulder', 'right_hip'],
      ['left_hip', 'right_hip'],
      ['left_hip', 'left_knee'], ['right_hip', 'right_knee'],
      ['left_knee', 'left_ankle'], ['right_knee', 'right_ankle']
    ];
    
    // 创建关键点映射
    const keypointMap = {};
    pose.keypoints.forEach(keypoint => {
      keypointMap[keypoint.name] = keypoint;
    });
    
    // 绘制连接线
    ctx.strokeStyle = 'blue';
    ctx.lineWidth = 2;
    
    connections.forEach(([p1Name, p2Name]) => {
      const p1 = keypointMap[p1Name];
      const p2 = keypointMap[p2Name];
      
      if (p1 && p2 && p1.score > 0.3 && p2.score > 0.3) {
        ctx.beginPath();
        ctx.moveTo(p1.x, p1.y);
        ctx.lineTo(p2.x, p2.y);
        ctx.stroke();
      }
    });
  };
  
  // 归一化姿态数据
  const normalizePose = (pose) => {
    if (!pose || !pose.keypoints || pose.keypoints.length === 0) return null;
    
    // 提取关键点
    const keypoints = pose.keypoints;
    
    // 检查关键点得分
    const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
    if (validKeypoints.length < 5) return null; // 至少需要5个有效关键点
    
    // 计算身体中心点
    let centerX = 0;
    let centerY = 0;
    let totalWeight = 0;
    
    validKeypoints.forEach(kp => {
      centerX += kp.x * kp.score;
      centerY += kp.y * kp.score;
      totalWeight += kp.score;
    });
    
    centerX /= totalWeight;
    centerY /= totalWeight;
    
    // 计算缩放因子
    let maxDist = 0;
    validKeypoints.forEach(kp => {
      const dist = Math.sqrt(
        Math.pow(kp.x - centerX, 2) + 
        Math.pow(kp.y - centerY, 2)
      );
      maxDist = Math.max(maxDist, dist);
    });
    
    // 防止除以零
    const scale = maxDist > 0 ? 1.0 / maxDist : 1.0;
    
    // 归一化所有关键点
    const normalizedData = [];
    
    // 确保所有17个关键点都有值
    for (let i = 0; i < 17; i++) {
      const keypoint = keypoints.find(kp => kp.name === getKeypointName(i));
      
      if (keypoint && keypoint.score > 0.3) {
        // 归一化坐标
        const x = (keypoint.x - centerX) * scale;
        const y = (keypoint.y - centerY) * scale;
        normalizedData.push(x, y);
      } else {
        // 对于缺失或低置信度的关键点，使用0值
        normalizedData.push(0, 0);
      }
    }
    
    return normalizedData;
  };
  
  // 获取关键点名称
  const getKeypointName = (index) => {
    const keypointNames = [
      'nose', 'left_eye', 'right_eye', 'left_ear', 'right_ear',
      'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
      'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
      'left_knee', 'right_knee', 'left_ankle', 'right_ankle'
    ];
    return keypointNames[index] || 'nose';
  };
  
  // 捕获姿态数据
  const capturePoseData = (pose) => {
    const videoWidth = videoRef.current ? videoRef.current.videoWidth : 320;
    const keypointsToStore = facingModeRef.current === 'user' ?
      pose.keypoints.map(kp => ({ ...kp, x: videoWidth - kp.x })) :
      pose.keypoints;

    const poseToStore = { ...pose, keypoints: keypointsToStore };

    // 归一化姿态数据
    const normalizedPose = normalizePose(poseToStore);
    if (!normalizedPose) return;
    
    // 创建样本
    const sample = {
      id: Date.now(),
      classId: currentClassRef.current,
      keypoints: poseToStore.keypoints.map(kp => ({
        x: kp.x,
        y: kp.y,
        score: kp.score,
        name: kp.name
      })),
      normalizedData: normalizedPose,
      timestamp: Date.now(),
      // 添加设置信息，使用ref来获取最新值
      settings: { ...recordSettingsRef.current }
    };
    
    // 添加到数据集
    poseDataRef.current.push(sample);
    
    // 更新类别节点
    setClassNodes(prev => 
      prev.map(node => 
        node.id === currentClassRef.current
          ? { ...node, samples: [...node.samples, sample] }
          : node
      )
    );
    
  };
  
  // 删除样本
  const deleteSample = (classId, sampleId) => {
    // 从数据集中删除
    poseDataRef.current = poseDataRef.current.filter(sample => sample.id !== sampleId);
    
    // 更新类别节点
    setClassNodes(prev => 
      prev.map(node => 
        node.id === classId
          ? { ...node, samples: node.samples.filter(sample => sample.id !== sampleId) }
          : node
      )
    );
  };

  // 训练模型
  const train = async () => {
    if (isTraining) return;
    
    // 重置训练历史
    setTrainingHistory({
      epochs: [],
      trainAccuracy: [],
      trainLoss: [],
      valAccuracy: [],
      valLoss: [],
    });

    try {
      // 检查TensorFlow是否已正确加载
      if (!window.tf) {
        throw new Error('TensorFlow.js未加载，请刷新页面重试');
      }
      
      // 检查浏览器内存情况
      try {
        if (window.performance && window.performance.memory) {
          const memory = window.performance.memory;
          ////console.log(`浏览器内存情况 - 已使用堆大小: ${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB, 堆大小限制: ${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`);
          
          // 如果内存使用超过80%，警告可能会出现内存问题
          if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
            console.warn('浏览器内存使用率较高，训练可能不稳定');
            showMessage('警告：浏览器内存使用较高，如遇问题请刷新页面');
          }
        } else {
          ////console.log('此浏览器不支持内存使用检测');
        }
      } catch (memoryCheckError) {
        console.warn('检查内存使用时出错:', memoryCheckError);
      }
      
      // 区分导入的类别和新添加的类别
      const importedClasses = classNodes.filter(node => node.isImported);
      const newClasses = classNodes.filter(node => !node.isImported);
      
      ////console.log('准备训练模型，类别信息:');
      ////console.log('- 导入的类别:', importedClasses.map(c => `${c.id}:${c.name}`));
      ////console.log('- 新添加的类别:', newClasses.map(c => `${c.id}:${c.name}`));
      
      // 检查是否存在导入的模型和类别
      const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
      
      // 验证训练数据 - 针对导入模型的情况进行特殊处理
      if (hasImportedModel) {
        ////console.log('检测到导入的模型，使用特殊验证逻辑');
        
        // 检查是否有新添加的类别
        if (newClasses.length === 0) {
          throw new Error('请添加至少一个新类别进行训练');
        }
        
        // 检查新添加的类别是否有足够的样本
        let hasEnoughSamples = false;
        const minSamplesRequired = 8;
        
        for (const newClass of newClasses) {
          const samplesCount = poseDataRef.current.filter(sample => sample.classId === newClass.id).length;
          ////console.log(`新类别 ${newClass.name} (ID:${newClass.id}) 的样本数量: ${samplesCount}`);
          
          if (samplesCount >= minSamplesRequired) {
            hasEnoughSamples = true;
            break; // 只要有一个新类别有足够样本即可
          }
        }
        
        if (!hasEnoughSamples) {
          throw new Error(`请为至少一个新添加的类别采集足够的样本（至少 ${minSamplesRequired} 个）`);
        }
      } else {
        // 原始验证逻辑（未导入模型的情况）
        const classCount = new Set(poseDataRef.current.map(p => p.classId)).size;
        if (classCount < 2) {
          throw new Error('至少需要2个不同的类别才能训练模型');
        }
        
        // 检查样本数量
        const classSamples = {};
        poseDataRef.current.forEach(sample => {
          classSamples[sample.classId] = (classSamples[sample.classId] || 0) + 1;
        });
        
        const minSamplesRequired = 8;
        Object.entries(classSamples).forEach(([classId, count]) => {
          if (count < minSamplesRequired) {
            const classLabel = classNodes.find(node => node.id === parseInt(classId))?.name;
            throw new Error(`类别 ${classLabel} 的样本数量不足，需要至少 ${minSamplesRequired} 个样本`);
          }
        });
      }
      
      // 开始训练
      setIsTraining(true);
      setTraining(0);
      
      // 数据增强和预处理
      const augmentedData = await augmentTrainingData();
      const {xs, ys} = prepareTrainingData(augmentedData);
      
      let model;
      
      // 针对有导入模型的情况实现迁移学习
      if (hasImportedModel) {
        ////console.log('使用迁移学习方式扩展已导入模型...');
        
        try {
          // 清理TensorFlow变量和内存，避免命名冲突
          if (window.tf) {
            ////console.log('彻底清理TensorFlow内存和变量...');
            
            // 获取所有已注册的变量名
            const allVars = window.tf.engine().registeredVariables;
            ////console.log(`当前有 ${Object.keys(allVars).length} 个已注册的变量`);
            
            try {
              // 执行标准清理，不使用ENV标志
              ////console.log('执行标准TensorFlow资源清理');
              window.tf.engine().disposeVariables();
              
              // 使用scope方式清理内存
              try {
                window.tf.engine().startScope();
                window.tf.engine().endScope();
              } catch (scopeError) {
                console.warn('Scope清理过程中出错:', scopeError);
              }
              
              // 等待更长时间，确保清理完成
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (cleanupError) {
              console.warn('清理过程中出错:', cleanupError);
            }
            
            // 验证清理结果
            try {
              const remainingVars = window.tf.engine().registeredVariables;
              ////console.log(`清理后剩余 ${Object.keys(remainingVars).length} 个已注册的变量`);
            } catch (checkError) {
              console.warn('检查剩余变量时出错:', checkError);
            }
          }
          
          // 记录原模型的结构，以便稍后重建
          ////console.log('保存原始模型结构，并清理资源...');
          
          // 先获取当前模型信息，再释放资源
          let originalWeights = [];
          let originalConfig = {};
          
          if (trainedModelRef.current) {
            try {
              // 保存原始模型的各层配置和权重
              const originalModel = trainedModelRef.current;
              const originalLayers = originalModel.layers;
              
              originalConfig = {
                layerTypes: originalLayers.map(l => l.getClassName()),
                inputShape: [34]
              };
              
              // 保存模型各层权重（前n-1层）
              for (let i = 0; i < originalLayers.length - 1; i++) {
                try {
                  const layerWeights = originalLayers[i].getWeights();
                  // 确保每层至少有一个权重张量
                  if (layerWeights && layerWeights.length > 0) {
                    originalWeights.push({
                      layerIndex: i,
                      weights: layerWeights.map(w => {
                        try {
                          return w.clone();
                        } catch (cloneError) {
                          console.warn(`无法克隆第${i}层的权重:`, cloneError);
                          return w; // 如果无法克隆，返回原始权重
                        }
                      })
                    });
                    ////console.log(`成功保存第${i}层的权重，包含${layerWeights.length}个张量`);
                  } else {
                    console.warn(`第${i}层没有权重或权重为空`);
                    // 添加一个占位符，以保持索引一致性
                    originalWeights.push({
                      layerIndex: i,
                      weights: []
                    });
                  }
                } catch (weightError) {
                  console.warn(`获取第${i}层权重时出错:`, weightError);
                  // 添加一个占位符
                  originalWeights.push({
                    layerIndex: i,
                    weights: []
                  });
                }
              }
              
              // 释放原模型资源
              originalModel.dispose();
              trainedModelRef.current = null;
            } catch (error) {
              console.warn('保存原始模型信息失败:', error);
            }
          }
          
          // 使用导入的模型为基础，但要避免变量冲突
          const timestamp = Date.now(); // 创建一个时间戳作为唯一标识
          
          // 创建新模型
          ////console.log('根据原始结构创建新的迁移学习模型...');
          const newModel = window.tf.sequential();
          
          try {
            // 添加验证检查
            if (!originalConfig || !originalConfig.layerTypes || originalConfig.layerTypes.length === 0) {
              console.warn('原始模型配置不完整或无效，将使用默认模型结构');
              throw new Error('原始模型配置无效');
            }
            
            ////console.log(`准备重建模型结构，共有${originalConfig.layerTypes.length}层`);
            
            // 根据保存的配置信息重建模型结构（除最后一层外）
            for (let i = 0; i < originalConfig.layerTypes.length - 1; i++) {
              const layerType = originalConfig.layerTypes[i].replace('Layer', '').toLowerCase();
              
              // 为每层设置唯一名称
              layerConfig.name = `transfer_${i}_${timestamp}`;
              
              // 根据层类型设置特定参数
              if (layerType === 'dense') {
                // 获取这一层权重的形状，推断units数量
                // 检查originalWeights[i]是否存在和有效
                if (originalWeights[i] && originalWeights[i].weights && originalWeights[i].weights.length > 0) {
                  const weights = originalWeights[i].weights;
                  const kernelShape = weights[0].shape;
                  layerConfig.units = kernelShape[1];
                } else {
                  // 如果没有有效的权重信息，使用默认值
                  console.warn(`缺少第${i}层的权重信息，使用默认配置`);
                  layerConfig.units = i === 0 ? 128 : (i === 2 ? 64 : 32);
                }
                layerConfig.activation = 'relu';
                layerConfig.kernelRegularizer = window.tf.regularizers.l2({l2: 0.01});
              } else if (layerType === 'dropout') {
                layerConfig.rate = i === 1 ? 0.3 : 0.2; // 根据层的位置设置dropout率
              }
              
              // 创建新层
              const newLayer = window.tf.layers[layerType](layerConfig);
              newModel.add(newLayer);
              
              // 设置保存的权重
              if (originalWeights[i] && originalWeights[i].weights && originalWeights[i].weights.length > 0) {
                try {
                  ////console.log(`应用第${i}层权重，包含${originalWeights[i].weights.length}个权重张量`);
                  newLayer.setWeights(originalWeights[i].weights);
                } catch (weightError) {
                  console.warn(`设置第${i}层权重失败:`, weightError);
                }
              } else {
                console.warn(`无法为第${i}层设置权重: 权重信息不存在或无效`);
              }
              
              // 冻结前半部分的层，防止它们在训练过程中更新
              if (i < Math.floor(originalConfig.layerTypes.length / 2)) {
                newLayer.trainable = false;
                ////console.log(`冻结层 ${i}: ${newLayer.name}`);
              }
            }
            
            // 3. 添加新的输出层以适应所有类别
            newModel.add(window.tf.layers.dense({
              units: classNodes.length,
              activation: 'softmax',
              name: `output_${timestamp}` // 唯一的输出层名称
            }));
            
            // 4. 编译新模型
            newModel.compile({
              optimizer: window.tf.train.adam(trainParams.learningRate),
              loss: 'categoricalCrossentropy',
              metrics: ['accuracy']
            });
            
            model = newModel;
            ////console.log('迁移学习模型创建成功，有以下层:');
            model.layers.forEach((l, i) => console.log(`- 层 ${i}: ${l.name} (${l.getClassName()}), 可训练: ${l.trainable}`));
            
            // 清理保存的权重数据，释放内存
            originalWeights.forEach(layerData => {
              layerData.weights.forEach(w => w.dispose());
            });
            originalWeights = [];
          } catch (buildError) {
            console.error('构建迁移学习模型失败:', buildError);
            throw buildError; // 传递错误以触发后续的错误处理
          }
          
        } catch (transferError) {
          console.error('迁移学习失败，回退到从头训练模型:', transferError);
          // 清理资源后从头创建模型
          if (window.tf) {
            try {
              // 彻底清理所有TensorFlow变量和资源
              ////console.log('错误处理：彻底清理TensorFlow资源');
              window.tf.engine().disposeVariables();
              
              try {
                window.tf.engine().startScope();
                window.tf.engine().endScope();
              } catch (scopeError) {
                console.warn('Scope清理过程中出错:', scopeError);
              }
              
              // 等待更长时间，确保清理完成
              await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (cleanupError) {
              console.warn('清理资源失败:', cleanupError);
            }
          }
          
          // 提示用户恢复方法
          showMessage('迁移学习失败，将从头创建新模型');
          
          // 从头创建模型（见下一个分支）
          try {
            model = createNewModel(classNodes.length);
            ////console.log('已创建全新模型作为备选方案');
          } catch (fallbackError) {
            // 如果连备选方案都失败，则抛出错误结束训练
            console.error('创建备选模型也失败:', fallbackError);
            throw new Error('模型创建失败，请刷新页面后重试');
          }
        }
      } else {
        // 不存在导入模型，从头创建模型
        ////console.log('从头创建新模型...');
        
        // 清理旧模型资源（如果有）
        if (trainedModelRef.current) {
          try {
            trainedModelRef.current.dispose();
            trainedModelRef.current = null;
            
            if (window.tf) {
              window.tf.engine().disposeVariables();
              window.tf.engine().startScope();
              window.tf.engine().endScope();
            }
          } catch (cleanupError) {
            console.warn('清理旧模型资源时发生错误:', cleanupError);
          }
        }
        
        model = createNewModel(classNodes.length);
      }
      
      // 训练配置 - 确保有 validationSplit
      const trainConfig = {
        epochs: hasImportedModel ? Math.floor(trainParams.epochs * 0.6) : trainParams.epochs,
        batchSize: trainParams.batchSize,
        validationSplit: 0.2, // 确保设置了验证集分割比例
        shuffle: true,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            const totalEpochs = hasImportedModel ? Math.floor(trainParams.epochs * 0.6) : trainParams.epochs;
            const progress = ((epoch + 1) / totalEpochs);
            setTraining(progress);
            console.log(`训练进度: ${(progress * 100).toFixed(1)}%, 损失: ${logs.loss.toFixed(4)}, 准确率: ${logs.acc.toFixed(4)}, 验证损失: ${logs.val_loss?.toFixed(4)}, 验证准确率: ${logs.val_acc?.toFixed(4)}`);

            // 更新训练历史状态
            setTrainingHistory(prev => ({
              epochs: [...prev.epochs, epoch + 1],
              trainAccuracy: [...prev.trainAccuracy, logs.acc || 0],
              trainLoss: [...prev.trainLoss, logs.loss || 0],
              // 确保 val_acc 和 val_loss 存在
              valAccuracy: [...prev.valAccuracy, logs.val_acc || 0], 
              valLoss: [...prev.valLoss, logs.val_loss || 0],
            }));
          }
        }
      };
      
      // 开始训练
      ////console.log('开始训练过程...');
      await model.fit(xs, ys, trainConfig);
      
      // 评估模型
      const evaluation = await model.evaluate(xs, ys);
      ////console.log('模型评估结果:', evaluation);
      
      // 训练完成
      trainedModelRef.current = model;
      modelTrainedRef.current = true;
      setModelTrained(true);
      showMessage('模型训练完成');

      // 重置训练状态
      setIsTraining(false);
      
      // 释放内存
      xs.dispose();
      ys.dispose();
      
      // 训练完成后自动开始预览
      startPreview();

    } catch (error) {
      console.error('训练失败:', error);
      showError('训练失败', error.message);
    
      setIsTraining(false);
      // 清理可能部分记录的历史数据
      setTrainingHistory({
        epochs: [],
        trainAccuracy: [],
        trainLoss: [],
        valAccuracy: [],
        valLoss: [],
      });
    }
  };
  
  // 创建新模型的辅助函数
  const createNewModel = (numClasses) => {
    ////console.log(`创建新模型，类别数: ${numClasses}`);
    
    // 使用时间戳创建唯一标识，避免命名冲突
    const timestamp = Date.now();
    
    const model = window.tf.sequential({
      layers: [
        window.tf.layers.dense({
          inputShape: [34],
          units: 128,
          activation: 'relu',
          kernelRegularizer: window.tf.regularizers.l2({l2: 0.01}),
          name: `input_dense_${timestamp}`
        }),
        window.tf.layers.dropout({
          rate: 0.3,
          name: `dropout1_${timestamp}`
        }),
        window.tf.layers.dense({
          units: 64,
          activation: 'relu',
          kernelRegularizer: window.tf.regularizers.l2({l2: 0.01}),
          name: `hidden_dense_${timestamp}`
        }),
        window.tf.layers.dropout({
          rate: 0.2,
          name: `dropout2_${timestamp}`
        }),
        window.tf.layers.dense({
          units: numClasses,
          activation: 'softmax',
          name: `output_dense_${timestamp}`
        })
      ]
    });

    // 编译模型
    model.compile({
      optimizer: window.tf.train.adam(trainParams.learningRate),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });
    
    return model;
  };
  
  // 数据增强
  const augmentTrainingData = async () => {
    const augmentedData = [];
    
    // 区分导入的类别和新添加的类别
    const importedClasses = classNodes.filter(node => node.isImported);
    const newClasses = classNodes.filter(node => !node.isImported);
    const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
    
    // 统计每个类别的样本数量
    const classSamples = {};
    poseDataRef.current.forEach(sample => {
      classSamples[sample.classId] = (classSamples[sample.classId] || 0) + 1;
    });
    
    ////console.log('原始样本分布:', classSamples);
    
    // 处理已有样本（新类别的真实样本）
    for (const sample of poseDataRef.current) {
      // 添加原始数据
      augmentedData.push(sample);
      
      // 添加噪声变体
      const noisySample = { ...sample, id: `${sample.id}-noise1` };
      noisySample.normalizedData = sample.normalizedData.map(val => val + (Math.random() - 0.5) * 0.1);
      augmentedData.push(noisySample);
      
      // 再添加一个噪声变体
      const noisySample2 = { ...sample, id: `${sample.id}-noise2` };
      noisySample2.normalizedData = sample.normalizedData.map(val => val + (Math.random() - 0.5) * 0.15);
      augmentedData.push(noisySample2);
      
      // 为新类别再添加一个小噪声变体
      if (!hasImportedModel || !classNodes.find(node => node.id === sample.classId)?.isImported) {
        const noisySample3 = { ...sample, id: `${sample.id}-noise3` };
        noisySample3.normalizedData = sample.normalizedData.map(val => val + (Math.random() - 0.5) * 0.05);
        augmentedData.push(noisySample3);
      }
    }
    
    // 对于导入的类别，需要生成一些合成样本
    if (hasImportedModel && importedClasses.length > 0) {
      ////console.log('为导入的类别生成合成样本以平衡数据集');
      
      // 计算新类别的平均样本数
      let avgNewClassSamples = 0;
      let totalNewClassSamples = 0;
      
      newClasses.forEach(newClass => {
        const count = poseDataRef.current.filter(s => s.classId === newClass.id).length;
        totalNewClassSamples += count;
      });
      
      if (newClasses.length > 0) {
        avgNewClassSamples = Math.floor(totalNewClassSamples / newClasses.length);
      }
      
      // 确保每个导入类别至少有一定数量的合成样本，增加比例
      const syntheticSamplesPerClass = Math.max(15, Math.floor(avgNewClassSamples * 0.9));
      ////console.log(`为每个导入类别生成 ${syntheticSamplesPerClass} 个合成样本`);
      
      // 为每个导入的类别生成合成样本
      importedClasses.forEach(importedClass => {
        // 如果有足够的新类别样本，从中生成合成数据
        if (poseDataRef.current.length > 0) {
          // 随机选择一些基础样本作为模板
          const baseSamples = [];
          
          // 尝试从每个新类别选择一些样本
          newClasses.forEach(newClass => {
            const classSamples = poseDataRef.current.filter(s => s.classId === newClass.id);
            if (classSamples.length > 0) {
              // 随机选择最多5个样本
              const sampleCount = Math.min(5, classSamples.length);
              for (let i = 0; i < sampleCount; i++) {
                const randomIndex = Math.floor(Math.random() * classSamples.length);
                baseSamples.push(classSamples[randomIndex]);
              }
            }
          });
          
          // 如果没有足够的基础样本，使用所有样本
          if (baseSamples.length < 5) {
            const allSamples = poseDataRef.current.slice();
            while (baseSamples.length < 5 && allSamples.length > 0) {
              const randomIndex = Math.floor(Math.random() * allSamples.length);
              baseSamples.push(allSamples[randomIndex]);
              allSamples.splice(randomIndex, 1);
            }
          }
          
          // 基于基础样本生成合成样本
          for (let i = 0; i < syntheticSamplesPerClass; i++) {
            // 随机选择一个基础样本
            const baseIndex = Math.floor(Math.random() * baseSamples.length);
            const baseSample = baseSamples[baseIndex];
            
            // 创建合成样本，使用不同强度的噪声以增加多样性
            const noiseStrength = 0.35 + (Math.random() * 0.15); // 0.35-0.5之间的随机值
            
            const syntheticSample = {
              id: `synthetic-${importedClass.id}-${i}`,
              classId: importedClass.id,
              normalizedData: baseSample.normalizedData.map(val => {
                // 添加较大的随机噪声以增加多样性
                const noise = (Math.random() - 0.5) * noiseStrength;
                return val + noise;
              }),
              timestamp: Date.now(),
              isSynthetic: true
            };
            
            augmentedData.push(syntheticSample);
          }
        }
      });
    }
    
    // 打印增强后的样本分布
    const augmentedClassSamples = {};
    augmentedData.forEach(sample => {
      augmentedClassSamples[sample.classId] = (augmentedClassSamples[sample.classId] || 0) + 1;
    });
    
    ////console.log('增强后的样本分布:', augmentedClassSamples);
    ////console.log(`总样本数: ${augmentedData.length}`);
    
    return augmentedData;
  };

  // 准备训练数据
  const prepareTrainingData = (augmentedData) => {
    // 检查是否有导入的类别
    const importedClasses = classNodes.filter(node => node.isImported);
    const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
    
    ////console.log(`准备训练数据，是否有导入模型: ${hasImportedModel}, 总样本数: ${augmentedData.length}`);
    
    const xs = [];
    const ys = [];
    
    augmentedData.forEach(sample => {
      // 添加特征
      xs.push(sample.normalizedData);
      
      // 创建 one-hot 编码标签
      const label = new Array(classNodes.length).fill(0);
      
      // 根据类ID找到对应的类索引位置
      const classIndex = classNodes.findIndex(node => node.id === sample.classId);
      
      if (classIndex !== -1) {
        label[classIndex] = 1;
      } else {
        console.warn(`未找到classId为${sample.classId}的类别，使用默认映射`);
        // 如果找不到匹配的类别，使用原始classId（可能是导入模型的情况）
        if (sample.classId < classNodes.length) {
          label[sample.classId] = 1;
        } else {
          console.error(`无效的classId: ${sample.classId}, 超出了类别范围`);
          // 使用第一个类别作为后备
          label[0] = 1;
        }
      }
      
      ys.push(label);
    });
    
    ////console.log(`准备完成，特征数: ${xs.length}, 标签数: ${ys.length}`);
    
    return {
      xs: window.tf.tensor2d(xs),
      ys: window.tf.tensor2d(ys)
    };
  };

  // 开始预览
  const startPreview = async () => {
    if (!trainedModelRef.current) {
      showError('预览失败', '模型尚未训练完成');
      return;
    }

    // --- 添加代码：关闭所有类别节点 ---
    setExpandedNodes(prev => {
      const newState = {};
      Object.keys(prev).forEach(key => {
        newState[key] = false;
      });
      return newState;
    });
    // ---------------------------------

    try {
      // 停止现有视频流
      stopVideoStream();

      const currentFacingMode = previewFacingModeRef.current;

      // 创建新的视频流
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: currentFacingMode,
          width: { ideal: 640 },
          height: { ideal: 480 }
        },
        audio: false
      });

      const transformValue = currentFacingMode === 'user' ? 'scaleX(-1)' : 'scaleX(1)';

      // 创建视频元素
      if (!previewVideoRef.current) {
        previewVideoRef.current = document.createElement('video');
        previewVideoRef.current.setAttribute('playsinline', '');
      }
      previewVideoRef.current.style.transform = transformValue;

      // 创建画布
      if (!previewCanvasRef.current) {
        previewCanvasRef.current = document.createElement('canvas');
      }
      previewCanvasRef.current.style.transform = transformValue;

      // 设置视频源
      previewVideoRef.current.srcObject = stream;

      // 播放视频
      await previewVideoRef.current.play();

      // 设置画布大小
      previewCanvasRef.current.width = previewVideoRef.current.videoWidth;
      previewCanvasRef.current.height = previewVideoRef.current.videoHeight;

      // 获取预览容器
      const previewContainer = document.getElementById('preview-container');
      if (previewContainer) {
        // 清空容器
        previewContainer.innerHTML = '';

        // 设置样式
        previewVideoRef.current.style.cssText = `
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          transform: ${transformValue};
        `;

        previewCanvasRef.current.style.cssText = `
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transform: ${transformValue};
        `;

        // 添加到容器
        previewContainer.appendChild(previewVideoRef.current);
        previewContainer.appendChild(previewCanvasRef.current);

        const flipButton = document.createElement('div');
        flipButton.style.position = 'absolute';
        flipButton.style.top = '8px';
        flipButton.style.right = '8px';
        flipButton.style.background = 'transparent';
        flipButton.style.color = 'white';
        flipButton.style.border = 'none';
        flipButton.style.width = '32px';
        flipButton.style.height = '32px';
        flipButton.style.padding = '0';
        flipButton.style.cursor = 'pointer';
        flipButton.style.display = 'flex';
        flipButton.style.alignItems = 'center';
        flipButton.style.justifyContent = 'center';
        flipButton.innerHTML = `
          <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M24 6V42" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M4 34L16 12V34H4Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
              <path d="M44 34H32V12L44 34Z" fill="none" stroke="white" stroke-width="4" stroke-linejoin="round"/>
          </svg>`;
        flipButton.addEventListener('click', () => togglePreviewCameraFacing());
        previewContainer.appendChild(flipButton);
        
        previewContainer.style.display = 'block';
      }

      // 重置预测缓冲区
      predictionBufferRef.current = [];

      // 更新状态
      isPreviewActiveRef.current = true;
      setPredicting(true);

      // 开始预测
      detectAndPredict();

    } catch (error) {
      console.error('启动预览失败:', error);
      showError('预览失败', error.message || '无法启动预览模式');
      stopPreview();
    }
  };
  
  const togglePreviewCameraFacing = () => {
    const newFacingMode = previewFacingModeRef.current === 'user' ? 'environment' : 'user';
    previewFacingModeRef.current = newFacingMode;
    startPreview();
  };

  // 停止预览
  const stopPreview = () => {
    // 更新状态
    isPreviewActiveRef.current = false;
    setPredicting(false);
    
    // 停止视频流
      if (previewVideoRef.current && previewVideoRef.current.srcObject) {
        const tracks = previewVideoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
        previewVideoRef.current.srcObject = null;
      }

    // 隐藏预览容器
    const previewContainer = document.getElementById('preview-container');
    if (previewContainer) {
      previewContainer.style.display = 'none';
    }
    
    // 重置预测结果
      setCurrentPrediction(null);
    predictionBufferRef.current = [];
  };

  // 检测和预测
  const detectAndPredict = async () => {
    if (!isPreviewActiveRef.current || !trainedModelRef.current || !previewVideoRef.current) {
      return;
    }

    try {
      // 检测姿态
      const poses = await modelRef.current.estimatePoses(previewVideoRef.current);
      
      if (poses.length > 0) {
        const pose = poses[0];
        
        // 绘制骨架
        if (previewCanvasRef.current) {
          const ctx = previewCanvasRef.current.getContext('2d');
          ctx.clearRect(0, 0, previewCanvasRef.current.width, previewCanvasRef.current.height);
          
          // 绘制关键点
          ctx.fillStyle = 'red';
          pose.keypoints.forEach(keypoint => {
            if (keypoint.score > 0.3) {
              ctx.beginPath();
              ctx.arc(keypoint.x, keypoint.y, 5, 0, 2 * Math.PI);
              ctx.fill();
            }
          });
          
          // 绘制骨架连接线
          const connections = [
            ['nose', 'left_eye'], ['nose', 'right_eye'],
            ['left_eye', 'left_ear'], ['right_eye', 'right_ear'],
            ['nose', 'left_shoulder'], ['nose', 'right_shoulder'],
            ['left_shoulder', 'right_shoulder'],
            ['left_shoulder', 'left_elbow'], ['right_shoulder', 'right_elbow'],
            ['left_elbow', 'left_wrist'], ['right_elbow', 'right_wrist'],
            ['left_shoulder', 'left_hip'], ['right_shoulder', 'right_hip'],
            ['left_hip', 'right_hip'],
            ['left_hip', 'left_knee'], ['right_hip', 'right_knee'],
            ['left_knee', 'left_ankle'], ['right_knee', 'right_ankle']
          ];
          
          // 创建关键点映射
          const keypointMap = {};
          pose.keypoints.forEach(keypoint => {
            keypointMap[keypoint.name] = keypoint;
          });
          
          // 绘制连接线
          ctx.strokeStyle = 'blue';
          ctx.lineWidth = 4;
          
          connections.forEach(([p1Name, p2Name]) => {
            const p1 = keypointMap[p1Name];
            const p2 = keypointMap[p2Name];
            
            if (p1 && p2 && p1.score > 0.3 && p2.score > 0.3) {
              ctx.beginPath();
              ctx.moveTo(p1.x, p1.y);
              ctx.lineTo(p2.x, p2.y);
              ctx.stroke();
            }
          });
        }
        
        // 预测姿态
          await predictPose(pose);
        }
    } catch (error) {
      console.error('预测错误:', error);
    }

    // 如果仍在预览，继续检测
      if (isPreviewActiveRef.current) {
        requestAnimationFrame(detectAndPredict);
    }
  };
  
  // 预测姿态
  const predictPose = async (pose) => {
    if (!trainedModelRef.current || !isPreviewActiveRef.current) return;

    try {
      const videoWidth = previewVideoRef.current ? previewVideoRef.current.videoWidth : 640;
      const keypointsToPredict = previewFacingModeRef.current === 'user' ? 
        pose.keypoints.map(kp => ({ ...kp, x: videoWidth - kp.x })) :
        pose.keypoints;
      const poseToPredict = { ...pose, keypoints: keypointsToPredict };
      // 归一化姿态数据
      const normalizedPose = normalizePose(poseToPredict);
      if (!normalizedPose) return;
      
      // 检查是否有导入的类别
      const importedClasses = classNodes.filter(node => node.isImported);
      const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
      
      // 进行预测
      const input = window.tf.tensor2d([normalizedPose], [1, 34]);
      const prediction = trainedModelRef.current.predict(input);
      const probabilities = await prediction.data();
      
      // 将新预测添加到缓冲区
      predictionBufferRef.current.push(Array.from(probabilities));
      
      // 增大缓冲区大小以提高稳定性
      const bufferSize = hasImportedModel ? 8 : 5; // 如果有导入模型，使用更大的缓冲区
      
      if (predictionBufferRef.current.length > bufferSize) {
        predictionBufferRef.current.shift();
      }
      
      // 计算平均预测结果
      const averageProbabilities = calculateAverageProbabilities();
      
      // 更新UI显示
      updatePredictionUI(averageProbabilities);
      
      // 释放张量
      input.dispose();
      prediction.dispose();
      
    } catch (error) {
      console.error('预测错误:', error);
    }
  };

  // 计算平均预测概率
  const calculateAverageProbabilities = () => {
    if (!predictionBufferRef.current || predictionBufferRef.current.length === 0) return null;
    
    const numClasses = predictionBufferRef.current[0].length;
    const averageProbabilities = new Array(numClasses).fill(0);
    
    // 区分导入的类别和新添加的类别
    const importedClasses = classNodes.filter(node => node.isImported);
    const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
    
    // 使用加权平均，更新的预测权重更高
    const weights = [];
    const bufferLength = predictionBufferRef.current.length;
    
    // 为不同的预测分配权重，最新的预测权重最高
    for (let i = 0; i < bufferLength; i++) {
      // 使用指数衰减权重
      weights.push(Math.exp(0.5 * (i / bufferLength)));
    }
    
    // 计算权重总和
    const totalWeight = weights.reduce((sum, w) => sum + w, 0);
    
    // 对每个类别计算加权平均
    for (let i = 0; i < numClasses; i++) {
      let weightedSum = 0;
      
      for (let j = 0; j < bufferLength; j++) {
        const prediction = predictionBufferRef.current[j];
        const weight = weights[j] / totalWeight;
        weightedSum += prediction[i] * weight;
      }
      
      averageProbabilities[i] = weightedSum;
    }
    
    // 如果有导入模型，增强预测稳定性
    if (hasImportedModel) {
      // 记录上一次的预测结果
      if (!window.lastPrediction) {
        window.lastPrediction = [...averageProbabilities];
      } else {
        // 平滑预测结果，减少抖动
        const smoothingFactor = 0.3; // 30%的新预测 + 70%的旧预测
        for (let i = 0; i < numClasses; i++) {
          averageProbabilities[i] = 
            smoothingFactor * averageProbabilities[i] + 
            (1 - smoothingFactor) * window.lastPrediction[i];
        }
        window.lastPrediction = [...averageProbabilities];
      }
    }
    
    return averageProbabilities;
  };

  // 更新预测UI
  const updatePredictionUI = (probabilities) => {
    if (!probabilities) return;
    
    // 区分导入的类别和新添加的类别
    const importedClasses = classNodes.filter(node => node.isImported);
    const hasImportedModel = trainedModelRef.current !== null && importedClasses.length > 0;
    
    // 调整置信度阈值
    const confidenceThreshold = 0.5; // 进一步降低置信度阈值

    // 创建新的置信度数组
    const newConfidences = classNodes.map((node, index) => {
      let adjustedProb = index < probabilities.length ? probabilities[index] : 0;
      
      if (hasImportedModel) {
          if (node.isImported) {
              adjustedProb = Math.min(1.0, adjustedProb * 1.25);
          } else {
              adjustedProb = adjustedProb * 0.95;
          }
      }
      
      return {
          id: node.id,
          name: node.name,
          confidence: adjustedProb
      };
  });

  // 更新置信度状态
  setConfidences(newConfidences);
    
    // 更新每个类别的进度条
    classNodes.forEach((node, index) => {
      if (index < probabilities.length) {
        let adjustedProb = probabilities[index];
        
        // 如果是导入模型并且有导入的类别，对预测概率进行调整以平衡结果
        if (hasImportedModel) {
          // 如果是导入的类别，提升其预测权重
          if (node.isImported) {
            adjustedProb = Math.min(1.0, adjustedProb * 1.25); // 提高25%（之前是15%）
          } else {
            // 略微降低新类别的权重
            adjustedProb = adjustedProb * 0.95; // 降低5%
          }
        }
        
        const percentage = Math.round(adjustedProb * 100);
        
        const progressBar = document.getElementById(`progress-fill-${index}`);
        const percentageText = document.getElementById(`percentage-${index}`);

        if (progressBar) progressBar.style.width = `${percentage}%`;
        if (percentageText) percentageText.textContent = `${percentage}%`;
      }
    });

    // 获取最高概率的类别（使用调整后的概率）
    let maxIndex = 0;
    let maxProbability = 0;
    
    classNodes.forEach((node, index) => {
      if (index < probabilities.length) {
        let adjustedProb = probabilities[index];
        
        // 如果是导入模型并且有导入的类别，对预测概率进行调整
        if (hasImportedModel) {
          if (node.isImported) {
            adjustedProb = Math.min(1.0, adjustedProb * 1.25); // 提高25%
          } else {
            adjustedProb = adjustedProb * 0.95; // 降低5%
          }
        }
        
        if (adjustedProb > maxProbability) {
          maxProbability = adjustedProb;
          maxIndex = index;
        }
      }
    });
    
    // 如果存在导入的类别，在概率接近时优先选择导入的类别
    if (hasImportedModel && importedClasses.length > 0) {
      // 找出导入类别中概率最高的
      let maxImportedIndex = -1;
      let maxImportedProb = 0;
      
      importedClasses.forEach(importedClass => {
        const index = classNodes.findIndex(node => node.id === importedClass.id);
        if (index >= 0 && index < probabilities.length) {
          const adjustedProb = Math.min(1.0, probabilities[index] * 1.25); // 提高25%
          if (adjustedProb > maxImportedProb) {
            maxImportedProb = adjustedProb;
            maxImportedIndex = index;
          }
        }
      });
      
      // 降低比较阈值，如果导入类别的最高概率接近总体最高概率的70%（之前是80%），优先选择导入类别
      if (maxImportedIndex >= 0 && maxImportedProb >= maxProbability * 0.7) {
        ////console.log(`优先选择导入类别: ${classNodes[maxImportedIndex].name} (调整后概率: ${maxImportedProb.toFixed(2)})`);
        maxIndex = maxImportedIndex;
        maxProbability = maxImportedProb;
      }
    }

    // 更新预测结果，添加可视化的标记
    if (maxProbability >= confidenceThreshold && classNodes[maxIndex]) {
      const isImported = classNodes[maxIndex].isImported;
      setCurrentPrediction({
        className: classNodes[maxIndex].name + (isImported ? ' (导入)' : ''),
        confidence: maxProbability * 100,
        isImported: isImported
      });
    } else {
      setCurrentPrediction({
        className: '未知姿态',
        confidence: maxProbability * 100,
        isImported: false
      });
    }
  };

  // 下载模型
  const handleExportToLocal = async () => {
    if (!trainedModelRef.current) {
      showError('下载失败', '模型尚未训练完成');
      return;
    }

    try {
      // 收集每个类别的训练数据
      const trainingData = {};
      classNodes.forEach(node => {
        if (node.samples && node.samples.length > 0) {
          trainingData[node.id] = node.samples.map(sample => ({
            id: sample.id,
            keypoints: sample.keypoints,
            normalizedData: sample.normalizedData,
            timestamp: sample.timestamp,
            settings: sample.settings
          }));
        }
      });

      // 构建完整的模型数据
      const modelData = {
        modelType: 'pose',
        classLabels: classNodes.map(node => node.name),
        trainingData: trainingData, // 添加训练数据
        trainParams: trainParams, // 添加训练参数
        modelVersion: '1.4', // 版本号
        exportDate: new Date().toISOString(),
        totalSamples: poseDataRef.current.length
      };

      // 直接创建下载链接并下载
      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(modelData));
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute("href", dataStr);
      downloadAnchorNode.setAttribute("download", "pose_model.json");
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();

      TrainNotification.success('模型已成功导出到本地');
    } catch (error) {
      console.error('模型下载失败:', error);
      showError('下载失败', `模型下载过程中发生错误: ${error.message}`);
    }
  };

  // 保存模型到云端
  const handleExportToCloud = async () => {
    if (!trainedModelRef.current) {
      showError('保存失败', '模型尚未训练完成');
      return;
    }

    try {
      // 收集每个类别的训练数据
      const trainingData = {};
      classNodes.forEach(node => {
        if (node.samples && node.samples.length > 0) {
          trainingData[node.id] = node.samples.map(sample => ({
            id: sample.id,
            keypoints: sample.keypoints,
            normalizedData: sample.normalizedData,
            timestamp: sample.timestamp,
            settings: sample.settings
          }));
        }
      });

      // 构建完整的模型数据
      const modelData = {
        modelType: 'pose',
        classLabels: classNodes.map(node => node.name),
        trainingData: trainingData, // 添加训练数据
        trainParams: trainParams, // 添加训练参数
        modelVersion: '1.4', // 版本号
        exportDate: new Date().toISOString(),
        totalSamples: poseDataRef.current.length
      };

      // 创建弹窗让用户输入模型名称和描述
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `;

      const modalContent = document.createElement('div');
      modalContent.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        width: 400px;
        max-width: 90%;
      `;

      const modalTitle = document.createElement('h3');
      modalTitle.textContent = '保存姿态模型到云端';
      modalTitle.style.cssText = `
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
      `;

      const nameLabel = document.createElement('label');
      nameLabel.textContent = '模型名称';
      nameLabel.style.cssText = `
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
      `;

      const nameInput = document.createElement('input');
      nameInput.type = 'text';
      nameInput.placeholder = '请输入模型名称';
      nameInput.style.cssText = `
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin-bottom: 16px;
        box-sizing: border-box;
      `;

      const descLabel = document.createElement('label');
      descLabel.textContent = '模型描述';
      descLabel.style.cssText = `
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
      `;

      const descInput = document.createElement('textarea');
      descInput.placeholder = '请输入模型描述（可选）';
      descInput.style.cssText = `
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin-bottom: 16px;
        box-sizing: border-box;
        min-height: 80px;
        resize: vertical;
      `;

      const buttonContainer = document.createElement('div');
      buttonContainer.style.cssText = `
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      `;

      const cancelButton = document.createElement('button');
      cancelButton.textContent = '取消';
      cancelButton.style.cssText = `
        padding: 8px 16px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background: white;
        cursor: pointer;
      `;

      const saveButton = document.createElement('button');
      saveButton.textContent = '保存';
      saveButton.style.cssText = `
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        background: #4766C2;
        color: white;
        cursor: pointer;
      `;

      // 添加取消按钮点击事件
      cancelButton.addEventListener('click', () => {
        document.body.removeChild(modal);
      });

      // 添加保存按钮点击事件
      saveButton.addEventListener('click', async () => {
        const modelName = nameInput.value.trim();
        const modelDesc = descInput.value.trim();

        if (!modelName) {
          alert('请输入模型名称');
          return;
        }

        // 禁用保存按钮
        saveButton.disabled = true;
        saveButton.textContent = '保存中...';

        try {
          // 调用云端保存逻辑
          const { saveModelToCloud } = await import('./utils/modelCloudStorage.js');

          // 构建包含训练数据的元数据
          const metadata = {
            modelType: 'pose',
            classLabels: classNodes.map(node => node.name),
            trainingData: trainingData, // 包含训练数据
            trainParams: trainParams, // 包含训练参数
            modelVersion: '1.4',
            exportDate: new Date().toISOString(),
            totalSamples: poseDataRef.current.length
          };

          await saveModelToCloud(
            trainedModelRef.current,
            metadata,
            modelName,
            modelDesc,
            false, // isPublic
            (message) => TrainNotification.success(message),
            (title, message) => TrainNotification.error(`${title}: ${message}`)
          );

          // 移除弹窗
          document.body.removeChild(modal);

        } catch (error) {
          console.error('保存模型失败:', error);
          TrainNotification.error(`保存模型失败: ${error.message}`);

          // 恢复保存按钮状态
          saveButton.disabled = false;
          saveButton.textContent = '保存';
        }
      });

      // 组装DOM
      buttonContainer.appendChild(cancelButton);
      buttonContainer.appendChild(saveButton);

      modalContent.appendChild(modalTitle);
      modalContent.appendChild(nameLabel);
      modalContent.appendChild(nameInput);
      modalContent.appendChild(descLabel);
      modalContent.appendChild(descInput);
      modalContent.appendChild(buttonContainer);

      modal.appendChild(modalContent);
      document.body.appendChild(modal);

      // 自动聚焦到名称输入框
      nameInput.focus();

    } catch (error) {
      console.error('保存到云端失败:', error);
      showError('保存失败', `保存到云端过程中发生错误: ${error.message}`);
    }
  };

  // 添加一个 useEffect 来处理样本渲染
  useEffect(() => {
    // 为每个样本绘制姿态线框
    classNodes.forEach(node => {
      node.samples.forEach(sample => {
        // 处理展开状态的样本画布
        const canvas = document.getElementById(`sample-canvas-${sample.id}`);
        if (canvas && sample.keypoints) {
          // 设置画布尺寸
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          // 绘制姿态
          drawSamplePose(canvas, sample.keypoints);
        }
        
        // 处理未展开状态的预览缩略图
        const previewCanvas = document.getElementById(`preview-canvas-${sample.id}`);
        if (previewCanvas && sample.keypoints) {
          // 设置画布尺寸
          previewCanvas.width = previewCanvas.offsetWidth;
          previewCanvas.height = previewCanvas.offsetHeight;
          // 绘制姿态
          drawSamplePose(previewCanvas, sample.keypoints);
        }
      });
    });
  }, [expandedNodes,classNodes]);
  
  // 添加加载已保存模型的函数
  const loadSavedModels = () => {
    try {
      // 使用导入的工具函数加载模型
      const models = loadModelsFromStorage();
      setSavedModels(models);
      return models;
    } catch (error) {
      console.error('加载已保存模型失败:', error);
      return [];
    }
  };

  // 在组件挂载时加载已保存的模型
  useEffect(() => {
    loadSavedModels();
  }, []);

  // 在组件挂载时初始化类别节点
  useEffect(() => {
    // 确保类别节点的样式正确显示
    classNodes.forEach(node => {
      const { id } = node;
      // 如果节点是展开状态，确保摄像头容器正确显示
      if (expandedNodes[id]) {
        // 确保同一时间只有一个类别处于展开状态
        const expandedCount = Object.values(expandedNodes).filter(Boolean).length;
        if (expandedCount > 1) {
          // 如果有多个类别处于展开状态，只保留当前类别展开
          setExpandedNodes(prev => {
            const newState = {};
            Object.keys(prev).forEach(key => {
              newState[key] = key === id.toString();
            });
            return newState;
          });
        }
        
        // setTimeout(() => {
          const cameraContainer = document.getElementById(`camera-container-${id}`);
          if (cameraContainer) {
            cameraContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%;">点击"采集姿态"按钮开始使用摄像头</div>';
          }
        // }, 100);
      }
    });

    // 初始化类别 ID 映射
    if (Object.keys(classIdMapping).length === 0 && classNodes.length > 0) {
      const initialMapping = {};
      classNodes.forEach(node => {
        initialMapping[node.id] = node.id;
      });
      setClassIdMapping(initialMapping);
    }

    // 设置初始化完成
    setIsInitializing(false);
  }, []);

  // 添加保存模型到本地存储的函数
  const exportModelToBlocks = async () => {
    if (!trainedModelRef.current || !modelTrainedRef.current) {
      showError('保存失败', '模型尚未训练完成');
      return Promise.reject(new Error('模型尚未训练完成'));
    }

    try {
      // 收集每个类别的训练数据
      const trainingData = {};
      classNodes.forEach(node => {
        if (node.samples && node.samples.length > 0) {
          trainingData[node.id] = node.samples.map(sample => ({
            id: sample.id,
            keypoints: sample.keypoints,
            normalizedData: sample.normalizedData,
            timestamp: sample.timestamp,
            settings: sample.settings
          }));
        }
      });

      // 构建完整的模型数据
      const modelData = {
        modelType: 'pose',
        classLabels: classNodes.map(node => node.name),
        trainingData: trainingData, // 添加训练数据
        trainParams: trainParams, // 添加训练参数
        modelVersion: '1.4', // 版本号
        exportDate: new Date().toISOString(),
        totalSamples: poseDataRef.current.length
      };

      // 获取全局实例
      const parentInstance = window._logicleapPoseTrainInstance;
      if (!parentInstance) {
        TrainNotification.error('无法获取全局实例');
        return Promise.reject(new Error('无法获取全局实例'));
      }

      // 更新模型状态
      parentInstance.modelImported = true;

      // 保存模型数据到全局实例
      parentInstance.modelCount = (parentInstance.modelCount || 0) + 1;
      const modelNumber = String(parentInstance.modelCount);

      // 不再使用旧的缓存机制，完全使用 poseModels 作为缓存键名
      // 但仍然保留对 parentInstance 的兼容性更新
      parentInstance.modelCache = parentInstance.modelCache || {};
      parentInstance.modelDataCache = parentInstance.modelDataCache || {};

      // 获取模型标签
      const modelName = `包含${classNodes.length}个类别的姿态模型`;

      // 为了向后兼容，仍然更新旧的缓存对象
      parentInstance.modelCache[modelNumber] = {
        modelName: modelName,
        importTime: new Date().toISOString(),
        labels: classNodes.map(node => node.name)
      };

      // 将模型数据保存到浏览器缓存，使用与云端保存一致的数据结构
      try {
        // 获取已保存的模型列表
        const savedModelsStr = localStorage.getItem('poseModels');
        const models = savedModelsStr ? JSON.parse(savedModelsStr) : [];

        // 计算新模型的序号
        let nextModelNumber = 1;
        if (models.length > 0) {
          // 找出最大序号并加1
          nextModelNumber = Math.max(...models.map(m => m.number)) + 1;
        }

        // 保存模型为 ArrayBuffer（与云端保存一致）
        const modelArtifacts = await trainedModelRef.current.save(tf.io.withSaveHandler(async artifacts => {
          return artifacts;
        }));

        // 创建新模型记录（使用与云端保存相同的数据结构）
        const newModel = {
          number: nextModelNumber,
          name: modelName,
          // 使用与云端保存相同的数据结构
          modelType: modelData.modelType || 'pose',
          classLabels: modelData.classLabels || [],
          trainingData: modelData.trainingData || {}, // 包含训练数据
          trainParams: modelData.trainParams || {}, // 包含训练参数
          modelVersion: modelData.modelVersion || '1.4',
          exportDate: modelData.exportDate || new Date().toISOString(),
          totalSamples: modelData.totalSamples || 0,
          // TensorFlow.js 模型数据
          modelTopology: modelArtifacts.modelTopology,
          weightSpecs: modelArtifacts.weightSpecs,
          weightData: Array.from(new Uint8Array(modelArtifacts.weightData)),
          // 本地存储特有字段
          metadata: modelData, // 保留原始元数据以兼容性
          createdAt: new Date().toISOString()
        };

        // 添加到模型列表
        const updatedModels = [...models, newModel];

        // 保存到localStorage，使用统一的 poseModels 键名
        localStorage.setItem('poseModels', JSON.stringify(updatedModels));

        // 更新 parentInstance 中的模型编号为新的序号
        parentInstance.currentModel = String(nextModelNumber);

      } catch (storageError) {
        console.warn('保存到本地存储失败:', storageError);
        // 即使本地存储失败，也继续执行，因为模型已经在内存中
      }

      // 触发模型缓存更新事件
      if (typeof parentInstance.dispatchModelCacheUpdatedEvent === 'function') {
        parentInstance.dispatchModelCacheUpdatedEvent();
      }

      // 显示成功消息
      TrainNotification.success(`模型已成功保存（编号：${parentInstance.currentModel}），可以使用"预测姿态"积木块来使用此模型。`);

      // 自动关闭训练窗口
      setTimeout(() => {
        if (parentInstance && typeof parentInstance.closeTrainDialog === 'function') {
          parentInstance.closeTrainDialog();
        }
      }, 1500); // 添加延时，让用户看到成功消息

      return Promise.resolve(); // 返回成功的Promise
    } catch (error) {
      console.error('保存模型失败:', error);
      showError('保存失败', `保存模型过程中发生错误: ${error.message}`);
      return Promise.reject(error); // 返回失败的Promise
    }
  };

  // 添加临时消息显示函数
  const showMessage = (text) => {
    TrainNotification.success(text);
  };
  
  // 处理待导入的模型数据
  const handlePendingModelImport = async (modelDataString) => {
    try {
      console.log('🐱 Claude 4.0 sonnet: 开始解析姿态模型数据...');
      console.log('🐱 原始数据长度:', modelDataString?.length);

      // 检查数据是否为空
      if (!modelDataString) {
        console.error('模型数据为空');
        TrainNotification.error('模型数据为空，请选择有效的模型文件');
        return;
      }

      // 解析JSON数据
      let modelData;
      try {
        modelData = JSON.parse(modelDataString);
        console.log('JSON解析成功，姿态模型数据:', modelData);
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        TrainNotification.error('模型文件格式错误，无法解析JSON数据');
        return;
      }

      // 检查模型数据是否为空
      console.log('模型数据类型:', typeof modelData);
      console.log('模型数据是否为null:', modelData === null);
      console.log('模型数据是否为undefined:', modelData === undefined);
      console.log('模型数据布尔值:', !!modelData);

      if (!modelData || typeof modelData !== 'object' || modelData === null) {
        console.error('解析后的模型数据为空或无效');
        TrainNotification.error('模型数据为空，请选择有效的模型文件');
        return;
      }

      // 检查模型类型
      console.log('检查模型类型:', modelData.modelType);
      if (!modelData.modelType || modelData.modelType !== 'pose') {
        console.error('不支持的模型类型:', modelData.modelType);
        TrainNotification.error('不支持的模型类型，请导入姿态训练模型');
        return;
      }

      // 检查是否有训练数据
      if (modelData.trainingData) {
        // 处理训练数据，恢复到类别节点
        const newClassNodes = [];
        const newPoseData = [];

        // 遍历标签创建类别节点
        modelData.classLabels.forEach((label, index) => {
          const classId = index;
          const samples = [];

          // 如果有对应的训练数据，添加到samples中
          if (modelData.trainingData[classId]) {
            modelData.trainingData[classId].forEach(sampleData => {
              const sample = {
                id: sampleData.id || Date.now() + Math.random(),
                classId: classId,
                keypoints: sampleData.keypoints,
                normalizedData: sampleData.normalizedData,
                timestamp: sampleData.timestamp || Date.now(),
                settings: sampleData.settings || {}
              };

              samples.push(sample);
              newPoseData.push(sample);
            });
          }

          newClassNodes.push({
            id: classId,
            name: label,
            samples: samples,
            isImported: false, // 标记为可编辑
            originalIndex: index
          });
        });

        // 计算总的样本数量
        const totalSamples = newClassNodes.reduce((total, node) => total + node.samples.length, 0);

        // 检查样本数量，如果超过30个则显示警告
        if (totalSamples > 30) {
          const confirmed = window.confirm(
            `该模型包含${totalSamples}个姿态样本（超过30个），模型较大可能会导致机器卡顿。\n\n是否继续导入？`
          );

          if (!confirmed) {
            // 用户取消导入
            return;
          }
        }

        // 用户确认导入，更新状态
        setClassNodes(newClassNodes);
        poseDataRef.current = newPoseData;

        // 恢复训练参数（如果存在）
        if (modelData.trainParams) {
          setTrainParams(modelData.trainParams);
          console.log('已恢复训练参数:', modelData.trainParams);
        } else {
          // 向后兼容：旧模型文件没有训练参数时使用默认值
          setTrainParams(DefaultTrainParams);
          console.log('使用默认训练参数（旧版本模型）');
        }

        // 显示成功消息
        const hasTrainParams = modelData.trainParams ? '训练参数已恢复，' : '';
        if (totalSamples > 0) {
          TrainNotification.success(`训练数据已成功导入！包含${newClassNodes.length}个类别，共${totalSamples}个姿态样本。${hasTrainParams}请点击"开始训练"来训练模型。`);
        } else {
          TrainNotification.success(`模型结构已导入！包含${newClassNodes.length}个类别（旧版本模型，无样本数据）。${hasTrainParams}请添加样本后重新训练。`);
        }
      } else {
        TrainNotification.error('模型文件中没有找到训练数据，无法编辑');
      }

    } catch (error) {
      console.error('自动导入姿态模型失败:', error);
      TrainNotification.error('自动导入姿态模型失败: ' + error.message);
    }
  };

  // 处理导入模型
  const handleImportModel = async () => {
    try {
      // 创建文件输入框
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.json';
      fileInput.style.display = 'none';

      // 添加文件选择事件处理
      fileInput.onchange = async (event) => {
        if (!event.target.files || !event.target.files[0]) return;

        const file = event.target.files[0];

        try {
          // 读取文件内容
          const reader = new FileReader();

          // 设置加载完成后的处理函数
          reader.onload = async (e) => {
            await handlePendingModelImport(e.target.result);
          };

          // 读取文件
          reader.readAsText(file);

        } catch (error) {
          console.error('读取文件失败:', error);
          TrainNotification.error('读取文件失败: ' + error.message);
        }
      };

      // 添加到文档并触发点击
      document.body.appendChild(fileInput);
      fileInput.click();

      // 使用完成后移除
      setTimeout(() => {
        document.body.removeChild(fileInput);
      }, 100);

    } catch (error) {
      console.error('导入模型失败:', error);
      TrainNotification.error('导入模型失败: ' + error.message);
    }
  };
  
  // 在类别节点变化时更新连接线
  useEffect(() => {
    if (typeof trainMainFunctionRef.current.updateConnections === 'function') {
      trainMainFunctionRef.current.updateConnections();
    }
  }, [classNodes, expandedNodes]);
  

  // 添加清空样本功能
  const clearSamples = (classId) => {
    // 关闭菜单
    closeAllMenus();
    
    // 从数据集中删除该类别的所有样本
    poseDataRef.current = poseDataRef.current.filter(sample => sample.classId !== classId);
    
    // 更新类别节点
    setClassNodes(prev => 
      prev.map(node => 
        node.id === classId
          ? { ...node, samples: [] }
          : node
      )
    );
    
    showMessage(`已清空类别${classId + 1}的所有样本`, 'success');
  };

  // 添加菜单切换功能
  const toggleMenu = (e, nodeId) => {
    e.stopPropagation();
    
    setShowMenus(prev => {
      const newState = {};
      Object.keys(prev).forEach(key => {
        newState[key] = false;
      });
      newState[nodeId] = !prev[nodeId];
      return newState;
    });
    
    // 添加点击其他地方关闭菜单的事件
    document.addEventListener('click', closeAllMenus);
  };

  // 关闭所有菜单
  const closeAllMenus = () => {
    setShowMenus({});
    document.removeEventListener('click', closeAllMenus);
  };

  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 判断是否按下空格键
      if (e.key === ' ' || e.keyCode === 32) {
        if(!canSpaceRecord()){
          return;
        }
        // 判断是否有展开的节点
        const expandedNodeId = Object.keys(expandedNodes).find(id => expandedNodes[id]);
        if (expandedNodeId) {
          // 判断该节点是否可以采集
          const node = classNodes.find(n => n.id === parseInt(expandedNodeId));
          if (node && !node.isImported) {
            if (recordSettingsRef.current.holdToRecord) {
              if (!isCollecting || currentClass !== node.id) {
                // 开始采集
                spaceCapturingRef.current = true;
                startCollecting(node.id);
              }
            } else if (!isCollecting) {
              // 非按住模式，空格键切换采集状态
              if (currentClass !== node.id) {
                startCollecting(node.id);
              } else {
                stopCollecting();
              }
            }
          }
        }
      }
    };

    const handleKeyUp = (e) => {

      // 判断是否松开空格键
      if ((e.key === ' ' || e.keyCode === 32) && recordSettingsRef.current.holdToRecord && spaceCapturingRef.current) {
        if(!canSpaceRecord()){
          return;
        }
        // 停止采集
        if (isCollecting) {
          stopCollecting();
          spaceCapturingRef.current = false;
        }
      }
    };

    const canSpaceRecord = ()=>{
      if(recordSettingsRef.current.canSpaceRecord){
        return true;
      }else{
        return false;
      }
    }

    // 添加事件监听
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [expandedNodes, classNodes, isCollecting, currentClass]);

  // 添加设置当前类别的函数
  const setActiveClass = (classId) => {
    // 更新状态变量和ref
    setCurrentClass(classId);
    currentClassRef.current = classId;
    //console.log('已设置当前类别:', classId);
    
    // 确保对应类别处于展开状态
    setExpandedNodes(prev => ({
      ...prev,
      [classId]: true
    }));
  };

  const renderClassCardIsImported=(node)=>{
    return(
      <div 
          key={node.id} 
          data-class-node={true}
          className={ClassCardCss.classCard}
      >
          <div style={ClassCardStyle.Header}>
              <div style={ClassCardStyle.HeaderLeft}>
                <div style={styles.flexGrow}>
                  <input
                    type="text"
                    value={node.name}
                    onChange={(e) => {
                      const inputValue = e.target.value.trim();
                      setClassNodes(prev => 
                        prev.map(n => 
                          n.id === node.id ? { ...n, name: inputValue } : n
                        )
                      );
                    }}
                    style={ClassCardStyle.HeaderLeftInput}
                    readOnly={true} // 导入的类别不允许编辑
                  />
                  <span style={styles.importedTag}>
                    已导入
                  </span>
                </div>
              </div>


              <div style={ClassCardStyle.HeaderRight}>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // 打开/关闭菜单的逻辑
                          // 可以增加一个菜单状态控制
                          const menuElem = e.currentTarget.nextElementSibling;
                          if (menuElem) {
                              menuElem.style.display = menuElem.style.display === 'block' ? 'none' : 'block';
                          }
                      }}
                        style={ClassCardStyle.HeaderRightMenuButton}
                        data-class-card-menu-button="true"
                      >
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                      </button>
                      
                        <div 
                            style={ClassCardStyle.HeaderRightMenuDropdown}
                            data-class-card-menu="true"
                        >
                          <div
                            style={{
                              ...ClassCardStyle.HeaderRightMenuItem,
                              color: '#d93025',
                            }}
                            onClick={() => deleteClassNode(node.id)}
                            onMouseEnter={(e) => e.currentTarget.style.background = '#fff5f5'}
                            onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M3 6h18"></path>
                              <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                              <line x1="10" y1="11" x2="10" y2="17"></line>
                              <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                            删除类别
                          </div>
                        </div>
                </div>
          </div>


      </div>
    )
  }

  const renderCLassCardNotImported=(node)=>{
    return(
      <div 
        key={node.id} 
        data-class-node={true}
        className={ClassCardCss.classCard}
      >
        <div style={ClassCardStyle.Header}>
            <div style={ClassCardStyle.HeaderLeft}>
              <div style={styles.flexGrow}>
                <input
                  type="text"
                  value={node.name}
                  onChange={(e) => {
                    const inputValue = e.target.value.trim();
                    setClassNodes(prev => 
                      prev.map(n => 
                        n.id === node.id ? { ...n, name: inputValue } : n
                      )
                    );
                  }}
                  style={ClassCardStyle.HeaderLeftInput}
                />
                  <button
                    onClick={(e) => {
                      // 获取当前输入框元素
                      const input = e.currentTarget.parentNode.querySelector('input');
                      if (input) {
                        // 聚焦并选择所有文本
                        input.focus();
                        input.select();
                      }
                    }}
                    style={ClassCardStyle.HeaderLeftInputEditButton}
                    onMouseEnter={(e) => {
                      e.currentTarget.querySelector('svg').style.stroke = '#333';
                      e.currentTarget.style.opacity = '1';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.querySelector('svg').style.stroke = '#666';
                      e.currentTarget.style.opacity = '0.5';
                    }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
                    </svg>
                  </button>
              </div>
            </div>


            <div style={ClassCardStyle.HeaderRight}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // 打开/关闭菜单的逻辑
                        // 可以增加一个菜单状态控制
                        const menuElem = e.currentTarget.nextElementSibling;
                        if (menuElem) {
                            menuElem.style.display = menuElem.style.display === 'block' ? 'none' : 'block';
                        }
                    }}
                      style={ClassCardStyle.HeaderRightMenuButton}
                      data-class-card-menu-button="true"
                    >
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                      </svg>
                    </button>
                    
                      <div 
                          style={ClassCardStyle.HeaderRightMenuDropdown}
                          data-class-card-menu="true"
                      >
                        <div
                          style={ClassCardStyle.HeaderRightMenuItem}
                          onClick={() => clearSamples(node.id)}
                          onMouseEnter={(e) => e.currentTarget.style.background = '#f5f5f5'}
                          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#666" strokeWidth="2">
                            <polyline points="3 6 5 6 21 6"></polyline>
                            <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                          </svg>
                          清空样本
                        </div>
                        <div
                          style={{
                            ...ClassCardStyle.HeaderRightMenuItem,
                            color: '#d93025',
                          }}
                          onClick={() => deleteClassNode(node.id)}
                          onMouseEnter={(e) => e.currentTarget.style.background = '#fff5f5'}
                          onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M3 6h18"></path>
                            <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                          删除类别
                        </div>
                      </div>
              </div>
        </div>

          
        {!expandedNodes[node.id]  && (
          <div style={ClassCardStyle.ContentNotExpanded}>

            <div style={ClassCardStyle.ContentNotExpandedSamplesSummary}>
                {node.samples.length === 0 ? 
                  '添加姿态样本:（应至少添加10个）' : 
                  `${node.samples.length} 个姿态样本（应至少添加10个）`
                }
            </div>

            <div style={ClassCardStyle.ContentNotExpandedContent}>
              <div style={ClassCardStyle.ContentNotExpandedContentLeftButtonContainer}>
                <button
                  onClick={() => initCamera(node.id)}
                  style={ClassCardStyle.ContentNotExpandedContentLeftButton}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#4766C2" strokeWidth="2">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                    <circle cx="12" cy="13" r="4"></circle>
                  </svg>
                  <span>摄像头</span>
                </button>
              </div>


              <div className={ClassCardCss.sampleScrollContainer}>
                {node.samples.slice(0, 10).map(sample => (
                  <div key={sample.id} style={{...styles.previewThumbnail, position: 'relative'}}>
                    <div 
                      style={{
                        position: 'absolute',
                        top: '2px',
                        right: '2px',
                        width: '16px',
                        height: '16px',
                        backgroundColor: 'rgba(255, 77, 79, 0.8)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '10px',
                        cursor: 'pointer',
                        zIndex: 10,
                        opacity: 0,
                        transition: 'opacity 0.2s'
                      }}
                      onClick={(e) => {
                        e.stopPropagation(); // 防止事件冒泡
                        deleteSample(node.id, sample.id);
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.opacity = '1'}
                      onMouseLeave={(e) => e.currentTarget.style.opacity = '0'}
                    >
                      ×
                    </div>
                    <canvas 
                      id={`preview-canvas-${sample.id}`}
                      style={{ width: '100%', height: '100%' }}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {expandedNodes[node.id] && (
          <div style={ClassCardStyle.ContentExpanded}>
              <div style={ClassCardStyle.ContentExpandedContainerLeft}>
                <div style={ClassCardStyle.ContentExpandedContainerLeftHeader}>
                    <div style={ClassCardStyle.ContentExpandedContainerLeftHeaderTitle}>
                        摄像头
                    </div>
                    <div style={ClassCardStyle.ContentExpandedContainerLeftHeaderCloseButton}
                          onClick={(e) => {
                              e.stopPropagation();
                              // 关闭展开视图
                              setExpandedNodes(prev => ({
                                ...prev,
                                [node.id]: false
                              }));
                          }}
                    >
                        <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="#1890ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </div>
                  </div>
                  {/* 摄像头容器 */}
                  <div 
                    id={`camera-container-${node.id}`}
                    style={CameraCardStyle.cameraContainer}
                  />
              
                  <div style={CameraCardStyle.cameraButtonContainer}>
                    <button 
                      onMouseDown={() => {
                        // 当按下鼠标时开始采集
                        if (recordSettingsRef.current.holdToRecord) {
                          if (!isCollecting || currentClass !== node.id) {
                            startCollecting(node.id);
                          }
                        }
                      }}
                      onMouseUp={() => {
                        // 当松开鼠标时停止采集
                        if (recordSettingsRef.current.holdToRecord) {
                          if (isCollecting && currentClass === node.id) {
                            stopCollecting();
                          }
                        }
                      }}
                      onMouseLeave={() => {
                        // 当鼠标离开按钮区域时也停止采集
                        // 防止用户按住后将鼠标移出按钮区域的情况
                        if (recordSettingsRef.current.holdToRecord) {
                          if (isCollecting && currentClass === node.id) {
                            stopCollecting();
                          }
                        }
                      }}
                      onClick={() => {
                        // 非按住录制模式，点击按钮切换开始/停止
                        if (!recordSettingsRef.current.holdToRecord) {
                          if (!isCollecting || currentClass !== node.id) {
                            startCollecting(node.id);
                          } else {
                            stopCollecting();
                          }
                        }
                      }}
                      onTouchStart={() => {
                        // 移动设备触摸开始时开始采集
                        if (recordSettingsRef.current.holdToRecord) {
                          if (!isCollecting || currentClass !== node.id) {
                            startCollecting(node.id);
                          }
                        }
                      }}
                      onTouchEnd={() => {
                        // 移动设备触摸结束时停止采集
                        if (recordSettingsRef.current.holdToRecord) {
                          if (isCollecting && currentClass === node.id) {
                            stopCollecting();
                          }
                        }
                      }}
                      style={CameraCardStyle.cameraRecordButton}
                    >
                      <span>开始录制</span>
                    </button>
                    
                    {/* 设置按钮 */}
                    <button 
                      onClick={() => setShowSettingsPanel(prev => !prev)}
                      style={CameraCardStyle.cameraSettingButton}
                    >
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="3"></circle>
                              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                          </svg>
                    </button>
                  </div>
                  
                  {/* 设置面板 */}
                  {showSettingsPanel && (
                    <div style={CameraCardStyle.cameraSettingContainer}>
                      <div style={CameraCardStyle.cameraSettingContainerHeader}>
                        <h3 style={CameraCardStyle.cameraSettingContainerHeaderTitle}>设置</h3>
                      </div>
                      
                      <div style={CameraCardStyle.cameraSettingContainerItem}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>选择摄像头:</span>
                          <select
                            value={recordSettings.selectedDeviceId}
                            onChange={(e) => {
                              const newDeviceId = e.target.value;
                              setRecordSettings(prev => ({
                                ...prev,
                                selectedDeviceId: newDeviceId
                              }));
                            }}
                            style={{...CameraCardStyle.cameraSettingContainerItemInput, width: 'auto', flex: '1'}}
                          >
                            {videoDevices.map((device, index) => (
                              <option key={device.deviceId} value={device.deviceId}>
                                {device.label || `摄像头 ${index + 1}`}
                              </option>
                            ))}
                          </select>
                        </label>
                      </div>

                      <div style={CameraCardStyle.cameraSettingContainerItem}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>每秒帧数:</span>
                          <input 
                            type="number" 
                            value={recordSettings.fps} 
                            onChange={(e) => {
                              e.persist(); // 防止React事件池回收此事件
                              // 提取当前的值，避免后续引用已回收的事件对象
                              const newValue = e.target && e.target.value;
                              setRecordSettings(prev => ({
                                ...prev,
                                fps: Math.max(1, Math.min(30, parseInt(newValue) || 1))
                              }));
                            }}
                            style={CameraCardStyle.cameraSettingContainerItemInput}
                            min="1"
                            max="30"
                          />
                        </label>
                      </div>

                      <div style={CameraCardStyle.cameraSettingContainerItem}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>按空格键即可录制</span>
                          <div 
                            onClick={() => {
                              // 切换按住录制模式
                              const newCanSpaceRecord = !recordSettingsRef.current.canSpaceRecord;
                              setRecordSettings(prev => ({
                                ...prev,
                                canSpaceRecord: newCanSpaceRecord
                              }));
                            }}
                            style={{
                              width: '40px',
                              height: '20px',
                              backgroundColor: recordSettings.canSpaceRecord ? '#4766C2' : '#ccc',
                              borderRadius: '10px',
                              position: 'relative',
                              cursor: 'pointer',
                              transition: 'background-color 0.3s'
                            }}
                          >
                            <div style={{
                              width: '16px',
                              height: '16px',
                              backgroundColor: 'white',
                              borderRadius: '50%',
                              position: 'absolute',
                              top: '2px',
                              left: recordSettings.canSpaceRecord ? '22px' : '2px',
                              transition: 'left 0.3s'
                            }} />
                          </div>
                        </label>
                      </div>
                      
                      <div style={CameraCardStyle.cameraSettingContainerItem}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>按住即可录制</span>
                          <div 
                            onClick={() => {
                              // 切换按住录制模式
                              const newHoldToRecord = !recordSettingsRef.current.holdToRecord;
                              setRecordSettings(prev => ({
                                ...prev,
                                holdToRecord: newHoldToRecord
                              }));
                              
                              // 如果切换到按住录制模式，并且正在录制，则停止录制
                              if (newHoldToRecord && isCollecting) {
                                stopCollecting();
                              }
                            }}
                            style={{
                              width: '40px',
                              height: '20px',
                              backgroundColor: recordSettings.holdToRecord ? '#4766C2' : '#ccc',
                              borderRadius: '10px',
                              position: 'relative',
                              cursor: 'pointer',
                              transition: 'background-color 0.3s'
                            }}
                          >
                            <div style={{
                              width: '16px',
                              height: '16px',
                              backgroundColor: 'white',
                              borderRadius: '50%',
                              position: 'absolute',
                              top: '2px',
                              left: recordSettings.holdToRecord ? '22px' : '2px',
                              transition: 'left 0.3s'
                            }} />
                          </div>
                        </label>
                      </div>
                      
                      <div style={{ 
                        ...CameraCardStyle.cameraSettingContainerItem,
                        opacity: recordSettings.holdToRecord ? 0.5 : 1,
                        pointerEvents: recordSettings.holdToRecord ? 'none' : 'auto'
                      }}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>延迟时间 (秒):</span>
                          <input 
                            type="number" 
                            value={recordSettings.delayTime} 
                            onChange={(e) => {
                              e.persist(); // 防止React事件池回收此事件
                              // 提取当前的值，避免后续引用已回收的事件对象
                              const newValue = e.target && e.target.value;
                              setRecordSettings(prev => ({
                                ...prev,
                                delayTime: Math.max(0, Math.min(10, parseFloat(newValue) || 0))
                              }));
                            }}
                            style={CameraCardStyle.cameraSettingContainerItemInput}
                            min="0"
                            max="10"
                            step="0.1"
                            disabled={recordSettings.holdToRecord}
                          />
                        </label>
                      </div>
                      
                      <div style={{ 
                        ...CameraCardStyle.cameraSettingContainerItem,
                        opacity: recordSettings.holdToRecord ? 0.5 : 1,
                        pointerEvents: recordSettings.holdToRecord ? 'none' : 'auto'
                      }}>
                        <label style={CameraCardStyle.cameraSettingContainerItemLabel}>
                          <span>时长 (秒):</span>
                          <input 
                            type="number" 
                            value={recordSettings.duration} 
                            onChange={(e) => {
                              e.persist(); // 防止React事件池回收此事件
                              // 提取当前的值，避免后续引用已回收的事件对象
                              const newValue = e.target && e.target.value;
                              setRecordSettings(prev => ({
                                ...prev,
                                duration: Math.max(1, Math.min(10, parseInt(newValue) || 1))
                              }));
                            }}
                            style={CameraCardStyle.cameraSettingContainerItemInput}
                            min="1"
                            max="10"
                            disabled={recordSettings.holdToRecord}
                          />
                        </label>
                      </div>
                      
                      <div style={CameraCardStyle.cameraSettingContainerButtonContainer}>
                        <button 
                          onClick={() => setShowSettingsPanel(false)}
                          style={CameraCardStyle.cameraSettingContainerButtonReturn}
                        >
                          返回
                        </button>
                        
                        <button 
                          onClick={() => {
                            // 保存设置到localStorage
                            try {
                              localStorage.setItem('poseRecordSettings', JSON.stringify(recordSettings));
                              console.log('设置已保存:', recordSettings);
                            } catch (error) {
                              console.error('设置保存失败:', error);
                            }
                            setShowSettingsPanel(false);
                          }}
                          style={CameraCardStyle.cameraSettingContainerButtonSave}
                        >
                          保存设置
                        </button>
                      </div>
                    </div>
                  )}
              </div>
                
              <div style={ClassCardStyle.ContentExpandedContainerRight}>
                <div style={{
                    ...CameraCardStyle.sampleStats
                }}>
                    {node.samples.length === 0 ? 
                        '添加姿态样本（应至少添加10个）:' : 
                        `${node.samples.length} 个姿态样本（应至少添加10个）`
                      }
                </div>
                <div style={CameraCardStyle.samplesContentContainer}>
                    {node.samples.map(sample => (
                      <div 
                        key={sample.id} 
                        style={CameraCardStyle.sampleThumbnail}
                      >
                        <div 
                          style={{
                            position: 'absolute',
                            top: '4px',
                            right: '4px',
                            width: '20px',
                            height: '20px',
                            backgroundColor: 'rgba(255, 77, 79, 0.8)',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '12px',
                            cursor: 'pointer',
                            zIndex: 10,
                            opacity: 0,
                            transition: 'opacity 0.2s'
                          }}
                          onClick={(e) => {
                            e.stopPropagation(); // 防止事件冒泡
                            deleteSample(node.id, sample.id);
                          }}
                          onMouseEnter={(e) => e.currentTarget.style.opacity = '1'}
                          onMouseLeave={(e) => e.currentTarget.style.opacity = '0'}
                        >
                          ×
                        </div>
                        <canvas 
                          id={`sample-canvas-${sample.id}`} 
                          style={styles.poseCanvas}
                        />
                      </div>
                    ))}
                  </div>
              </div>
          </div>
        )}
      </div>
    )
  }

  //这里下面存放重构过的代码


  //渲染类别卡片
  const renderClassCard=(node)=>{
    if(node.isImported){
      return renderClassCardIsImported(node);
    }else{
      return renderCLassCardNotImported(node);
    }
  }

  //渲染预览面板具体内容
  const renderPreviewContainer = () => {
      return (
                  <div
                    id="preview-container"
                    style={styles.previewContainer}
                  />
      )
  }

  // 将图表绘制相关的useEffect移到顶层
  useEffect(() => {
    if (showDataChart && trainingHistory.epochs.length > 0) {
      const drawChart = () => {
        const drawAccuracyChart = () => {
          const canvas = accChartRef.current;
          if (!canvas) return;
          
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // 设置画布尺寸
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          
          // 增加顶部填充，为图例留出更多空间
          const padding = { top: 30, right: 20, bottom: 30, left: 40 };
          const chartWidth = canvas.width - padding.left - padding.right;
          const chartHeight = canvas.height - padding.top - padding.bottom;
          
          // 绘制网格背景
          ctx.fillStyle = '#f9f9f9';
          ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
          
          // 绘制网格线
          ctx.beginPath();
          ctx.strokeStyle = '#e0e0e0';
          ctx.lineWidth = 1;
          
          // 水平网格线
          for (let i = 0; i <= 10; i++) {
            const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(canvas.width - padding.right, y);
          }
          
          // 垂直网格线 - 根据数据点数量决定
          const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
          if (trainingHistory.epochs.length > 0) {
            for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
              if (i <= trainingHistory.epochs.length) {
                const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                ctx.moveTo(x, padding.top);
                ctx.lineTo(x, canvas.height - padding.bottom);
              }
            }
          }
          
          ctx.stroke();
          
          // 绘制坐标轴
          ctx.beginPath();
          ctx.strokeStyle = '#999';
          ctx.lineWidth = 1.5;
          
          // X轴
          ctx.moveTo(padding.left, canvas.height - padding.bottom);
          ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
          
          // Y轴
          ctx.moveTo(padding.left, padding.top);
          ctx.lineTo(padding.left, canvas.height - padding.bottom);
          ctx.stroke();
          
          // 如果没有数据，则不绘制
          if (trainingHistory.epochs.length === 0) return;
          
          // 计算比例尺
          const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
          
          // 绘制图例 - 移到顶部标题位置上方，避免挡住数据
          ctx.font = '12px Arial';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          
          // 训练准确率图例
          ctx.fillStyle = '#4f7df9';
          ctx.fillRect(padding.left, padding.top - 20, 15, 6);
          ctx.fillText('acc', padding.left + 20, padding.top - 20);
          
          // 验证准确率图例
          if (trainingHistory.valAccuracy.some(v => v > 0)) {
            ctx.fillStyle = '#f97d4f';
            ctx.fillRect(padding.left + 120, padding.top - 20, 15, 6);
            ctx.fillText('test acc', padding.left + 140, padding.top - 20);
          }
          
          // 绘制训练准确率
          if (trainingHistory.trainAccuracy.length > 0) {
            ctx.beginPath();
            ctx.strokeStyle = '#4f7df9';
            ctx.lineWidth = 2;
            
            trainingHistory.trainAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            trainingHistory.trainAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              ctx.beginPath();
              ctx.fillStyle = '#4f7df9';
              ctx.arc(x, y, 3, 0, Math.PI * 2);
              ctx.fill();
            });
          }
          
          // 绘制验证准确率
          if (trainingHistory.valAccuracy.length > 0 && trainingHistory.valAccuracy.some(v => v > 0)) {
            ctx.beginPath();
            ctx.strokeStyle = '#f97d4f';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 3]);
            
            trainingHistory.valAccuracy.forEach((acc, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (acc * chartHeight);
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制数据点
            trainingHistory.valAccuracy.forEach((acc, i) => {
              if (acc > 0) {
                const x = padding.left + i * xScale;
                const y = canvas.height - padding.bottom - (acc * chartHeight);
                
                ctx.beginPath();
                ctx.fillStyle = '#f97d4f';
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
              }
            });
          }
          
          // 绘制Y轴刻度
          ctx.fillStyle = '#666';
          ctx.textAlign = 'right';
          ctx.textBaseline = 'middle';
          
          for (let i = 0; i <= 10; i++) {
            const y = canvas.height - padding.bottom - (i / 10) * chartHeight;
            ctx.fillText((i / 10).toFixed(1), padding.left - 5, y);
          }
          
          // 绘制X轴刻度 - 每4个周期显示一个刻度
          const totalEpochs = trainingHistory.epochs.length;
          const labelStep = Math.max(1, Math.floor(totalEpochs / Math.min(10, totalEpochs)));
          
          if (totalEpochs > 0) {
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            
            // 绘制起点
            ctx.fillText(trainingHistory.epochs[0], padding.left, canvas.height - padding.bottom + 5);
            
            // 绘制中间点
            for (let i = labelStep; i < totalEpochs - 1; i += labelStep) {
              const x = padding.left + i * xScale;
              ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
            }
            
            // 绘制终点
            if (totalEpochs > 1) {
              ctx.fillText(
                trainingHistory.epochs[totalEpochs - 1], 
                padding.left + (totalEpochs - 1) * xScale, 
                canvas.height - padding.bottom + 5
              );
            }
          }
        };
        
        const drawLossChart = () => {
          const canvas = lossChartRef.current;
          if (!canvas) return;
          
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          
          // 设置画布尺寸
          canvas.width = canvas.offsetWidth;
          canvas.height = canvas.offsetHeight;
          
          // 增加顶部填充，为图例留出更多空间
          const padding = { top: 30, right: 20, bottom: 30, left: 40 };
          const chartWidth = canvas.width - padding.left - padding.right;
          const chartHeight = canvas.height - padding.top - padding.bottom;
          
          // 绘制网格背景
          ctx.fillStyle = '#f9f9f9';
          ctx.fillRect(padding.left, padding.top, chartWidth, chartHeight);
          
          // 如果没有数据，则不绘制
          if (trainingHistory.epochs.length === 0) return;
          
          // 计算损失的最大值，用于缩放Y轴
          const maxTrainLoss = Math.max(...trainingHistory.trainLoss);
          const maxValLoss = Math.max(...trainingHistory.valLoss.filter(v => v > 0));
          let maxLoss = Math.max(maxTrainLoss, maxValLoss);
          maxLoss = Math.max(maxLoss, 0.01); // 确保有最小值
          
          // 将maxLoss四舍五入到适当的值，例如0.1,0.2,0.5,1.0等
          const steps = 10;
          const roundedMaxLoss = Math.ceil(maxLoss * steps) / steps;
          maxLoss = roundedMaxLoss;
          
          // 绘制网格线
          ctx.beginPath();
          ctx.strokeStyle = '#e0e0e0';
          ctx.lineWidth = 1;
          
          // 水平网格线
          for (let i = 0; i <= steps; i++) {
            const y = canvas.height - padding.bottom - (i / steps) * chartHeight;
            ctx.moveTo(padding.left, y);
            ctx.lineTo(canvas.width - padding.right, y);
          }
          
          // 垂直网格线 - 根据数据点数量决定
          const gridStep = Math.min(10, Math.max(4, Math.floor(trainingHistory.epochs.length / 4)));
          if (trainingHistory.epochs.length > 0) {
            for (let i = 0; i <= trainingHistory.epochs.length; i += gridStep) {
              if (i <= trainingHistory.epochs.length) {
                const x = padding.left + (i / trainingHistory.epochs.length) * chartWidth;
                ctx.moveTo(x, padding.top);
                ctx.lineTo(x, canvas.height - padding.bottom);
              }
            }
          }
          
          ctx.stroke();
          
          // 绘制坐标轴
          ctx.beginPath();
          ctx.strokeStyle = '#999';
          ctx.lineWidth = 1.5;
          
          // X轴
          ctx.moveTo(padding.left, canvas.height - padding.bottom);
          ctx.lineTo(canvas.width - padding.right, canvas.height - padding.bottom);
          
          // Y轴
          ctx.moveTo(padding.left, padding.top);
          ctx.lineTo(padding.left, canvas.height - padding.bottom);
          ctx.stroke();
          
          // 绘制图例 - 移到顶部标题位置上方，避免挡住数据
          ctx.font = '12px Arial';
          ctx.textAlign = 'left';
          ctx.textBaseline = 'top';
          
          // 训练损失图例
          ctx.fillStyle = '#4fb14f';
          ctx.fillRect(padding.left, padding.top - 20, 15, 6);
          ctx.fillText('loss', padding.left + 20, padding.top - 20);
          
          // 验证损失图例
          if (trainingHistory.valLoss.some(v => v > 0)) {
            ctx.fillStyle = '#b14f4f';
            ctx.fillRect(padding.left + 120, padding.top - 20, 15, 6);
            ctx.fillText('test loss', padding.left + 140, padding.top - 20);
          }
          
          // 计算比例尺
          const xScale = chartWidth / (trainingHistory.epochs.length - 1 || 1);
          
          // 绘制训练损失
          if (trainingHistory.trainLoss.length > 0) {
            ctx.beginPath();
            ctx.strokeStyle = '#4fb14f';
            ctx.lineWidth = 2;
            
            trainingHistory.trainLoss.forEach((loss, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
              
              if (i === 0) {
                ctx.moveTo(x, y);
              } else {
                ctx.lineTo(x, y);
              }
            });
            
            ctx.stroke();
            
            // 绘制数据点
            trainingHistory.trainLoss.forEach((loss, i) => {
              const x = padding.left + i * xScale;
              const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
              
              ctx.beginPath();
              ctx.fillStyle = '#4fb14f';
              ctx.arc(x, y, 3, 0, Math.PI * 2);
              ctx.fill();
            });
          }
          
          // 绘制验证损失
          if (trainingHistory.valLoss.length > 0 && trainingHistory.valLoss.some(v => v > 0)) {
            ctx.beginPath();
            ctx.strokeStyle = '#b14f4f';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 3]);
            
            trainingHistory.valLoss.forEach((loss, i) => {
              if (loss > 0) {
                const x = padding.left + i * xScale;
                const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                
                if (i === 0 || trainingHistory.valLoss[i-1] <= 0) {
                  ctx.moveTo(x, y);
                } else {
                  ctx.lineTo(x, y);
                }
              }
            });
            
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制数据点
            trainingHistory.valLoss.forEach((loss, i) => {
              if (loss > 0) {
                const x = padding.left + i * xScale;
                const y = canvas.height - padding.bottom - (loss / maxLoss) * chartHeight;
                
                ctx.beginPath();
                ctx.fillStyle = '#b14f4f';
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
              }
            });
          }
          
          // 绘制Y轴刻度
          ctx.fillStyle = '#666';
          ctx.textAlign = 'right';
          ctx.textBaseline = 'middle';
          
          for (let i = 0; i <= steps; i++) {
            const y = canvas.height - padding.bottom - (i / steps) * chartHeight;
            ctx.fillText((i / steps * maxLoss).toFixed(2), padding.left - 5, y);
          }
          
          // 绘制X轴刻度 - 使用与准确率图表相同的逻辑
          const totalEpochs = trainingHistory.epochs.length;
          const labelStep = Math.max(1, Math.floor(totalEpochs / Math.min(10, totalEpochs)));
          
          if (totalEpochs > 0) {
            ctx.fillStyle = '#666';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            
            // 绘制起点
            ctx.fillText(trainingHistory.epochs[0], padding.left, canvas.height - padding.bottom + 5);
            
            // 绘制中间点
            for (let i = labelStep; i < totalEpochs - 1; i += labelStep) {
              const x = padding.left + i * xScale;
              ctx.fillText(trainingHistory.epochs[i], x, canvas.height - padding.bottom + 5);
            }
            
            // 绘制终点
            if (totalEpochs > 1) {
              ctx.fillText(
                trainingHistory.epochs[totalEpochs - 1], 
                padding.left + (totalEpochs - 1) * xScale, 
                canvas.height - padding.bottom + 5
              );
            }
          }
        };
        
        // 绘制两个图表
        drawAccuracyChart();
        drawLossChart();
      };
      
      // 初始绘制并添加窗口大小变化监听
      drawChart();
      window.addEventListener('resize', drawChart);
      
      // 组件卸载时清理
      return () => {
        window.removeEventListener('resize', drawChart);
      };
    }
  }, [showDataChart, trainingHistory]);

  // 4. 实现 renderDataChart 函数 (参考 SoundMode)
  const renderDataChart = () => {
    if (!showDataChart) return null;

    // 使用临时变量存储训练历史数据的状态，避免使用stale数据
    const currentTrainingHistory = trainingHistory;
    const hasData = currentTrainingHistory.epochs.length > 0;

    // --- 复制 SoundMode 中的样式常量 ---
    // 图表容器的样式
    const chartPanelStyle = {
      position: 'absolute',
      top: 0,
      right: 0,
      width: '300px',
      height: '100%', 
      backgroundColor: 'white',
      boxShadow: '-4px 0 12px rgba(0, 0, 0, 0.1)',
      zIndex: 100,
      transform: showDataChart ? 'translateX(0)' : 'translateX(100%)',
      transition: 'transform 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      borderLeft: '1px solid #eee'
    };
    
    const chartHeaderStyle = {
      padding: '16px',
      borderBottom: '1px solid #eee',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    };
    
    const chartContentStyle = {
      flex: 1,
      padding: '16px',
      overflowY: 'auto'
    };
    
    const closeButtonStyle = {
      background: 'none',
      border: 'none',
      fontSize: '20px',
      cursor: 'pointer',
      color: '#666'
    };
    
    const chartStyle = {
      marginBottom: '24px',
      padding: '16px',
      border: '1px solid #eee',
      borderRadius: '8px',
      backgroundColor: '#f9f9f9'
    };
    
    // 图表图标样式
    const chartIconStyle = {
      marginRight: '8px', 
      display: 'inline-flex',
      verticalAlign: 'middle',
      width: '20px',
      height: '20px',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#4f7df9'
    };
    
    // 介绍内容块样式
    const introBlockStyle = {
      padding: '12px',
      backgroundColor: '#f0f7ff',
      borderRadius: '8px',
      fontSize: '14px',
      lineHeight: 1.5,
      color: '#4a5568',
      marginBottom: '24px',
      border: '1px solid #e3eeff'
    };
    // --- 样式常量结束 ---

    // 复制 soundMode.jsx 中 renderDataChart 函数内的样式常量
    // （为简洁起见，这里省略了具体的样式代码，请从 soundMode.jsx 复制过来）
    // const chartPanelStyle = {
    //     position: 'absolute',
    //     top: 0,
    //     right: 0,
    //     width: '300px',
    //     height: '100%',
    //     backgroundColor: 'white',
    //     boxShadow: '-4px 0 12px rgba(0, 0, 0, 0.1)',
    //     zIndex: 100,
    //     transform: showDataChart ? 'translateX(0)' : 'translateX(100%)',
    //     transition: 'transform 0.3s ease',
    //     display: 'flex',
    //     flexDirection: 'column',
    //     overflow: 'hidden',
    //     borderLeft: '1px solid #eee'
    // };

    // const chartHeaderStyle = {
    //     padding: '16px',
    //     borderBottom: '1px solid #eee',
    //     display: 'flex',
    //     justifyContent: 'space-between',
    //     alignItems: 'center'
    // };

    // const chartContentStyle = {
    //     flex: 1,
    //     padding: '16px',
    //     overflowY: 'auto'
    // };

    // const closeButtonStyle = {
    //     background: 'none',
    //     border: 'none',
    //     fontSize: '20px',
    //     cursor: 'pointer',
    //     color: '#666'
    // };

    // const chartStyle = {
    //     marginBottom: '24px',
    //     padding: '16px',
    //     border: '1px solid #eee',
    //     borderRadius: '8px',
    //     backgroundColor: '#f9f9f9'
    // };

    // const chartIconStyle = {
    //   marginRight: '8px',
    //   display: 'inline-flex',
    //   verticalAlign: 'middle',
    //   width: '20px',
    //   height: '20px',
    //   alignItems: 'center',
    //   justifyContent: 'center',
    //   color: '#4f7df9' // 可以根据需要调整颜色
    // };

    // const introBlockStyle = {
    //   padding: '12px',
    //   backgroundColor: '#f0f2f5', // 调整背景色以匹配姿态模式风格
    //   borderRadius: '8px',
    //   fontSize: '14px',
    //   lineHeight: 1.5,
    //   color: '#4a5568',
    //   marginBottom: '24px',
    //   border: '1px solid #e1e4e8' // 调整边框色
    // };


    return (
      <>
        <div style={chartPanelStyle}>
          <div style={chartHeaderStyle}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
              <span style={chartIconStyle}>
                <svg viewBox="0 0 24 24" width="20" height="20" stroke="currentColor" strokeWidth="2" fill="none">
                  <path d="M2 22h20M2 2v16h20V2H2z" />
                  <path d="M7 14l3-3 2 2 3-3 3 3" />
                </svg>
              </span>
              数据图表
            </h3>
            <button 
              style={closeButtonStyle}
              onClick={() => setShowDataChart(false)}
            >
              ×
            </button>
          </div>
          
          <div style={chartContentStyle}>
            {/* 添加介绍内容 */}
            <div style={introBlockStyle}>
              <strong style={{ fontSize: '15px', display: 'block', marginBottom: '4px' }}>训练图表</strong>
              <p style={{ margin: '0' }}>这里的图表可以帮助您了解模型的训练和运行情况</p>
            </div>
            
            {hasData ? (
              <>
                <div style={{...chartStyle, height: '220px'}}>
                  <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#4f7df9', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M23 6l-9.5 9.5-5-5L1 18" />
                      </svg>
                    </span>
                    每个周期的准确度
                  </h4>
                  <div style={{ position: 'relative', height: '180px', width: '100%' }}>
                    <canvas 
                      ref={accChartRef} 
                      width="280" 
                      height="180"
                      style={{ width: '100%', height: '100%' }}
                    />
                  </div>
                </div>
                
                <div style={{...chartStyle, height: '220px'}}>
                  <h4 style={{ marginTop: 0, display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#4fb14f', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M3 3v18h18" />
                        <path d="M18 12l-6-6-6 6" />
                      </svg>
                    </span>
                    每个周期的损失
                  </h4>
                  <div style={{ position: 'relative', height: '180px', width: '100%' }}>
                    <canvas 
                      ref={lossChartRef} 
                      width="280" 
                      height="180"
                      style={{ width: '100%', height: '100%' }}
                    />
                  </div>
                </div>
                
                <div style={{ marginTop: '20px', backgroundColor: 'white', padding: '16px', borderRadius: '8px', border: '1px solid #eee' }}>
                  <h4 style={{ margin: '0 0 12px 0', display: 'flex', alignItems: 'center' }}>
                    <span style={{ color: '#718096', marginRight: '6px' }}>
                      <svg viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 3v18M3 12h18" />
                        <path d="M5 5l14 14M5 19l14-14" />
                      </svg>
                    </span>
                    数据统计
                  </h4>
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr>
                        <th style={{ padding: '8px', textAlign: 'left', borderBottom: '1px solid #eee', color: '#718096' }}>指标</th>
                        <th style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', color: '#718096' }}>训练集</th>
                        <th style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', color: '#718096' }}>验证集</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>最终准确度</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#4f7df9' }}>
                          {currentTrainingHistory.trainAccuracy.length > 0 
                            ? (currentTrainingHistory.trainAccuracy[currentTrainingHistory.trainAccuracy.length - 1] * 100).toFixed(2) + '%' 
                            : '-'}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#f97d4f' }}>
                          {currentTrainingHistory.valAccuracy.length > 0 && currentTrainingHistory.valAccuracy[currentTrainingHistory.valAccuracy.length - 1] > 0
                            ? (currentTrainingHistory.valAccuracy[currentTrainingHistory.valAccuracy.length - 1] * 100).toFixed(2) + '%' 
                            : '-'}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>最终损失值</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#4fb14f' }}>
                          {currentTrainingHistory.trainLoss.length > 0 
                            ? currentTrainingHistory.trainLoss[currentTrainingHistory.trainLoss.length - 1].toFixed(4)
                            : '-'}
                        </td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold', color: '#b14f4f' }}>
                          {currentTrainingHistory.valLoss.length > 0 && currentTrainingHistory.valLoss[currentTrainingHistory.valLoss.length - 1] > 0
                            ? currentTrainingHistory.valLoss[currentTrainingHistory.valLoss.length - 1].toFixed(4)
                            : '-'}
                        </td>
                      </tr>
                      <tr>
                        <td style={{ padding: '8px', borderBottom: '1px solid #eee' }}>总训练周期</td>
                        <td style={{ padding: '8px', textAlign: 'right', borderBottom: '1px solid #eee', fontWeight: 'bold' }} colSpan={2}>
                          {currentTrainingHistory.epochs.length}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 20px', color: '#999', backgroundColor: '#f9f9f9', borderRadius: '8px', marginTop: '20px' }}>
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="#ccc" strokeWidth="1.5" style={{ margin: '0 auto 16px', display: 'block' }}>
                  <path d="M2 22h20M2 2v16h20V2H2z" />
                  <path d="M7 14l3-3 2 2 3-3 3 3" strokeDasharray="4" />
                </svg>
                <p style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '8px' }}>尚无训练数据</p>
                <p style={{ fontSize: '14px' }}>训练模型后将在此显示训练过程的准确率和损失</p>
              </div>
            )}
          </div>
        </div>
      </>
    );
  };


  // 如果你要在预览头部增加内容，你应该在这里添加
  const renderPreviewHeaderFunction = () => {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        {/* 摄像头设置图标 */}
        <div
          onClick={() => setShowCameraSettings(true)}
          style={{
            width: '24px',
            height: '24px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '4px',
            backgroundColor: 'transparent',
            transition: 'background-color 0.2s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
          }}
          title="摄像头设置"
        >
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="#666" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.01127 9.77251C4.28054 9.5799 4.48571 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="#666" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
    )
  }

  
  // 从localStorage加载设置
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('poseRecordSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        // 确保设置中包含所有必需的属性，否则使用默认值
        const validatedSettings = {
          fps: parsedSettings.fps && parsedSettings.fps > 0 ? parsedSettings.fps : 10,
          holdToRecord: typeof parsedSettings.holdToRecord === 'boolean' ? parsedSettings.holdToRecord : true,
          delayTime: parsedSettings.delayTime >= 0 ? parsedSettings.delayTime : 0,
          duration: parsedSettings.duration > 0 ? parsedSettings.duration : 3,
          selectedDeviceId: parsedSettings.selectedDeviceId || ''
        };
        setRecordSettings(validatedSettings);
        console.log('已加载设置:', validatedSettings);
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      // 如果加载失败，设置为默认值
      setRecordSettings({
        fps: 10,
        holdToRecord: true,
        delayTime: 0,
        duration: 3,
        selectedDeviceId: ''
      });
    }
  }, []);

  // 监听recordSettings的变化，更新recordSettingsRef
  useEffect(() => {
    recordSettingsRef.current = recordSettings;
  }, [recordSettings]);
  
  // 训练主组件的属性
  const trainMainProps={
    //ref元素
    containerRef,
    classesPanelContentRef,

    //传入面板方法
    //渲染连接线
    //渲染预览内容面板
    
    //传入训练的属性或者方法
    //类别属性
    classNodes,
    //类别方法
    renderClassCard,
    addClassNode,

    //训练属性
    trainParams,
    setTrainParams,
    DefaultTrainParams,
    isTraining,
    training,
    advancedExpanded,
    setAdvancedExpanded,
    canTrainRef,

    //训练方法
    train,

    //传入预览的属性或者方法
    //预览属性
    modelTrained,
    predicting,
    confidences,

    //预览方法
    exportModelToBlocks,
    renderPreviewHeaderFunction,
    renderPreviewContainer,


    //ref,用于回传上面组件需要调用的方法
    trainMainFunctionRef,

    //数据图表
    showDataChart,
    setShowDataChart,
    renderDataChart,
}
  return (
      <>
        <TrainMain {...trainMainProps} />

        {/* 摄像头设置模态框 */}
        {showCameraSettings && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000
          }}>
            <div style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '24px',
              minWidth: '400px',
              maxWidth: '500px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '20px'
              }}>
                <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>摄像头设置</h3>
                <button
                  onClick={() => setShowCameraSettings(false)}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '20px',
                    cursor: 'pointer',
                    padding: '4px',
                    color: '#666'
                  }}
                >
                  ×
                </button>
              </div>

              <div style={{ marginBottom: '16px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  选择摄像头设备：
                </label>
                <select
                  value={recordSettings.selectedDeviceId}
                  onChange={(e) => {
                    const newSettings = { ...recordSettings, selectedDeviceId: e.target.value };
                    setRecordSettings(newSettings);
                    recordSettingsRef.current = newSettings;
                    localStorage.setItem('poseRecordSettings', JSON.stringify(newSettings));
                  }}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                >
                  <option value="">默认摄像头</option>
                  {videoDevices.map(device => (
                    <option key={device.deviceId} value={device.deviceId}>
                      {device.label || `摄像头 ${device.deviceId.slice(0, 8)}...`}
                    </option>
                  ))}
                </select>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '12px',
                marginTop: '24px'
              }}>
                <button
                  onClick={() => setShowCameraSettings(false)}
                  style={{
                    padding: '8px 16px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: 'white',
                    cursor: 'pointer'
                  }}
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    setShowCameraSettings(false);
                    // 这里可以添加应用设置的逻辑
                  }}
                  style={{
                    padding: '8px 16px',
                    border: 'none',
                    borderRadius: '4px',
                    backgroundColor: '#4f46e5',
                    color: 'white',
                    cursor: 'pointer'
                  }}
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        )}
      </>
  );
};


export default PoseMode;