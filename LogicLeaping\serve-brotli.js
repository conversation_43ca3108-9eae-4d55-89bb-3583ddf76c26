/**
 * 支持Brotli压缩的本地开发服务器
 * 用法: node serve-brotli.js
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const PORT = 8601;

// 静态文件目录
const BUILD_DIR = path.join(__dirname, 'build');

// MIME类型映射
const MIME_TYPES = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2'
};

// 自定义中间件，优先提供.br文件
app.use((req, res, next) => {
  // 跳过API请求
  if (req.path.startsWith('/api/')) {
    return next();
  }

  // 只处理特定类型的文件
  if (req.path.match(/\.(js|css|html|svg|json)$/)) {
    const acceptEncoding = req.headers['accept-encoding'] || '';
    
    if (acceptEncoding.includes('br')) {
      // 构建.br文件的路径
      const filePath = path.join(BUILD_DIR, req.path);
      const brPath = `${filePath}.br`;
      
      // 检查.br文件是否存在
      if (fs.existsSync(brPath)) {
        console.log(`提供Brotli压缩文件: ${req.path}.br`);
        
        // 设置正确的Content-Type
        const ext = path.extname(req.path);
        const contentType = MIME_TYPES[ext] || 'application/octet-stream';
        
        // 设置响应头
        res.set({
          'Content-Encoding': 'br',
          'Content-Type': contentType,
          'Vary': 'Accept-Encoding',
          'Cache-Control': 'public, max-age=31536000, immutable'
        });
        
        // 直接发送.br文件
        return fs.createReadStream(brPath).pipe(res);
      }
    }
  }
  
  // 如果不是.br文件或不存在，继续下一个中间件
  next();
});

// 设置静态文件服务
app.use(express.static(BUILD_DIR, {
  // 添加缓存控制头
  setHeaders: (res, filePath) => {
    const ext = path.extname(filePath);
    // 静态资源使用长缓存
    if (['.js', '.css', '.png', '.jpg', '.gif', '.svg', '.woff', '.woff2'].includes(ext)) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    } else {
      // HTML等文件使用较短的缓存
      res.setHeader('Cache-Control', 'public, max-age=3600');
    }
  }
}));

// 处理单页应用路由 - 修复通配符路由
app.use((req, res) => {
  // 对于所有不匹配静态文件的请求，返回index.html
  res.sendFile(path.join(BUILD_DIR, 'index.html'));
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`Brotli支持的开发服务器运行在 http://localhost:${PORT}`);
  console.log(`提供来自 ${BUILD_DIR} 的文件`);
  console.log('使用浏览器开发者工具检查响应头中的Content-Encoding: br');
}); 