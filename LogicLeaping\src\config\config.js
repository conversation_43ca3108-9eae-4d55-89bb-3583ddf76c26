// 开发环境配置
export const DEV_CONFIG = {
    API_HOST: "http://127.0.0.1",
    API_PORT: "8601",
    BASE_URL: "${API_HOST}:${API_PORT}",
    SERVER_URL: "http://127.0.0.1:8602",
    API_URL: "http://127.0.0.1:8003",
    WS_URL: "ws://127.0.0.1:8001/ws",
    WS_SERVER_URL: "ws://127.0.0.1:8602/server/ws",
};

// 生产环境配置
export const PROD_CONFIG = {
    API_HOST: "https://www.logicleapai.cn",
    API_PORT: "8601",
    BASE_URL: "${API_HOST}:${API_PORT}",
    SERVER_URL: "https://www.logicleapai.cn/server",
    API_URL: "https://www.logicleapai.cn/api",
    WS_URL: "wss://logicleapai.cn/ws",
    WS_SERVER_URL: "wss://logicleapai.cn/server/ws",
};

// 根据环境导出相应配置
export const CONFIG =
    process.env.NODE_ENV === "production" ? PROD_CONFIG : DEV_CONFIG;
export const OSS_DOMAIN = "https://logicleap.oss-cn-guangzhou.aliyuncs.com";

// 导出单独的配置项,方便直接使用
export const {
    API_HOST,
    API_PORT,
    BASE_URL,
    SERVER_URL,
    API_URL,
    WS_URL,
    WS_SERVER_URL,
} = CONFIG;
