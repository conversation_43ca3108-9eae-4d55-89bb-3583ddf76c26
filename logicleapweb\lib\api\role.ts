// /lib/api/role.ts
import request from '../request';
const baseUrl = '/api/user/srch/templates';

// /**
//  * 获取角色列表
//  */
// export const getRoleList = (params?: {
//   keyword?: string;
//   status?: number;
// }) => request.post('/app/user/role/list', params);

/**
 * 获取角色列表
 */
export const getRoleList = (params?: {
  keyword?: string;
  status?: number;
}) => request.post('/user-role/condition', params);

// /**
//  * 获取角色详情
//  */
// export const getRoleInfo = (id: number) =>
//   request.post('/app/user/role/info', { id });

// /**
//  * 创建角色
//  */
// export const createRole = (params: {
//   roleName: string;
//   roleCode: string;
//   roleDescription?: string;
// }) => request.post('/app/user/role/add', params);

// /**
//  * 更新角色
//  */
// export const updateRole = (params: {
//   id: number;
//   roleName?: string;
//   roleCode?: string;
//   roleDescription?: string;
// }) => request.post('/app/user/role/update', params);

// /**
//  * 删除角色
//  */
// export const deleteRole = (id: number) =>
//   request.post('/app/user/role/delete', { id });

/**
 * 获取角色权限模板列表
 */
export const getRoleTemplateList = (id: number) => {
  return request.get(baseUrl + '/list/' + id);
}

/**
 * 获取模板详情
 */
export const getTemplateInfo = (id: number) =>
  request.get('/api/user/srch/templates/' + id);

/**
 * 创建角色权限模板
 */
export const createTemplate = (params: {
  userId: number;
  roleId: number;
  templateName: string;
  templateDescription?: string;
  permissions?: {
    extensions?: Array<{
      extensionId: string;
      isEnabled: boolean;
    }>;
    blocks?: Array<{
      extensionId: string;
      blockId: string;
      isEnabled: boolean;
    }>;
  };
}) => {
  return request.post('/api/user/srch/templates/create', params).then(response => {
    return response;
  }).catch(error => {
    throw error;
  });
};

/**
 * 更新权限模板
 */
export const updateTemplate = (params: {
  id: number;
  templateName?: string;
  templateDescription?: string;
  permissions?: {
    extensions?: Array<{
      extensionId: string;
      isEnabled: boolean;
    }>;
    blocks?: Array<{
      extensionId: string;
      blockId: string;
      isEnabled: boolean;
    }>;
  };
  isOfficial?: boolean;
}) => request.post('/api/user/srch/templates/update', params);

/**
 * 删除权限模板
 */
export const deleteTemplate = (id: number) =>
  request.delete('/api/user/srch/templates/' + id);

/**
 * 设置默认模板
 */
export const setDefaultTemplate = (templateId: number) =>
  request.post('/api/user/srch/templates/' + templateId + '/default');

// /**
//  * 复制权限模板
//  */
// export const copyTemplate = (params: {
//   sourceTemplateId: number;
//   targetRoleId: number;
//   newTemplateName: string;
// }) => request.post('/app/user/role/template/copy', params);

/**
 * 获取用户的角色和权限信息
 */
export const getUserRoleInfo = (userId: number) =>
  request.post('/api/user-join-role/getUserRoleAndTemplateId', { userId });

// /**
//  * 分配角色
//  */
// export const assignRole = (params: {
//   userId: number;
//   roleId: number;
//   templateId?: number;
// }) => request.post('/app/user/role/assign', params);

// /**
//  * 批量分配用户角色
//  */
// export const batchAssignUserRole = (params: {
//   userIds: number[];
//   roleId: number;
//   templateId?: number;
// }) => request.post('/app/user/role/user/batchAssign', params);

// /**
//  * 移除用户角色
//  */
// export const removeUserRole = (params: {
//   userId: number;
//   roleId: number;
// }) => request.post('/app/user/role/user/remove', params);

// /**
//  * 获取角色下的所有用户
//  */
// export const getRoleUsers = (params: {
//   roleId: number;
//   page?: number;
//   size?: number;
// }) => request.post('/app/user/role/users', params);

/**
 * 新增用户角色关联    okok
 */
export const addUserJoinRole = (params: {
  userId: number;
  roleId: number;
  templateId?: number;
  originalTemplateId?: number;
}) => request.post('/api/user-join-role/createUserJoinRole', params);

/**
 * 批量新增用户角色关联
 */
export const batchAddUserJoinRole = (params: {
  users: Array<{
    userId: number;
    roleId: number;
    templateId?: number;
    originalTemplateId?: number;
  }>;
}) => request.post('/api/user-join-role/batchCreateUserJoinRole', params);

/**
 * 获取官方模板列表
 */
export const getOfficialTemplates = () =>
  request.get(baseUrl + '/official/list');

// /**
//  * 从官方模板创建自定义模板
//  */
// export const createFromOfficialTemplate = (params: {
//   sourceTemplateId: number;
//   roleId: number;
//   userId: number;
//   templateName: string;
// }) => request.post('/app/user/role/template/createFromOfficial', params);

/**
 * 通过page获取官方模板,从下面修改而来，上面那个没有页面选择
 */
export const getOfficialTemplateList = (page?: number, size?: number) => {
  return request.post('/api/user/srch/template/official/list/page', { page, size });
};

// 获取所有模板（管理员）
export const getAdminTemplateList = (page?: number, size?: number) => {
  return request.post('/api/user/srch/templates/list', { page, size });
};


/**
 * 删除官方模板（管理员专用）
 */
export const deleteOfficialTemplate = (params: {
  id: number;
  operatorId: number;
}) => request.post('/api/user/srch/template/official/delete', params);

/**
 * 获取用户当前使用的模板
 */
export const getUserCurrentTemplate = (userId: number) => {
  return request.get(baseUrl + '/current/' + userId);
}

/**
 * 获取老师的学生使用的模板列表
 */
export const getStudentTemplates = (params: {
  teacherId: number;
  page?: number;
  size?: number;
}) => {
  return request.post(baseUrl + '/teacher_students', params);
}

/**
 * 获取系统默认模板列表
 */
export const getTeacherDefaultTemplate = () =>
  request.post('/api/user/srch/template/teacher/default');