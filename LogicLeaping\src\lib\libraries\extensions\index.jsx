import React from 'react';
import { FormattedMessage } from 'react-intl';

import logicleapBaseIconURL from './logicleap_base/logo_base_1.png';
import logicleapBaseInsetIconURL from './logicleap_base/base.svg';

import logicleapVoiceIconURL from './logicleap_voice/logo_voice_1.png';
import logicleapVoiceInsetIconURL from './logicleap_voice/voice.svg';

import logicleapVoiceRecognizeIconURL from './logicleap_voice_recognize/voice_recognize.png';
import logicleapVoiceRecognizeInsetIconURL from './logicleap_voice_recognize/voice_recognize.svg';

import logicleapRecorderIconURL from './logicleap_recorder/logicleap_recorder_1.png';
import logicleapRecorderInsetIconURL from './logicleap_recorder/recorder.svg';

import logicleapTextIconURL from './logicleap_text/logo_text_1.png';
import logicleapTextInsetIconURL from './logicleap_text/text.svg';

import logicleapImageIconURL from './logicleap_image/logo_image_1.png';
import logicleapImageInsetIconURL from './logicleap_image/image.svg';

import logicleapImageRecognizeIconURL from './logicleap_image_recognize/logo_image_recognize_1.png';
import logicleapImageRecognizeInsetIconURL from './logicleap_image_recognize/image_recognize.svg';

import listIconURL from './list/list_1.png';
import listInsetIconURL from './list/list.svg';

import cameraIconURL from './camera/camera_1.png';
import cameraInsetIconURL from './camera/camera.svg';

import trainIconURL from './train/train_1.png';
import trainInsetIconURL from './train/train.svg';

import musicIconURL from './music/music.png';
import musicInsetIconURL from './music/music-small.svg';

import penIconURL from './pen/pen.png';
import penInsetIconURL from './pen/pen-small.svg';

import segmentIconURL from './segment/segment.png';
import segmentInsetIconURL from './segment/segment.svg';

import imageEnhanceIconURL from './image_enhance/image_enhance.png';
import imageEnhanceInsetIconURL from './image_enhance/enhance.svg';

import imageScoreIconURL from './image_score/image_score.png';
import imageScoreInsetIconURL from './image_score/score.svg';

import imageTrainIconURL from './logicleap_image_train/image_train_1.png';
import imageTrainInsetIconURL from './logicleap_image_train/image_train.svg';


import poseTrainIconURL from './logicleap_pose_train/pose_train.png';
import poseTrainInsetIconURL from './logicleap_pose_train/pose_train.svg';

import soundTrainIconURL from './logicleap_sound_train/sound_train_1.png';
import soundTrainInsetIconURL from './logicleap_sound_train/sound_train.svg';

import faceRecognitionIconURL from './logicleap_face_recognition/face_recognition.png';
import faceRecognitionInsetIconURL from './logicleap_face_recognition/face_recognition.svg';

import faceOneContrastOneIconURL from './faceOneContrastOne/faceOneContrastOne.png';
import faceOneContrastOneInsetIconURL from './faceOneContrastOne/faceOneContrastOne.svg';

import expressIconURL from './logicleap_express/express.png';
import expressInsetIconURL from './logicleap_express/express.svg';


import logicleapVoiceprintRecognitionIconURL from './logicleap_voiceprint_recognition/voiceprint_recognition.png';
import logicleapVoiceprintRecognitionInsetIconURL from './logicleap_voiceprint_recognition/voiceprint_recognition.svg';

// 添加静态手势识别扩展的图标导入
import staticGestureRecognitionIconURL from './logicleap_static_gesture_recognition/static_gesture_recognition.png';
import staticGestureRecognitionInsetIconURL from './logicleap_static_gesture_recognition/static_gesture_recognition.svg';

import voiceprintRecognitionIconURL from './logicleap_voiceprint_recognition/voiceprint_recognition.png';
import voiceprintRecognitionInsetIconURL from './logicleap_voiceprint_recognition/voiceprint_recognition.svg';

import objectDetectionIconURL from './logicleap_object_detection/object_detection.png';
import objectDetectionInsetIconURL from './logicleap_object_detection/object_detection.svg';

import { loadPermissions, getPermissions, setPermissions } from '../../permissions-manager';



// 移除重复的权限状态管理
let extensionPermissions = null;

// 优化权限预加载函数
const loadExtensionPermissions = async () => {
  try {
    // 使用permissions-manager加载权限
    const permissions = await loadPermissions();
    if (permissions) {
      // 提取扩展相关的权限
      extensionPermissions = {};
      const blocks = permissions.blocks || [];

      // 按扩展ID分组权限数据
      blocks.forEach(p => {
        if (!extensionPermissions[p.extensionId]) {
          extensionPermissions[p.extensionId] = {};
        }
        extensionPermissions[p.extensionId][p.blockId] = p.isEnabled;
      });

      // console.log('扩展权限预加载完成:', extensionPermissions);
      return extensionPermissions;
    }
  } catch (error) {
    console.error('扩展权限预加载失败:', error);
  }
  return null;
};

// 导出权限获取方法
export const getExtensionPermissions = (extensionId) => {
  // 如果还没有加载权限，尝试从permissions-manager获取
  if (!extensionPermissions) {
    const permissions = getPermissions();
    if (permissions && permissions.blocks) {
      extensionPermissions = {};
      permissions.blocks.forEach(p => {
        if (!extensionPermissions[p.extensionId]) {
          extensionPermissions[p.extensionId] = {};
        }
        extensionPermissions[p.extensionId][p.blockId] = p.isEnabled;
      });
    }
  }
  return extensionPermissions ? extensionPermissions[extensionId] || {} : {};
};

// 清除扩展权限缓存
export const clearExtensionPermissions = () => {
  extensionPermissions = null;
};

// 在页面加载时预加载权限
loadExtensionPermissions();

export default [
  {
    name: '积木拓展',
    isTitle: true
  },
  {
    name: "基础AI设置",
    extensionId: 'logicleapBase',
    iconURL: logicleapBaseIconURL,
    insetIconURL: logicleapBaseInsetIconURL,
    description: "包含图片处理，上传，计算等模块。",
    featured: true
  },
  {
    name: "大语言模型",
    extensionId: 'logicleapText',
    iconURL: logicleapTextIconURL,
    insetIconURL: logicleapTextInsetIconURL,
    description: "包含AI大语言模型对话模块",
    featured: true
  },
  {
    name: "AI绘画",
    extensionId: 'logicleapImage',
    iconURL: logicleapImageIconURL,
    insetIconURL: logicleapImageInsetIconURL,
    description: "包含AI图像生成模块",
    featured: true
  },
  {
    name: "视觉识别",
    extensionId: 'logicleapImageRecognize',
    iconURL: logicleapImageRecognizeIconURL,
    insetIconURL: logicleapImageRecognizeInsetIconURL,
    description: "包含AI图像识别模块",
    featured: true
  },
  {
    name: "AI语音",
    extensionId: 'logicleapVoice',
    iconURL: logicleapVoiceIconURL,
    insetIconURL: logicleapVoiceInsetIconURL,
    description: "包含语言合成等模块",
    featured: true
  },
  {
    name: "语音识别",
    extensionId: 'logicleapVoiceRecognize',
    iconURL: logicleapVoiceRecognizeIconURL,
    insetIconURL: logicleapVoiceRecognizeInsetIconURL,
    description: "包含AI语音识别模块",
    featured: true
  },
  {
    name: "录音工具",
    extensionId: 'logicleapRecorder',
    iconURL: logicleapRecorderIconURL,
    insetIconURL: logicleapRecorderInsetIconURL,
    description: "包含录音、播放和文件管理模块",
    featured: true
  },
  {
    name: "物体检测",
    extensionId: 'objectDetection',
    iconURL: objectDetectionIconURL,
    insetIconURL: objectDetectionInsetIconURL,
    description: "包含物体检测模块",
    featured: true
  },
  {
    name: "声纹识别",
    extensionId: 'logicleapVoiceprintRecognition',
    iconURL: voiceprintRecognitionIconURL,
    insetIconURL: voiceprintRecognitionInsetIconURL,
    description: "包含声纹识别模块",
    featured: true
  },
  // {
  //     name:"二次元转换",
  //     extensionId: 'twoDConversion',
  //     iconURL: twoDConversionIconURL,
  //     insetIconURL: twoDConversionInsetIconURL,
  //     description:"包含二次元转换模块",
  //     featured: true
  // },
  {
    name: "静态手势识别",
    extensionId: 'logicleapStaticGestureRecognition',
    iconURL: staticGestureRecognitionIconURL,
    insetIconURL: staticGestureRecognitionInsetIconURL,
    description: "包含手势识别模块",
    featured: true
  },
  {
    name: "esp32音频",
    extensionId: 'logicleapEsp32Audio',
    iconURL: staticGestureRecognitionIconURL,
    insetIconURL: staticGestureRecognitionInsetIconURL,
    description: "esp32音频",
    featured: true
  },
  {
    name: "图像分割",
    extensionId: 'segment',
    iconURL: segmentIconURL,
    insetIconURL: segmentInsetIconURL,
    description: "包含图像分割模块",
    featured: true
  },
  {
    name: "图像高清放大",
    extensionId: 'imageEnhance',
    iconURL: imageEnhanceIconURL,
    insetIconURL: imageEnhanceInsetIconURL,
    description: "包含图像增强模块",
    featured: true
  },
  {
    name: "图像评分",
    extensionId: 'imageScore',
    iconURL: imageScoreIconURL,
    insetIconURL: imageScoreInsetIconURL,
    description: "包含图像质量评分模块",
    featured: true
  },
  {
    name: "表情识别",
    extensionId: 'logicleapExpress',
    iconURL: expressIconURL,
    insetIconURL: expressInsetIconURL,
    description: "支持9种表情类型识别",
    featured: true
  },
  {
    name: "人脸识别",
    extensionId: 'faceRecognition',
    iconURL: faceRecognitionIconURL,
    insetIconURL: faceRecognitionInsetIconURL,
    description: "包含人脸识别模块",
    featured: true
  },
  {
    name: "人脸对比",
    extensionId: 'faceOneContrastOne',
    iconURL: faceOneContrastOneIconURL,
    insetIconURL: faceOneContrastOneInsetIconURL,
    description: "包含人脸对比模块",
    featured: true
  },
  {
    name: "图像训练",
    extensionId: 'logicleapImageTrain',
    iconURL: imageTrainIconURL,
    insetIconURL: imageTrainInsetIconURL,
    description: "包含图像训练模块",
    featured: true
  },
  {
    name: "姿态训练",
    extensionId: 'logicleapPoseTrain',
    iconURL: poseTrainIconURL,
    insetIconURL: poseTrainInsetIconURL,
    description: "包含姿态训练模块",
    featured: true
  },
  {
    name: "声音训练",
    extensionId: 'logicleapSoundTrain',
    iconURL: soundTrainIconURL,
    insetIconURL: soundTrainInsetIconURL,
    description: "包含声音训练模块",
    featured: true
  },
  {
    name: "摄像头模块",
    extensionId: 'camera',
    iconURL: cameraIconURL,
    insetIconURL: cameraInsetIconURL,
    description: "包含摄像头操作模块",
    featured: true
  },
  {
    name: "分类训练",
    extensionId: 'train',
    iconURL: trainIconURL,
    insetIconURL: trainInsetIconURL,
    description: "包含分类训练模块",
    featured: true
  },
  {
    name: "列表",
    extensionId: 'list',
    iconURL: listIconURL,
    insetIconURL: listInsetIconURL,
    description: "包含列表操作模块",
    featured: true
  },
  {
    name: (
      <FormattedMessage
        defaultMessage="Music"
        description="Name for the 'Music' extension"
        id="gui.extension.music.name"
      />
    ),
    extensionId: 'music',
    iconURL: musicIconURL,
    insetIconURL: musicInsetIconURL,
    description: (
      <FormattedMessage
        defaultMessage="Play instruments and drums."
        description="Description for the 'Music' extension"
        id="gui.extension.music.description"
      />
    ),
    featured: true
  },
  {
    name: (
      <FormattedMessage
        defaultMessage="Pen"
        description="Name for the 'Pen' extension"
        id="gui.extension.pen.name"
      />
    ),
    extensionId: 'pen',
    iconURL: penIconURL,
    insetIconURL: penInsetIconURL,
    description: (
      <FormattedMessage
        defaultMessage="Draw with your sprites."
        description="Description for the 'Pen' extension"
        id="gui.extension.pen.description"
      />
    ),
    featured: true
  },
  // {
  //     name: (
  //         <FormattedMessage
  //             defaultMessage="Video Sensing"
  //             description="Name for the 'Video Sensing' extension"
  //             id="gui.extension.videosensing.name"
  //         />
  //     ),
  //     extensionId: 'videoSensing',
  //     iconURL: videoSensingIconURL,
  //     insetIconURL: videoSensingInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Sense motion with the camera."
  //             description="Description for the 'Video Sensing' extension"
  //             id="gui.extension.videosensing.description"
  //         />
  //     ),
  //     featured: true
  // },
  // {
  //     name: (
  //         <FormattedMessage
  //             defaultMessage="Text to Speech"
  //             description="Name for the Text to Speech extension"
  //             id="gui.extension.text2speech.name"
  //         />
  //     ),
  //     extensionId: 'text2speech',
  //     collaborator: 'Amazon Web Services',
  //     iconURL: text2speechIconURL,
  //     insetIconURL: text2speechInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Make your projects talk."
  //             description="Description for the Text to speech extension"
  //             id="gui.extension.text2speech.description"
  //         />
  //     ),
  //     featured: true,
  //     internetConnectionRequired: true
  // },
  // {
  //     name: (
  //         <FormattedMessage
  //             defaultMessage="Translate"
  //             description="Name for the Translate extension"
  //             id="gui.extension.translate.name"
  //         />
  //     ),
  //     extensionId: 'translate',
  //     collaborator: 'Google',
  //     iconURL: translateIconURL,
  //     insetIconURL: translateInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Translate text into many languages."
  //             description="Description for the Translate extension"
  //             id="gui.extension.translate.description"
  //         />
  //     ),
  //     featured: true,
  //     internetConnectionRequired: true
  // },
  // {
  //     name: 'Makey Makey',
  //     extensionId: 'makeymakey',
  //     collaborator: 'JoyLabz',
  //     iconURL: makeymakeyIconURL,
  //     insetIconURL: makeymakeyInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Make anything into a key."
  //             description="Description for the 'Makey Makey' extension"
  //             id="gui.extension.makeymakey.description"
  //         />
  //     ),
  //     featured: true
  // },
  // {
  //     name: 'micro:bit',
  //     extensionId: 'microbit',
  //     collaborator: 'micro:bit',
  //     iconURL: microbitIconURL,
  //     insetIconURL: microbitInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Connect your projects with the world."
  //             description="Description for the 'micro:bit' extension"
  //             id="gui.extension.microbit.description"
  //         />
  //     ),
  //     featured: true,
  //     disabled: false,
  //     bluetoothRequired: true,
  //     internetConnectionRequired: true,
  //     launchPeripheralConnectionFlow: true,
  //     useAutoScan: false,
  //     connectionIconURL: microbitConnectionIconURL,
  //     connectionSmallIconURL: microbitConnectionSmallIconURL,
  //     connectingMessage: (
  //         <FormattedMessage
  //             defaultMessage="Connecting"
  //             description="Message to help people connect to their micro:bit."
  //             id="gui.extension.microbit.connectingMessage"
  //         />
  //     ),
  //     helpLink: 'https://scratch.mit.edu/microbit'
  // },
  // {
  //     name: 'LEGO MINDSTORMS EV3',
  //     extensionId: 'ev3',
  //     collaborator: 'LEGO',
  //     iconURL: ev3IconURL,
  //     insetIconURL: ev3InsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Build interactive robots and more."
  //             description="Description for the 'LEGO MINDSTORMS EV3' extension"
  //             id="gui.extension.ev3.description"
  //         />
  //     ),
  //     featured: true,
  //     disabled: false,
  //     bluetoothRequired: true,
  //     internetConnectionRequired: true,
  //     launchPeripheralConnectionFlow: true,
  //     useAutoScan: false,
  //     connectionIconURL: ev3ConnectionIconURL,
  //     connectionSmallIconURL: ev3ConnectionSmallIconURL,
  //     connectingMessage: (
  //         <FormattedMessage
  //             defaultMessage="Connecting. Make sure the pin on your EV3 is set to 1234."
  //             description="Message to help people connect to their EV3. Must note the PIN should be 1234."
  //             id="gui.extension.ev3.connectingMessage"
  //         />
  //     ),
  //     helpLink: 'https://scratch.mit.edu/ev3'
  // },
  // {
  //     name: 'LEGO BOOST',
  //     extensionId: 'boost',
  //     collaborator: 'LEGO',
  //     iconURL: boostIconURL,
  //     insetIconURL: boostInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Bring robotic creations to life."
  //             description="Description for the 'LEGO BOOST' extension"
  //             id="gui.extension.boost.description"
  //         />
  //     ),
  //     featured: true,
  //     disabled: false,
  //     bluetoothRequired: true,
  //     internetConnectionRequired: true,
  //     launchPeripheralConnectionFlow: true,
  //     useAutoScan: true,
  //     connectionIconURL: boostConnectionIconURL,
  //     connectionSmallIconURL: boostConnectionSmallIconURL,
  //     connectionTipIconURL: boostConnectionTipIconURL,
  //     connectingMessage: (
  //         <FormattedMessage
  //             defaultMessage="Connecting"
  //             description="Message to help people connect to their BOOST."
  //             id="gui.extension.boost.connectingMessage"
  //         />
  //     ),
  //     helpLink: 'https://scratch.mit.edu/boost'
  // },
  // {
  //     name: 'LEGO Education WeDo 2.0',
  //     extensionId: 'wedo2',
  //     collaborator: 'LEGO',
  //     iconURL: wedo2IconURL,
  //     insetIconURL: wedo2InsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Build with motors and sensors."
  //             description="Description for the 'LEGO WeDo 2.0' extension"
  //             id="gui.extension.wedo2.description"
  //         />
  //     ),
  //     featured: true,
  //     disabled: false,
  //     bluetoothRequired: true,
  //     internetConnectionRequired: true,
  //     launchPeripheralConnectionFlow: true,
  //     useAutoScan: true,
  //     connectionIconURL: wedo2ConnectionIconURL,
  //     connectionSmallIconURL: wedo2ConnectionSmallIconURL,
  //     connectionTipIconURL: wedo2ConnectionTipIconURL,
  //     connectingMessage: (
  //         <FormattedMessage
  //             defaultMessage="Connecting"
  //             description="Message to help people connect to their WeDo."
  //             id="gui.extension.wedo2.connectingMessage"
  //         />
  //     ),
  //     helpLink: 'https://scratch.mit.edu/wedo'
  // },
  // {
  //     name: 'Go Direct Force & Acceleration',
  //     extensionId: 'gdxfor',
  //     collaborator: 'Vernier',
  //     iconURL: gdxforIconURL,
  //     insetIconURL: gdxforInsetIconURL,
  //     description: (
  //         <FormattedMessage
  //             defaultMessage="Sense push, pull, motion, and spin."
  //             description="Description for the Vernier Go Direct Force and Acceleration sensor extension"
  //             id="gui.extension.gdxfor.description"
  //         />
  //     ),
  //     featured: true,
  //     disabled: false,
  //     bluetoothRequired: true,
  //     internetConnectionRequired: true,
  //     launchPeripheralConnectionFlow: true,
  //     useAutoScan: false,
  //     connectionIconURL: gdxforConnectionIconURL,
  //     connectionSmallIconURL: gdxforConnectionSmallIconURL,
  //     connectingMessage: (
  //         <FormattedMessage
  //             defaultMessage="Connecting"
  //             description="Message to help people connect to their force and acceleration sensor."
  //             id="gui.extension.gdxfor.connectingMessage"
  //         />
  //     ),
  //     helpLink: 'https://scratch.mit.edu/vernier'
  // },

];


