/** @jsxImportSource react */
'use client'

import { useState, useEffect } from 'react'
import { Modal, Avatar, Button, Spin } from 'antd'
import { CheckOutlined } from '@ant-design/icons'
import userApi from '@/lib/api/user'
import { useDispatch } from 'react-redux'
import { setUser } from '@/lib/store'
import { GetNotification } from 'logic-common/dist/components/Notification';

interface RoleSwitcherModalProps {
  isOpen: boolean
  onClose: () => void
  currentUser: any
}

export default function RoleSwitcherModal({ isOpen, onClose, currentUser }: RoleSwitcherModalProps) {
  const [roles, setRoles] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const dispatch = useDispatch()
  const [currentUserId, setCurrentUserId] = useState<string | number | undefined>(undefined)
  const notification = GetNotification();
  // 获取当前用户的所有角色
  useEffect(() => {
    if (isOpen && currentUser?.phone) {
      // 尝试从localStorage获取当前用户ID
      const storedUserId = localStorage.getItem('userId')
      if (storedUserId) {
        setCurrentUserId(storedUserId)
        console.log('从localStorage获取的用户ID:', storedUserId)
      } else if (currentUser?.userId) {
        setCurrentUserId(currentUser.userId)
        console.log('从currentUser.userId获取的用户ID:', currentUser.userId)
      } else if (currentUser?.id) {
        setCurrentUserId(currentUser.id)
        console.log('从currentUser.id获取的用户ID:', currentUser.id)
      }

      fetchUserRoles()
    }
  }, [isOpen, currentUser?.phone])

  // 监听roles变化的调试日志
  useEffect(() => {
    if (roles.length > 0) {
      console.log('roles更新后:', roles)
      console.log('当前用户ID:', currentUserId, '类型:', typeof currentUserId)
      console.log('过滤后的角色:', roles.filter(role => Number(role.id) !== Number(currentUserId)))
    }
  }, [roles, currentUserId])

  const fetchUserRoles = async () => {
    try {
      setLoading(true)
      const response = await userApi.findAllBindByPhone(currentUser.phone)
      console.log('获取用户角色响应:', response)

      // 处理不同的响应格式
      if (response.data?.code === 200) {
        // 直接使用data字段
        if (Array.isArray(response.data.data)) {
          setRoles(response.data.data)
        }
        // 使用data.list字段
        else if (response.data.data?.list && Array.isArray(response.data.data.list)) {
          setRoles(response.data.data.list)
        }
        // 直接使用响应数据
        else if (Array.isArray(response.data)) {
          setRoles(response.data)
        }

        // 添加调试日志
        console.log('当前用户ID:', currentUserId, '类型:', typeof currentUserId)
        console.log('所有角色:', roles)
      }
    } catch (error) {
      console.error('获取用户角色失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 切换角色
  const handleRoleSwitch = async (userId: number) => {
    try {
      setLoading(true)
      const response = await userApi.switchToUser(userId)
      console.log('账号切换响应:', response)

      // 处理响应结构
      if (response.code === 200) {
        // 获取用户数据和token
        const userData = response.data?.userInfo || response.userInfo
        const token = response.data?.token || response.token

        if (userData && token) {
          localStorage.setItem('token', token)
          localStorage.setItem('userId', userData.id)
          localStorage.setItem('user', JSON.stringify(userData))

          notification.success('账号切换成功,即将刷新网页')

          // 更新Redux状态
          dispatch(setUser({
            userId: userData.id,
            nickName: userData.nickName,
            avatarUrl: userData.avatarUrl,
            gender: userData.gender,
            phone: userData.phone,
            isLoggedIn: true,
            roleId: userData.roleId
          }))

          // 延迟刷新页面
          window.location.reload();
          onClose()

          // 刷新页面以应用新角色权限
        } else {
          notification.error('账号切换成功，但未获取到用户信息')
        }
      } else {
        const errorMsg = response.message || '账号切换失败'
        notification.error(errorMsg)
      }
    } catch (error) {
      console.error('账号切换失败:', error)
      notification.error('账号切换失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Modal
      title="选择账号"
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
    >
      {loading ? (
        <div className="py-10 flex justify-center">
          <Spin size="large" />
        </div>
      ) : (
        <div className="py-2">
          {/* 当前账号 */}
          <div className="mb-4">
            <div className="text-sm text-gray-500 mb-2">当前账号</div>
            <div className="flex items-center p-3 bg-blue-50 rounded-lg">
              <Avatar
                src={currentUser?.avatarUrl}
                size={40}
                className="mr-3"
              >
                {currentUser?.nickName?.charAt(0) || 'U'}
              </Avatar>
              <div className="flex-1">
                <div className="text-base font-medium">{currentUser?.nickName}</div>
                <div className="text-sm text-gray-500">
                  {roles.find(r => Number(r.id) === Number(currentUserId))?.role?.name || '当前账号'}
                </div>
              </div>
              <CheckOutlined className="text-blue-500" />
            </div>
          </div>

          {/* 可选账号 */}
          {roles.filter(role => Number(role.id) !== Number(currentUserId)).length > 0 ? (
            <div>
              <div className="text-sm text-gray-500 mb-2">可选账号</div>
              {roles.filter(role => {
                console.log('比较:', role.id, currentUserId, Number(role.id) !== Number(currentUserId))
                return Number(role.id) !== Number(currentUserId)
              }).map(role => (
                <div
                  key={role.id}
                  onClick={() => handleRoleSwitch(role.id)}
                  className="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors mb-2 border border-gray-100"
                >
                  <Avatar
                    src={role.avatarUrl}
                    size={40}
                    className="mr-3"
                  >
                    {role.nickName?.charAt(0) || 'U'}
                  </Avatar>
                  <div className="flex-1">
                    <div className="text-base font-medium">{role.nickName}</div>
                    <div className="text-sm text-gray-500">{role.role?.name || '未知角色'}</div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              没有其他可切换的账号
            </div>
          )}

          <div className="mt-6 flex justify-end">
            <Button onClick={onClose}>
              取消
            </Button>
          </div>
        </div>
      )}
    </Modal>
  )
} 